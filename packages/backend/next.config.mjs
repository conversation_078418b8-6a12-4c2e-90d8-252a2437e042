/** @type {import('next').NextConfig} */
console.log('NEXT_PUBLIC_ENV', process.env.NEXT_PUBLIC_ENV);
console.log('NODE_ENV', process.env.NODE_ENV);

import { withSentryConfig } from "@sentry/nextjs";

const nextConfig = withSentryConfig({
  webpack: (config) => {
    // See https://webpack.js.org/configuration/resolve/#resolvealias
    config.resolve.alias = {
        ...config.resolve.alias,
        // "sharp$": false,
        "onnxruntime-node$": false,
    }
    return config;
  },
  images: {
      remotePatterns: [
        {
          protocol: 'https',
          hostname: 'sgp-ai-data-1323765209.cos.ap-singapore.myqcloud.com',
          port: '',
        },
        {
          protocol: 'https',
          hostname: 'tr-avatar-1323765209.cos.ap-singapore.myqcloud.com',
          port: '',
        },
        {
          protocol: 'https',
          hostname: 'ai-data.424224.xyz',
          port: '',
          search: '',
        },
        {
          protocol: 'https',
          hostname: 'static.image.424224.xyz',
          port: '',
          search: '',
        }
      ],
      unoptimized: process.env.NEXT_PUBLIC_ENV === 'dev'? true : false,
  },
}, {
  org: "sentry",
  project: "javascript-nextjs",
  // sentryUrl: 'http://sentry.198432.xyz:8080',
  // An auth token is required for uploading source maps.
  // authToken: '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',

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  // widenClientFileUpload: true,

  // Uncomment to route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
  // This can increase your server load as well as your hosting bill.
  // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
  // side errors will fail.
  // tunnelRoute: "/monitoring",

  // Hides source maps from generated client bundles
  // hideSourceMaps: true,

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  // disableLogger: true,

  // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
  // See the following for more information:
  // https://docs.sentry.io/product/crons/
  // https://vercel.com/docs/cron-jobs
  // automaticVercelMonitors: true,
  // silent: false, // Can be used to suppress logs
});

export default nextConfig;
