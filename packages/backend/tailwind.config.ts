import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "../shared/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    typography: require('./typography'),
    extend: {
      
    },
  },
  plugins: [
    require('@tailwindcss/typography')
  ],
  darkMode: 'class'
};
export default config;
