'use client'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import s from './style.module.css'
import cn from 'classnames'
import { useTranslation } from 'react-i18next'
import { useParams } from 'next/navigation'
import useRequest from '@backend/app/[lang]/hook/useRequest'
import { AuthContext } from '../components/authContext';
import Toast from '@little-tavern/shared/src/ui/toast';
import useComfirm from '@little-tavern/shared/src/hook/useComfirm';
import { useForm } from 'react-hook-form';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  rectSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import Wdthdraw from './withdraw'

export type IOperation = {
  id?: string,
  type: 'human' | 'ai' | 'img',
  avatar?: string,
  message_id?: string,
  version?: string,
  // 跟message_id一样，缓存判断是否重复请求
  imgId?: string,
  voice_url?: string,
  content: string,
  timestamp: number | null,
  isLoading?: boolean
}
const Operation = () => {
  const { t } = useTranslation()
  const params = useParams()
  const lang = params.lang as string
  const request = useRequest();
  const auth = useContext(AuthContext);
  const tagRef = useRef<any>(null)
  const catRef = useRef<any>(null)
  const tag1Ref = useRef<any>(null)
  const classifyRef = useRef<any>(null)
  const [tags, setTags] = useState<any>([])
  const [classify, setClassify] = useState<any>([]);
  const [newSubTags, setNewSubTags] = useState<any>([]);
  const confirm = useComfirm();

  const queryClassify = async () => {
    try {
      const res = await request(`/tag_orders`)
      res.length > 0 && setClassify(res)
    } catch (e) {
      console.log("e", e);
    }
  }
  const queryTags = async () => {
    try {
      const res = await request(`/sub_tag/category_list`)
      if (res.error_code === 0) {
        setTags(res.data.sub_tags)
        setNewSubTags(res.data.category_groups)
      } else {
        Toast.notify({
          type: 'error',
          message: res.message
        })
      }
    } catch (e) {
      console.log("e", e);
    }
  }
  useEffect(() => {
    queryTags();
    queryClassify();
  }, [])

  const addTag = async () => {
    try {
      const res = await request('/sub_tag/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          tag: tagRef.current.value,
        })
      })
      setTags(res)
      tagRef.current.value = ''
      Toast.notify({
        type: 'success',
        message: `添加成功`
      })
    } catch (e) {
      console.log("e", e);
    }
  }
  const addNewTag = async () => {
    try {
      const res = await request('/sub_tag/add_or_update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          tag: tag1Ref.current.value,
          category: catRef.current.value,
        })
      })
      if (res.error_code === 0) {
        setNewSubTags(res.data.category_groups)
        tag1Ref.current.value = ''
        catRef.current.value = ''
        Toast.notify({
          type: 'success',
          message: `添加成功`
        })
        queryTags()
      } else {
        Toast.notify({
          type: 'error',
          message: res.message
        })
      }
    } catch (e) {
      console.log("e", e);
    }
  }

  const updateClassify = async (classify: any) => {
    try {
      const res = await request('/save_tag_orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(classify)
      })
      setClassify(classify)
      Toast.notify({
        type: 'success',
        message: `更新成功`
      })
    } catch (e) {
      console.log("e", e);
    }
  }

  const addClassify = async () => {
    const newClassify = [...classify, classifyRef.current.value];
    setClassify(newClassify)
    updateClassify(newClassify);
    classifyRef.current.value = ''
    Toast.notify({
      type: 'success',
      message: `添加成功`
    })
  }

  const delClassify = async (tag: string) => {
    const isConfirm = await confirm.show({
      title: `是否要删除：${tag}`,
      desc: `若删除标签，所有角色卡包含该标签的一起删除`
    });
    if (isConfirm.confirm) {
      try {
        let newClassify = [...classify];
        let pos = newClassify.indexOf(tag);
        if (pos != -1) {
          newClassify.splice(pos, 1)
        }
        updateClassify(newClassify)
        Toast.notify({
          type: 'success',
          message: `删除成功`
        })
      } catch (e) {
        console.log("e", e);
      }
    }
  }

  const delTag = async (tag: string) => {
    const isConfirm = await confirm.show({
      title: `是否要删除：${tag}`,
      desc: `若删除标签，所有角色卡包含该标签的一起删除`
    });
    if (isConfirm.confirm) {
      try {
        const res = await request('/sub_tag/delete', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            tag: tag,
          })
        })
        setTags(res)
        Toast.notify({
          type: 'success',
          message: `删除成功`
        })
      } catch (e) {
        console.log("e", e);
      }
    }
  }

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: any) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      const oldIndex = classify.indexOf(active.id);
      const newIndex = classify.indexOf(over.id);
      const newClassify = arrayMove(classify, oldIndex, newIndex);
      setClassify(newClassify);
      updateClassify(newClassify);
    }
  };

  const handleTagsDragEnd = async (event: any) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      const oldIndex = tags.indexOf(active.id);
      const newIndex = tags.indexOf(over.id);
      const newTags = arrayMove(tags, oldIndex, newIndex);
      setTags(newTags);
      try {
        const res = await request('/sub_tag/update_order', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            "sub_tag": active.id,
            "order": newIndex
          })
        })
        setTags(res.data.sub_tags)
        console.log('res', res, active, over);
        Toast.notify({
          type: 'success',
          message: `更新成功`
        })
      } catch (e) {
        console.log("e", e);
        Toast.notify({
          type: 'error',
          message: `更新异常`
        })
      }
    }
  };

  const SortableTag = ({ tag, onDelete }: { tag: string; onDelete: (tag: string) => void }) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
    } = useSortable({ id: tag });

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
    };

    return (
      <li
        ref={setNodeRef}
        style={style}
        className='flex justify-between mx-1 mb-2 whitespace-nowrap border border-gray-400 px-2 py-1 rounded text-sm gap-1 touch-none'
      >
        <div
          {...attributes}
          {...listeners}
          className='flex-1 text-gray-400 text-ellipsis overflow-hidden cursor-move'
          title={tag}
        >
          {tag}
        </div>
        <button
          onClick={(e) => {
            e.stopPropagation();
            onDelete(tag);
          }}
          type='button'
          className='px-1 text-xs text-gray-400 hover:text-gray-100'
        >
          x
        </button>
      </li>
    );
  };

  return (
    <>
      <main className="main-height bg-black con-width">
        <div>
          <Wdthdraw />
          <form className="" action="#" method="POST" onSubmit={() => { }}>
            <div className='overflow-auto space-y-6 mb-4 px-8 pt-4 pb-1'>
              <h2 className='text-xl'>标签管理</h2>
              <div className='sm:w-full'>
                <label htmlFor="name" className="block text-sm font-medium leading-6 ">
                  标签列表（拖拽排序）
                </label>
                <div className="mt-2 cursor-move">
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={handleTagsDragEnd}
                  >
                    <SortableContext
                      items={tags}
                      strategy={rectSortingStrategy}
                    >
                      <ul className='flex gap-2 flex-wrap'>
                        {tags.map((tag: string) => (
                          <SortableTag
                            key={tag}
                            tag={tag}
                            onDelete={delTag}
                          />
                        ))}
                      </ul>
                    </SortableContext>
                  </DndContext>
                </div>
              </div>
              <div className='sm:w-full'>
                <label htmlFor="name" className="block text-sm font-medium leading-6 ">
                  标签新分类
                </label>
                <div className="mt-2">
                  {newSubTags?.map((tag: any) => {
                    return <div key={tag.category} className='mt-2 text-sm'>
                      <div>{tag.category}</div>
                      <div className='flex gap-2 gap-y-1 mt-0.5 flex-wrap'>
                        {
                          tag?.sub_tags?.map((sub_tag: any) => {
                            return <label className='mr-2 text-gray-400' key={sub_tag}>{sub_tag}</label>
                          })
                        }
                      </div>
                    </div>
                  })}
                </div>
              </div>
              {/* <div className='sm:w-full sm:max-w-sm'>
                <label htmlFor="tags" className="block text-sm font-medium leading-6 ">
                  添加标签
                </label>
                <div className="mt-2">
                  <input
                    type="text" ref={tagRef}
                    id="tags"
                    autoComplete="tags"
                    required
                    placeholder='请输入标签'
                    className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3"
                  />
                  <button onClick={addTag} type='button' className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>添加</button>
                </div>
              </div> */}
              <div className='sm:w-full'>
                <label htmlFor="tags" className="block text-sm font-medium leading-6 ">
                  按分类添加标签
                </label>
                <div className="mt-2 space-x-2">
                  <input
                    type="text" ref={catRef}
                    id="cats"
                    autoComplete="cats"
                    required
                    placeholder='请输入分类'
                    className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3"
                  />
                  <input
                    type="text" ref={tag1Ref}
                    id="tags1"
                    autoComplete="tags1"
                    required
                    placeholder='请输入标签'
                    className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3"
                  />
                  <button onClick={addNewTag} type='button' className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>添加</button>
                </div>
              </div>
            </div>
          </form>
          <form className="" action="#" method="POST" onSubmit={() => { }}>
            <div className='max-h-[75vh] overflow-auto space-y-6 mb-4 px-8 pt-4 pb-1'>
              <h2 className='text-xl'>分类管理</h2>
              <div className='sm:w-full'>
                <label htmlFor="name" className="block text-sm font-medium leading-6 ">
                  分类列表（拖拽排序）
                </label>
                <div className="mt-2 cursor-move">
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEnd}
                  >
                    <SortableContext
                      items={classify}
                      strategy={rectSortingStrategy}
                    >
                      <div className="grid grid-cols-7 gap-2">
                        {classify.map((tag: any) => (
                          <SortableTag
                            key={tag}
                            tag={tag}
                            onDelete={delClassify}
                          />
                        ))}
                      </div>
                    </SortableContext>
                  </DndContext>
                </div>
              </div>
              <div className='sm:w-full'>
                <div className="mt-2 flex gap-3">
                  <input
                    type="text" ref={classifyRef}
                    id="classifyRef"
                    autoComplete="classifyRef"
                    required
                    placeholder='添加分类'
                    className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3"
                  />
                  <button onClick={addClassify} type='button' className='p-2 px-5 bg-purple-500 text-white rounded text-sm'>添加</button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </main>
    </>
  )
}

export default Operation
