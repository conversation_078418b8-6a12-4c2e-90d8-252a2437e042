
'use client'
import { useTranslation } from 'react-i18next'
import Link from 'next/link'
import { useParams, useSelectedLayoutSegment } from 'next/navigation'
import classNames from 'classnames'
import React, { useRef } from 'react'
import type { FC, FormEvent } from 'react'
import useRequest from '@backend/app/[lang]/hook/useRequest'
import Toast from '@little-tavern/shared/src/ui/toast';
import useComfirm from '@little-tavern/shared/src/hook/useComfirm';

type withdrawProps = {}

const Withdraw = ({}: withdrawProps) => {
  const alipayIdRef = useRef<any>(null);
  const withdrawAmountRef = useRef<any>(null);
  const widthDrawNameRef = useRef<any>(null);
  const { t } = useTranslation()
  const params = useParams()
  const lang = params.lang as string
  const request = useRequest();
  const confirm = useComfirm();
  
  const handleClick= async (e: FormEvent) => {
    e.preventDefault();
    const accountNo = alipayIdRef?.current.value;
    const fee = parseInt(withdrawAmountRef?.current.value, 10)
    const name = widthDrawNameRef?.current.value
    const isConfirm = await confirm.show({
      title: '是否要提现',
      desc:  `
      提现金额：${fee}
      提现给用户：${name}
      提现账号：${accountNo}
      `
    });
    if (isConfirm) {
      try {
        const res = await request('/cc_recharge/withdraw', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            account_no: accountNo,
            fee: fee,
            name: name,
            user_id: 0
          })
        })
        Toast.notify({
          type:'success',
          message: `提现成功`
        })
        alipayIdRef.current.value = ''
        withdrawAmountRef.current.value = ''
        widthDrawNameRef.current.value = ''
      } catch(e) {
        console.log("e", e);
      }
    }
  }
  return (
    <form className="" action="#" method="POST" onSubmit={handleClick}>
        <div className='max-h-[75vh] overflow-auto space-y-6 mb-4 px-8 pt-4 pb-1'>
            <h2 className='text-xl'>支付宝提现</h2>
            <div className=''>
            <label htmlFor="alipayId" className="block text-sm font-medium leading-6 ">
            提现到支付宝账号
            </label>
            <div className="mt-2 space-x-1 space-y-1">
                <input
                type="text" ref={alipayIdRef}
                id="alipayId"
                autoComplete="alipayId"
                required
                placeholder='请输入要提现的支付宝账号'
                className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 w-[200px]"
                />
                <input
                type="text" ref={withdrawAmountRef}
                id="withdrawAmount"
                autoComplete="withdrawAmount"
                required
                placeholder='请输入要提现的金额'
                className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3"
                />
                <input
                type="text" ref={widthDrawNameRef}
                id="widthDrawName"
                autoComplete="widthDrawName"
                required
                placeholder='请输入要提现的实名'
                className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3"
                />
                <button type='submit' className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>提现</button>
            </div>
            </div>
        </div>
        </form>
  )
}

export default Withdraw




