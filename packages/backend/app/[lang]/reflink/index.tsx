'use client'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useParams, useRouter } from 'next/navigation'
import s from '@backend/app/[lang]/globals.module.css'
import Loader from '@little-tavern/shared/src/ui/Loading'
import { AuthContext } from '../components/authContext'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import dynamic from 'next/dynamic'
import Toast from '@little-tavern/shared/src/ui/toast'

const btn = cn(s.primBtn, 'mx-2 text-sm')

const Pay = () => {
  const { t } = useTranslation()
  const params = useParams()
  const lang = params.lang as string
  const auth = useContext(AuthContext);
  const isLogin = auth?.isLogin;
  const request = useRequest();
  const channelRef = useRef<any>(null)
  const [channelId, setChannelId] = useState<any>(null)
  const [adLink, setAdlink] = useState(null)
  const [allLinks, setAllLinks] = useState<any>({})
  const allLinksArr = Object.keys(allLinks)
  const getChannelId = async () => {
    const res = await request('/links_by_channel', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        tg_link: channelRef?.current?.value
      })
    });
    setChannelId(res.links)
    console.log('res', res)
  }
  const generateLink = async (e: React.FormEvent) => {
    e.preventDefault()
    const formData = new FormData(e.target as HTMLFormElement);
    const data = {
      tg_link: formData.get('link'),
      role_id: Number(formData.get('roleId')),
      bot_type: formData.get('appType'),
    };
    const res = await request('/generate_link', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });

    setAdlink(res.url)
  }

  useEffect(() => {
    getAllLinks()
  }, [])

  const getAllLinks = async () => {
    const res = await request('/all_links');
    setAllLinks(res);
  }
  const copy = async (link: string) => {
    try {
      await navigator.clipboard.writeText(link);
      console.log('Content copied to clipboard');
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  }
  const GenerateList = ({ linkKey, index, lists }: any) => {
    return <div key={index} className='mt-6'>渠道链接：{linkKey}
      <table className='dark:bg-gray-900 bg-white text-gray-500 text-sm border-collapse w-[920px] mt-2'>
        <thead className='dark:bg-slate-700 bg-slate-300 dark:text-slate-300'>
          <tr className='text-center text-xs sm:text-sm'>
            <th className='border py-2 px-1 border-slate-600'>渠道id</th>
            <th className='border py-2 px-4 border-slate-600'>角色id</th>
            <th className='border py-2 px-4 border-slate-600'>类型</th>
            <th className='border py-2 px-4 border-slate-600'>推广链接</th>
            <th className='border py-2 px-3 border-slate-600'>操作</th>
          </tr>
        </thead>
        <tbody>

          {lists.map((list: any, index: number) => {
            return <tr key={index} className='ml-3 my-1 border-1 text-gray-400'>
              <td className='border border-slate-700 py-1 px-1'>{list.channel_id}</td>
              <td className='border border-slate-700 py-1 px-1'>{list.role_id}</td>
              <td className='border border-slate-700 py-1 px-1'>{list.target_type}</td>
              <td className='border border-slate-700 py-1 px-1'>{list.target_link}</td>
              <td className='border border-slate-700 py-1 px-1'><button type='button' className='p-1 px-3 bg-purple-500 text-white rounded text-sm ml-3' onClick={() => { copy(list.target_link) }}>复制推广链接</button></td>
            </tr>
          })}
        </tbody>
      </table>
    </div>
  }

  return (
    <>
      {<main className="main-height bg-black w-full con-width px-3 pt-6">
        <h2 className='text-xl mt-5'>运营推广</h2>
        <div className='mt-3'></div>
        <h3 className='mb-2'>查询渠道号</h3>
        <div className='mb-5'>
          <div className="mt-2">
            <input
              type="text" ref={channelRef}
              id="channel"
              autoComplete="channel"
              required
              placeholder='请输入tg频道或者群的链接'
              className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 w-72"
            />
            <button onClick={getChannelId} type='button' className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>查询</button>
            {channelId && <GenerateList linkKey={channelRef?.current?.value} index={0} lists={channelId} />}
          </div>
        </div>
        <h3 className='mb-2'>生成推广链接</h3>
        <div className='mb-5'>
          <form className="mt-2" onSubmit={generateLink}>
            <input
              type="number"
              id="roleId"
              name="roleId"
              autoComplete="roleId"
              required
              placeholder='请输入角色ID'
              className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 w-32 mr-2"
            />
            <input
              type="text"
              id="link"
              name='link'
              autoComplete="link"
              required
              placeholder='请输入tg频道或者群的链接'
              className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 w-72 mr-2"
            />
            <select name="appType" className='w-24 inline-block bg-gray-800 p-1 mr-2'>
              <option className='w-24 p-1' value={'TMA'}>TMA</option>
              <option className='w-24 p-1' value={'CHAT_BOT'}>BOT</option>
            </select>
            <button type='submit' className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>生成链接</button>
            {adLink && <div className='mt-3'>
              推广链接：{adLink}
            </div>}
          </form>
        </div>
        <h3 className='mb-2'>渠道号列表</h3>
        <div>
          {
            allLinksArr.map((linkKey: string, index: number) => {
              const lists = allLinks[linkKey]
              return <GenerateList key={index} linkKey={linkKey} index={index} lists={lists} />
            })
          }
        </div>
      </main>}
    </>
  )
}

export default React.memo(Pay)
