import type { Metadata } from "next";
import { APP_INFO } from '@backend/config'
import Header from "./components/header";
import I18N from "@backend/app/[lang]/i18n";
import { switchLanguages } from '@little-tavern/shared/src/i18n/settings'
import { ThemeProvider } from 'next-themes'
import { AuthProvider } from "./components/authContext";
import type { Viewport } from 'next'
import "./globals.css";
import './markdown.scss'
import { ConfigProvider } from "./configContext";


export const metadata: Metadata = {
  title: `${APP_INFO.title}`,
  description: `${APP_INFO.description}`,
}

export async function generateStaticParams() {
  return switchLanguages.map(lang => ({ lang }))
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
}

export default function RootLayout({
  children,
  params: {
    lang,
  },
}: Readonly<{
  children: React.ReactNode
  params: any
}>) {
  return (
    <html lang={lang} suppressHydrationWarning>
      <body>
        <I18N lang={lang}>
          <ThemeProvider attribute="class" defaultTheme='dark'>
            <ConfigProvider config={{}}>
              <AuthProvider>
                <Header title={APP_INFO.title} />
                <div className='mt-[57px]'>
                  {children}
                </div>
              </AuthProvider>
            </ConfigProvider>
          </ThemeProvider>
        </I18N>
      </body>
    </html>
  );
}
