import format from '@little-tavern/shared/src/module/formate-date'
import EditPayChannel from './editPayChannel';
import { useEffect, useState } from 'react';
import Toast from '@share/src/ui/toast';
import useRequest from '../hook/useRequest'

const PayChannel = () => {
  const [showEditPayChannel, setShowEditPayChannel] = useState(false)
  const request = useRequest();
  const [list, setList] = useState<any>([])
  const [payChannels, setPayChannels] = useState<any>([])
  const [rechargeProducts, setRechargeProducts] = useState<any>([])

  const reflesh = async () => {
    getPayChannelList()
  }
  const getPayChannelList = async () => {
    try {
      const res = await request(`/recharge_configs`);
      if (res.error_code === 0) {
        setList(res.data.recharge_configs)
        setPayChannels(res.data.channels)
      } else {
        Toast.notify({
          type: 'error',
          message: res.message
        })
      }
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: '获取支付渠道失败'
      })
    } finally {
      Toast.hideLoading()
    }
  }
  useEffect(() => {
    getPayChannelList()
  }, [])
  return <div className='mt-2'>
    {/* <button onClick={createAnouncement} type='button' className='btn'>新增</button> */}
    <table className='dark:bg-gray-900 bg-white text-gray-500 text-sm border-collapse mt-2'>
      <thead className='dark:bg-slate-700 bg-slate-300 dark:text-slate-300'>
        <tr className='text-center text-xs sm:text-sm'>
          <th className='border py-2 px-1 border-slate-600'>序号</th>
          <th className='border py-2 px-4 border-slate-600'>套餐</th>
          <th className='border py-2 px-4 border-slate-600'>套餐价格（元）</th>
          <th className='border py-2 px-4 border-slate-600'>支付渠道</th>
          <th className='border py-2 px-3 border-slate-600'>操作</th>
        </tr>
      </thead>
      <tbody>
        {list?.map((item: any, index: number) => {
          return <tr key={index} className='ml-3 my-1 border-1 text-gray-400'>
            <td className='border border-slate-700 py-1 px-1'>{index}</td>
            <td className='border border-slate-700 py-1 px-1'>{item?.recharge_product_name}</td>
            <td className='border border-slate-700 py-1 px-1'>{item?.cny_price}</td>
            <td className='border border-slate-700 py-1 px-1' >{item?.channel_list?.map((channel: any, idx: number) => {
              const channelName = Object.keys(channel)[0]
              const channelVal = channel[channelName];
              const channelStatus = payChannels[channelName]?.find((item: any) => {
                return channelVal === item.value
              })
              // console.log('channelStatus', channelStatus, channelStatus?.name || (channelVal === 'DEFAULT'? '默认' : '关闭'))
              return (
                <span key={idx}>
                  <span className="text-yellow-700">{channelName}</span>: <span className='text-green-300'>{channelStatus?.name || (channelVal === 'DEFAULT'? '默认' : '关闭')}</span>
                  {idx < item?.channel_list.length - 1 && ', '}
                </span>
              )
            })}</td>
            <td className='border border-slate-700 py-1 px-1'>
              <div className='flex flex-wrap gap-1'>
                <button type='button' className='p-1 px-3 bg-sky-500 text-white rounded text-sm' onClick={() => {
                  setRechargeProducts(item)
                  setShowEditPayChannel(true)
                }}>编辑</button>
              </div>
            </td>
          </tr>
        })}
      </tbody>
    </table>
    {showEditPayChannel && <EditPayChannel onClose={() => setShowEditPayChannel(false)} reflesh={reflesh} rechargeProducts={rechargeProducts} payChannels={payChannels} />}
  </div>
}

export default PayChannel