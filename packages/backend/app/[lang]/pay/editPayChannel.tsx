import Modal from "@share/src/ui/dialog/Modal"
import { useState, useEffect, useRef } from 'react'
import useRequest from "../hook/useRequest"
import Toast from "@share/src/ui/toast"
import { useForm } from 'react-hook-form'
import Link from "next/link"

type FormData = {
    recharge_product_id: string,
    channel_list: any[]
}

const EditPayChannel = ({ onClose, rechargeProducts, payChannels, reflesh }: any) => {
    const request = useRequest()
    const [loading, setLoading] = useState(false)
    const [testLinks, setTestLinks] = useState<{[key: number]: string}>({})
    console.log('testLinks', testLinks)
    const [generatingLink, setGeneratingLink] = useState<{[key: number]: boolean}>({})
    console.log('channels', rechargeProducts)
    const { register, handleSubmit, watch, formState: { errors } } = useForm<FormData>({
        defaultValues: {
            channel_list: rechargeProducts?.channel_list,
            recharge_product_id: rechargeProducts?.recharge_product_id
        }
    })
    const handleSave = async (data: FormData) => {
        setLoading(true)
        try {
            // Transform array of objects [{wechat: 'xxx'}, {alipay: 'xbb'}] to {wechat: 'xxx', alipay: 'xbb'}
            const transformedChannelList = data.channel_list.reduce((result: any, item: any) => {
                const key = Object.keys(item)[0];
                const value = item[key];
                return { ...result, [key]: value };
            }, {});
            
            const res = await request(`/recharge_config/edit`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    channels: transformedChannelList,
                    recharge_product_id: data.recharge_product_id
                })
            })
            if (res.error_code === 0) {
                Toast.notify({
                    type: 'success',
                    message: '更新成功'
                })
                onClose()
                reflesh()
            } else {
                Toast.notify({
                    type: 'error',
                    message: res.message
                })
            }
        } catch (e) {
            Toast.notify({
                type: 'error',
                message: '更新失败'
            })
        } finally {
            setLoading(false)
        }
    }
    const channel_list = watch('channel_list')
    // console.log('channel_list', channel_list)
    const generatLink = async (index: number) => {
        try {
            setGeneratingLink(prev => ({ ...prev, [index]: true }));
            const item = channel_list[index];
            const type = Object.keys(item)[0];
            const channel = item[type];
            
            const res = await request(`/recharge_config/generate_link`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    channel: channel,
                    type: type,
                    recharge_id: rechargeProducts.recharge_product_id
                })
            });
            
            if (res.error_code === 0 && res.data?.link) {
                setTestLinks(prev => ({ ...prev, [index]: res.data.link }));
                Toast.notify({
                    type: 'success',
                    message: '测试链接生成成功'
                });
            } else {
                Toast.notify({
                    type: 'error',
                    message: res.message || '生成测试链接失败'
                });
            }
        } catch (error) {
            Toast.notify({
                type: 'error',
                message: '生成测试链接失败'
            });
        } finally {
            setGeneratingLink(prev => ({ ...prev, [index]: false }));
        }
    }
    return (
        <Modal isOpen={true} onClose={onClose} className='!max-w-4xl'>
            <div className='mb-5'>
                <h3 className='mt-5 mb-2'>支付渠道配置</h3>
                <div className='space-y-3'>
                    <div className='space-y-2'>
                        {rechargeProducts.channel_list?.map((item: any, index: number) => {
                            return <div key={index} className='flex items-center'>
                                <span className="mr-2">{Object.keys(item)[0]}</span>
                                <select 
                                    className="bg-white text-gray-700" 
                                    {...register(`channel_list.${index}.${Object.keys(item)[0]}`)}
                                >
                                    {payChannels[Object.keys(item)[0]]? payChannels[Object.keys(item)[0]]?.map((channel: any) => {
                                        return <option key={channel.value} value={channel.value}>{channel.name}</option>
                                    }) : <>
                                        <option value="DEFAULT">默认</option>
                                        <option value="CLOSED">关闭</option>
                                    </>}
                                </select>
                                {payChannels[Object.keys(item)[0]] && <button 
                                    type="button" 
                                    className='p-1 px-3 bg-purple-500 text-white rounded text-sm ml-3' 
                                    onClick={() => generatLink(index)}
                                    disabled={generatingLink[index]}
                                >
                                    {generatingLink[index] ? '生成中...' : '生成测试地址'}
                                </button>}
                                {testLinks[index] && (
                                    <Link href={testLinks[index]} target="_blank" className="text-blue-500 ml-3 underline">
                                        {testLinks[index]}
                                    </Link>
                                )}
                            </div>
                        })}
                    </div>

                    <div className='px-2 py-2 flex flex-row-reverse'>
                        <button
                            type="button"
                            className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'
                            onClick={handleSubmit(handleSave)}
                            disabled={loading}
                        >
                            {loading ? '处理中...' : '更新'}
                        </button>
                        <button className='p-2 px-5 dark:bg-gray-800 bg-gray-300 rounded text-sm ' onClick={onClose}>取消</button>
                    </div>

                </div>
            </div>
        </Modal>
    )
}

export default EditPayChannel