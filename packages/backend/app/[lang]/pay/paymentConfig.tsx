import { useEffect, useState } from 'react';
import Toast from '@share/src/ui/toast';
import useRequest from '../hook/useRequest';

type PaymentConfigItem = {
  id: number;
  channel: string;
  pay_type: string;
  min_amount: number;
  max_amount: number;
  ratio: number;
  remark: string;
  enabled: boolean; // 添加是否启用字段
  isEditing?: boolean;
};

const PaymentConfig = () => {
  const request = useRequest();
  const [configList, setConfigList] = useState<PaymentConfigItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [newItem, setNewItem] = useState<PaymentConfigItem | null>(null);

  // 获取配置列表
  const getConfigList = async () => {
    setLoading(true);
    try {
      const res = await request(`/channel_control/list`);
      setConfigList(res.controls);
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: '获取支付配置失败'
      });
    } finally {
      setLoading(false);
    }
  };

  // 添加新项目
  const handleAddItem = () => {
    setNewItem({
      id: -1,
      channel: '',
      pay_type: '',
      min_amount: 0,
      max_amount: 0,
      ratio: 100,
      remark: '',
      enabled: true, // 默认启用
      isEditing: true
    });
  };

  // 保存项目
  const handleSaveItem = async (item: PaymentConfigItem) => {
    try {
      // 这里使用 mock 数据，实际项目中应该调用真实接口
      // const res = await request(`/payment_config/toggle/${id}`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ enabled: updatedItem.enabled })
      // });
      
      if (newItem && item.id === newItem.id) {
        const res = await request(`/channel_control/add`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(item)
        });

        setConfigList([...configList, { ...item, isEditing: false }]);
        setNewItem(null);
      } else {
        const res = await request(`/channel_control/update/${item.id}`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(item)
        });

        setConfigList(configList.map(config => 
          config.id === item.id ? { ...item, isEditing: false } : config
        ));
      }
      
      Toast.notify({
        type: 'success',
        message: '保存成功'
      });
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: '保存失败'
      });
    }
  };

  // 取消编辑
  const handleCancelEdit = (id: number) => {
    if (newItem && id === newItem.id) {
      setNewItem(null);
    } else {
      setConfigList(configList.map(item => 
        item.id === id ? { ...item, isEditing: false } : item
      ));
    }
  };

  // 开始编辑
  const handleStartEdit = (id: number) => {
    setConfigList(configList.map(item => 
      item.id === id ? { ...item, isEditing: true } : item
    ));
  };

  // 处理输入变化
  const handleInputChange = (id: number, field: keyof PaymentConfigItem, value: string | number | boolean) => {
    if (newItem && id === newItem.id) {
      setNewItem({ ...newItem, [field]: value });
    } else {
      setConfigList(configList.map(item => 
        item.id === id ? { ...item, [field]: value } : item
      ));
    }
  };

  useEffect(() => {
    getConfigList();
  }, []);

  // 渲染表格行
  const renderTableRow = (item: PaymentConfigItem) => {
    const isEditing = item.isEditing;
    
    return (
      <tr key={item.id} className="ml-3 my-1 border-1 text-gray-400">
        <td className="border border-slate-700 py-1 px-1">
          {isEditing ? (
            <input
              type="text"
              className="bg-gray-800 w-full px-2 py-1"
              value={item.channel}
              disabled={item.id !== newItem?.id} // 只有新增时可编辑
              onChange={(e) => handleInputChange(item.id, 'channel', e.target.value)}
            />
          ) : (
            item.channel
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1">
          {isEditing ? (
            <input
              type="text"
              className="bg-gray-800 w-full px-2 py-1"
              value={item.pay_type}
              disabled={item.id !== newItem?.id} // 只有新增时可编辑
              onChange={(e) => handleInputChange(item.id, 'pay_type', e.target.value)}
            />
          ) : (
            item.pay_type
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1">
          {isEditing ? (
            <input
              type="number"
              className="bg-gray-800 w-full px-2 py-1"
              value={item.min_amount}
              onChange={(e) => handleInputChange(item.id, 'min_amount', parseInt(e.target.value) || 0)}
            />
          ) : (
            item.min_amount
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1">
          {isEditing ? (
            <input
              type="number"
              className="bg-gray-800 w-full px-2 py-1"
              value={item.max_amount}
              onChange={(e) => handleInputChange(item.id, 'max_amount', parseInt(e.target.value) || 0)}
            />
          ) : (
            item.max_amount
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1">
          {isEditing ? (
            <input
              type="number"
              className="bg-gray-800 w-full px-2 py-1"
              value={item.ratio}
              onChange={(e) => handleInputChange(item.id, 'ratio', parseInt(e.target.value) || 0)}
            />
          ) : (
            item.ratio
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1 text-center">
          {isEditing ? (
            <input
              type="checkbox"
              className="h-4 w-4"
              checked={item.enabled}
              onChange={(e) => handleInputChange(item.id, 'enabled', e.target.checked)}
            />
          ) : (
            <div className="flex justify-center">
              <span 
                className={`inline-block w-3 h-3 rounded-full ${item.enabled ? 'bg-green-500' : 'bg-red-500'}`}
              ></span>
            </div>
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1">
          {isEditing ? (
            <input
              type="text"
              className="bg-gray-800 w-full px-2 py-1"
              value={item.remark}
              onChange={(e) => handleInputChange(item.id, 'remark', e.target.value)}
            />
          ) : (
            item.remark
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1">
          <div className="flex flex-wrap gap-1">
            {isEditing ? (
              <>
                <button
                  type="button"
                  className="p-1 px-3 bg-green-500 text-white rounded text-sm"
                  onClick={() => handleSaveItem(item)}
                >
                  保存
                </button>
                <button
                  type="button"
                  className="p-1 px-3 bg-gray-500 text-white rounded text-sm"
                  onClick={() => handleCancelEdit(item.id)}
                >
                  取消
                </button>
              </>
            ) : (
              <button
                type="button"
                className="p-1 px-3 bg-sky-500 text-white rounded text-sm"
                onClick={() => handleStartEdit(item.id)}
              >
                编辑
              </button>
            )}
          </div>
        </td>
      </tr>
    );
  };

  return (
    <div className="mt-2">
      <button
        type="button"
        className="p-2 px-5 bg-purple-500 text-white rounded text-sm mb-3"
        onClick={handleAddItem}
      >
        新增配置
      </button>
      
      <table className="dark:bg-gray-900 bg-white text-gray-500 text-sm border-collapse mt-2 w-full">
        <thead className="dark:bg-slate-700 bg-slate-300 dark:text-slate-300">
          <tr className="text-center text-xs sm:text-sm">
            <th className="border py-2 px-1 border-slate-600">渠道</th>
            <th className="border py-2 px-4 border-slate-600">付款方式</th>
            <th className="border py-2 px-4 border-slate-600">最小金额</th>
            <th className="border py-2 px-4 border-slate-600">最大金额</th>
            <th className="border py-2 px-4 border-slate-600">比例</th>
            <th className="border py-2 px-4 border-slate-600">是否启用</th>
            <th className="border py-2 px-4 border-slate-600">备注</th>
            <th className="border py-2 px-3 border-slate-600">操作</th>
          </tr>
        </thead>
        <tbody>
          {configList.map(item => renderTableRow(item))}
          {newItem && renderTableRow(newItem)}
        </tbody>
      </table>
    </div>
  );
};

export default PaymentConfig;
