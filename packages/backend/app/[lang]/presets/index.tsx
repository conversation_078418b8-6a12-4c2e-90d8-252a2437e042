'use client'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useParams, useRouter } from 'next/navigation'
import s from '@backend/app/[lang]/globals.module.css'
import Loader from '@little-tavern/shared/src/ui/Loading'
import Footer from "@backend/app/[lang]/components/footer";
import { AuthContext } from '../components/authContext'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import dynamic from 'next/dynamic'
import { set } from 'lodash'
const ReactJson = dynamic(() => import('react-json-view'), {
  ssr: false,
});

const btn = cn(s.primBtn, 'mx-2 text-sm')

const Pay = () => {
  const { t } = useTranslation()
  const [presets, setPresets] = useState<any>('')
  const auth = useContext(AuthContext);
  const isLogin = auth?.isLogin;
  const request = useRequest();
  const [loading, setIsLoading] = useState(true);
  const [models, setModels] = useState<any>([]);
  const curModelRef = useRef('')
  const curNSFWRef = useRef(1)
  const curContextRef = useRef(1)

  const get_presets = async (model: string, nsfw: number, scenario: number) => {
    setPresets({ "message": "Loading..." })
    const params = 'model=' + model + '&nsfw=' + nsfw + '&scenario=' + scenario
    const res = await request('/presets/get?' + params);
    setPresets(res.data);
  }

  useEffect(() => {
    const fetchPresets = async () => {
      const res = await request('/presets/llm_models');
      setModels(res.data);
      setIsLoading(false)
      curModelRef.current = res.data[0]
      curNSFWRef.current = 1
      curContextRef.current = 1
      get_presets(res.data[0], 1, 1);
    }
    fetchPresets();
  }, [isLogin])
  const onModelChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedModel = e.target.value;
    curModelRef.current = selectedModel
    get_presets(selectedModel, curNSFWRef.current, curContextRef.current);
  }, [])
  const onNSFWChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedNSFW = Number(e.target.value);
    curNSFWRef.current = selectedNSFW;
    get_presets(curModelRef.current, selectedNSFW, curContextRef.current);
  }, [])
  const onContextChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedContext = Number(e.target.value);
    curContextRef.current = selectedContext;
    get_presets(curModelRef.current, curNSFWRef.current, selectedContext);
  }, [])

  return (
    <>
      {loading ? <Loader /> : <main className="main-height bg-black w-full con-width px-3">
        <div className='pt-8 pb-4'>
          <span className='mr-4 text-lg'>当前预设</span>
        </div>
        <div className='mb-4'>
          {models.length > 0 &&
            <select id='model' className='w-48 bg-gray-800 p-1 mr-2' onChange={onModelChange}>
              {
                models.map((model: any) => {
                  return <option className='w-24 p-1' key={model} value={model}>
                    {model}
                  </option>
                })
              }
            </select>}
          <select id='nsfw' className='w-36 bg-gray-800 p-1 mr-2' onChange={onNSFWChange}>
            <option className='w-24 p-1' key="nsfw" value="1">nsfw</option>
            <option className='w-24 p-1' key="sfw" value="0">sfw</option>
          </select>
          <select id='context' className='w-36 bg-gray-800 p-1 mr-2' onChange={onContextChange}>
            <option className='w-24 p-1' key="chat" value="1">chat</option>
            <option className='w-24 p-1' key="impersonate" value="3">impersonate</option>
          </select>

        </div>
        <div className="px-6">
          <ReactJson src={presets} theme="monokai" displayDataTypes={false} displayObjectSize={false} />
        </div>
      </main>}
      <Footer cl='' />
    </>
  )
}

export default React.memo(Pay)
