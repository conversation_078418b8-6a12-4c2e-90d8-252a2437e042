'use client'
import React, { use, useCallback, useContext, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import s from '@backend/app/[lang]/globals.module.css'
import Loader from '@little-tavern/shared/src/ui/Loading'
import { AuthContext } from '../components/authContext'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import dynamic from 'next/dynamic'
import Toast from '@little-tavern/shared/src/ui/toast'
import { min, set } from 'lodash'
import { is } from 'immer/dist/internal.js'
import { on } from 'events'

const ReactJson = dynamic(() => import('react-json-view'), {
  ssr: false,
});

const btn = cn(s.primBtn, 'mx-2 text-sm')

const USE_FILTERS = [
  { value: 'DEFAULT', label: '付费' },
  { value: 'FREE_BENEFIT', label: '免费' },
  // { value: 'JOIN_ACTIVITY', label: '返钻活动' },
] as const;

const Pay = () => {
  const { t } = useTranslation()
  const auth = useContext(AuthContext);
  const isLogin = auth?.isLogin;
  const request = useRequest();
  const [loading, setIsLoading] = useState(true);

  const selectProductNameRef = useRef<string>('');
  const selectUseFilterRef = useRef<string>('')

  const [water_configs, setWaterConfigs] = useState<any[]>([]);
  const [product_list, setProductList] = useState<any[]>([]);
  const [model_config_list, setModelConfigList] = useState<any[]>([]);

  const selectedBackupModels = useRef<string[]>([]);

  const fetchGlobalConfig = async () => {
    const res = await request('/global/config/all');
    setProductList(res.product_list || []);
    selectProductNameRef.current = res.product_list[0]?.mid || '';
    selectUseFilterRef.current = 'DEFAULT';
    setModelConfigList(res.to_model_detail_list || []);
    refreshModelConfigList(selectProductNameRef.current, selectUseFilterRef.current);
    setIsLoading(false);
  }
  const refreshModelConfigList = async (product_mid: string, use_filter: string,) => {
    setIsLoading(true);
    const res = await request('/global/config/model_water/list_configs?product_mid=' + product_mid + '&use_filter=' + use_filter)
    const water_configs = res.data.water_configs || [];
    setWaterConfigs(water_configs);
    setIsLoading(false);
  }
  useEffect(() => {
    fetchGlobalConfig();
  }, [isLogin])
  const checkSuccessToast = (response: any) => {
    if (response.error_code === 0) {
      Toast.notify({
        type: 'success',
        message: response.message || '操作成功'
      });
      return true;
    }
    else {
      Toast.notify({
        type: 'error',
        message: response.message || '操作失败'
      });
    }
    return false;
  }

  const deleteModelWater = async (e: any) => {
    const id = e.target.getAttribute('data-id')
    const res = await request('/global/config/model_water/delete?id=' + id, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    if (checkSuccessToast(res)) {
      refreshModelConfigList(selectProductNameRef.current, selectUseFilterRef.current);
    }
  }
  const updateModelWater = async (e: any) => {
    const id = e.target.getAttribute('data-id');
    const num = e.target.parentNode.parentNode.parentNode.querySelector('input[name="num"]').value;
    let url_param = '';
    url_param += '?id=' + id + '&num=' + num
    const res = await request('/global/config/model_water/update_by_id' + url_param, {
      method: 'POST',
    })
    if (checkSuccessToast(res)) {
      refreshModelConfigList(selectProductNameRef.current, selectUseFilterRef.current);
    }
  }
  const addModelWater = async (e: any) => {
    let url = '';
    url += '?product_mid=' + e.get('product_mid');
    url += '&to_model=' + e.get('to_model');
    url += '&num=' + e.get('num');
    url += '&use_filter=' + e.get('use_filter');
    const res = await request('/global/config/model_water/add' + url, {
      method: 'POST'
    })
    if (checkSuccessToast(res)) {
      refreshModelConfigList(selectProductNameRef.current, selectUseFilterRef.current);
    }
  }
  const addModelConfig = async (e: any) => {
    console.log(e)
    const min_p = e.get('min_p') === '1' ? 1 : 0
    const repetition_penalty = e.get('repetition_penalty') === '1' ? 1 : 0
    const res = await request('/global/config/model/add', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        llm_model: e.get("llm_model"),
        request_llm_model: e.get("request_llm_model"),
        min_p: min_p,
        repetition_penalty: repetition_penalty
      })
    })
    if (res.error_code !== 0) {
      Toast.notify({
        type: 'error',
        message: res.message
      })
      return
    }
    Toast.notify({
      type: 'success',
      message: `新增成功`
    })
  }
  const updateModelWaterBackup = async (e: any) => {
    const water_config_id = e.get('water_config_id') as string;
    console.log("backup_models", selectedBackupModels.current);
    const res = await request('/global/config/model_water/add_backup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        water_config_id: water_config_id,
        backup_models: selectedBackupModels.current
      })
    })
    if (checkSuccessToast(res)) {
      refreshModelConfigList(selectProductNameRef.current, selectUseFilterRef.current);
    }
  }

  const onProductChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const productName = e.target.value
    console.log("productName", productName);
    selectProductNameRef.current = productName;
    refreshModelConfigList(productName, selectUseFilterRef.current);
  }

  const deleteModel = async (e: any) => {
    const url = '/global/config/model/delete?llm_model=' + e.target.getAttribute('data-id')
    const res = await request(url, {
      method: 'POST',
    })
    if (checkSuccessToast(res)) {
      fetchGlobalConfig();
    }
  }
  const onUseFilterChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedFilter = e.target.value;
    selectUseFilterRef.current = selectedFilter;
    refreshModelConfigList(selectProductNameRef.current, selectedFilter);
  }
  const updateOrder = async (id: number, direction: number) => {
    const params = "id=" + id + "&sort_order=" + direction;
    const res = await request('/global/config/model_water/update_sort_order?' + params, {
      method: 'POST'
    })
    if (checkSuccessToast(res)) {
      refreshModelConfigList(selectProductNameRef.current, selectUseFilterRef.current);
    }
  }
  return (
    <>
      {<main className="main-height bg-black w-full con-width px-3 pt-6">
        <h2 className='text-xl'>模型与掺水配置</h2>

        <div className='mt-3'>
          <div className="flex items-center w-full my-4">
            <hr className="flex-grow border-gray-400" />
            <h3 className="px-4 text-center font-medium">掺水管理</h3>
            <hr className="flex-grow border-gray-400" />
          </div>
          产品：{product_list.length > 0 && <select name="model" className='w-48 bg-gray-800 p-1 mr-2' onChange={onProductChange} value={selectProductNameRef.current}>
            {
              product_list.map((product: any) => {
                return <option className='w-24 p-1' key={product.mid} value={product.mid}>
                  {product.short_name}
                </option>
              })
            }
          </select>}
          使用场景：
          <select name="use_filter" className='w-48 bg-gray-800 p-1 mr-2' value={selectUseFilterRef.current} onChange={onUseFilterChange}>
            {USE_FILTERS.map(filter => (
              <option key={filter.value} value={filter.value}>
                {filter.label}
              </option>
            ))}
          </select>

          <table className='bg-gray-900 text-gray-500 text-sm border-collapse sm:w-[1200px]'>
            <thead className='bg-slate-700 text-slate-300'>
              <tr className='text-center text-xs sm:text-sm'>
                <th className='border py-2 px-1 border-slate-600'>id</th>
                <th className='border py-2 px-4 border-slate-600'>掺水模型</th>
                <th className='border py-2 px-4 border-slate-600'>掺水数量</th>
                <th className='border py-2 px-4 border-slate-600'>使用场景</th>
                <th className='border py-2 px-4 border-slate-600'>Backup模型</th>
                <th className='border py-2 px-4 border-slate-600'>状态</th>
                <th className='border py-2 px-4 border-slate-600'>操作</th>
              </tr>
            </thead>
            <tbody>
              {
                water_configs.map((config: any) => {
                  return <tr key={config.id} className='text-center text-xs sm:text-base'>
                    <td className='border border-slate-700 py-1 px-1'>{config.id}</td>
                    <td className='border border-slate-700 py-1 px-1'>{config.to_llm_model}</td>
                    <td className='border border-slate-700 py-1 px-1'>
                      <input type="text" name='num' defaultValue={config.num} className='w-16 bg-gray-800 mx-3' placeholder='' />
                    </td>
                    <td className='border border-slate-700 py-1 px-1'>
                      <div className='flex items-center justify-center space-x-4'>
                        {config.use_filter}
                      </div>
                    </td>
                    <td className='border border-slate-700 py-1 px-1'>
                      {config.backup_to_llm_models}
                    </td>
                    <td className='border border-slate-700 py-1 px-1'>{config.request_status}</td>


                    <td className='border border-slate-700 py-1 px-1'>
                      <div className="flex items-center justify-center space-x-2">
                        <button
                          data-id={config.id}
                          data-model-name={config.name}
                          onClick={updateModelWater}
                          className="px-3 py-1 text-sm bg-purple-500 hover:bg-purple-600 text-white rounded transition-colors whitespace-nowrap"
                        >
                          更新
                        </button>
                        <button
                          data-id={config.id}
                          onClick={deleteModelWater}
                          className="px-3 py-1 text-sm bg-red-500 hover:bg-red-600 text-white rounded transition-colors whitespace-nowrap"
                        >
                          删除
                        </button>
                        <button
                          data-id={config.id}
                          onClick={() => updateOrder(config.id, 1)}
                          className="px-3 py-1 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors whitespace-nowrap"
                        >
                          上移
                        </button>
                        <button
                          data-id={config.id}
                          onClick={() => updateOrder(config.id, -1)}
                          className="px-3 py-1 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors whitespace-nowrap"
                        >
                          下移
                        </button>
                      </div>
                    </td>

                  </tr>
                })
              }
            </tbody>
          </table>
          <h3 className='mt-4 mb-2'>Backup模型配置</h3>
          <form className='mt-3' action={updateModelWaterBackup}>
             掺水配置：{water_configs.length > 0 && <select name="water_config_id" className='w-48 bg-gray-800 p-1 mr-2'>
              {
                water_configs.map((config: any) => {
                  return <option className='w-24 p-1' key={config.id} value={config.id}>
                    {config.to_llm_model}（id:{config.id}）
                  </option>
                })
              }
            </select>}
            backup模型：
            <select name="backup_models" className='w-48 bg-gray-800 p-1 mr-2' onChange={(e) => {
              const selectedOptions = Array.from(e.currentTarget.selectedOptions);
              const selectedValues = selectedOptions.map(option => option.value)
              if (selectedValues.includes('')) {
                selectedValues.splice(selectedValues.indexOf(''), 1);
              }
              const selected_backup_models = selectedBackupModels.current.concat(selectedValues);
              selectedBackupModels.current = Array.from(new Set(selected_backup_models));
              // 这里可以处理选中的备份模型
              const backupModelsContainer = document.getElementById("backup_model_ret");
              if (!backupModelsContainer) return;
              backupModelsContainer.innerHTML = selectedBackupModels.current.join(', ');
            }}>
              <option className='w-24 p-1' value=''>请选择模型</option>
              {
                model_config_list.map((model: any) => {
                  return <option className='w-24 p-1' key={model.llm_model} value={model.llm_model}>
                    {model.llm_model}
                  </option>
                })
              }
            </select>
            <span id = "backup_model_ret" className='ml-2 text-gray-400'>
              {selectedBackupModels.current.join(', ')}
            </span>

            <button type="submit" className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>保存Backup</button>
            <button type="button" className='p-2 px-5 bg-red-500 text-white rounded text-sm ml-3' onClick={() => {
              selectedBackupModels.current = [];
              const backupModelsContainer = document.getElementById("backup_model_ret");
              if (backupModelsContainer) {
                backupModelsContainer.innerHTML = '';
              }
            }}>清空选项</button>
          </form>
          <h3 className='mt-4 mb-2'>新增掺水配置</h3>
          <form className='mt-3' action={addModelWater}>
            产品：{product_list.length > 0 && <select name="product_mid" className='w-48 bg-gray-800 p-1 mr-2'>
              {
                product_list.map((product: any) => {
                  return <option className='w-24 p-1' key={product.mid} value={product.mid}>
                    {product.short_name}
                  </option>
                })
              }
            </select>}
            使用场景：
            <select name="use_filter" className='w-48 bg-gray-800 p-1 mr-2' >
              {USE_FILTERS.map(filter => (
                <option key={filter.value} value={filter.value}>
                  {filter.label}
                </option>
              ))}
            </select>


            掺水数量： <input name='num' type="text" className='w-16 bg-gray-800 mx-3' placeholder=''/>
            {model_config_list.length > 0 && <select name="to_model" className='w-48 bg-gray-800 p-1 mr-2'>
              {
                model_config_list.map((model: any) => {
                  return <option className='w-24 p-1' key={model.llm_model} value={model.llm_model}>
                    {model.llm_model}
                  </option>
                })
              }
            </select>}
            <button type="submit" className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>新增</button>
          </form>
        </div>

        <div className='border-t border-gray-700 my-4'></div>
        <div className='mt-3'>
          <div className="flex items-center w-full my-4">
            <hr className="flex-grow border-gray-400" />
            <h3 className="px-4 text-center font-medium">模型管理</h3>
            <hr className="flex-grow border-gray-400" />
          </div>
          <form className='mt-3' action={addModelConfig}>
            推送配置名称： <input name='llm_model' type="text" className='w-32 bg-gray-800 mx-3' placeholder='' />
            实际请求名称： <input name='request_llm_model' type="text" className='w-32 bg-gray-800 mx-3' placeholder='' />
            MinP: <input name='min_p' type='checkbox' value='1' className='mx-3' />
            RepetitionPenalty: <input name='repetition_penalty' type="checkbox" value='1' className='mx-3' />
            <button type="submit" className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>新增</button>
          </form>
        </div>
        <div className='mt-3'>
          <h3 className='mb-2'>模型列表</h3>
          <table className='bg-gray-900 text-gray-500 text-sm border-collapse sm:w-[1200px]'>
            <thead className='bg-slate-700 text-slate-300'>
              <tr className='text-center text-xs sm:text-sm'>
                <th className='border py-2 px-1 border-slate-600'>配置模型</th>
                <th className='border py-2 px-1 border-slate-600'>请求模型</th>
                <th className='border py-2 px-4 border-slate-600'>支持参数</th>
                <th className='border py-2 px-4 border-slate-600'>是否缓存</th>
                <th className='border py-2 px-4 border-slate-600'>请求集群</th>
                <th className='border py-2 px-4 border-slate-600'>操作</th>
              </tr>
            </thead>
            <tbody>
              {
                model_config_list.map((model: any) => {
                  return <tr key={model.llm_model} className='text-center text-xs sm:text-base'>
                    <td className='border border-slate-700 py-1 px-1'>{model.llm_model}</td>
                    <td className='border border-slate-700 py-1 px-1'>{model.request_llm_model}</td>
                    <td className='border border-slate-700 py-1 px-1'>{JSON.stringify(model.support_params)}</td>
                    <td className='border border-slate-700 py-1 px-1'>{model.use_cache ? '是' : '否'}</td>
                    <td className='border border-slate-700 py-1 px-1'>{model.request_cluster}</td>
                    <td className='border border-slate-700 py-1 px-1'>
                      <div className="flex items-center justify-center space-x-2">
                        <button
                          data-id={model.llm_model}
                          onClick={deleteModel}
                          className="px-3 py-1 text-sm bg-red-500 hover:bg-red-600 text-white rounded transition-colors whitespace-nowrap"
                        >
                          下架
                        </button>
                      </div>
                    </td>
                  </tr>
                })
              }
            </tbody>
          </table>
        </div>
      </main>}
      {loading && <Loader />}
    </>
  )
}

export default React.memo(Pay)
