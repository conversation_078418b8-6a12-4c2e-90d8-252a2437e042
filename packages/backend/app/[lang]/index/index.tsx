'use client'
import React, { useCallback, useContext, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useParams } from 'next/navigation'
import Card from '@little-tavern/shared/src/ui/card'
import s from '@backend/app/[lang]/globals.module.css'
import Footer from "@backend/app/[lang]/components/footer";
import Toast from '@little-tavern/shared/src/ui/toast'
import { AuthContext } from '../components/authContext'
import useRequest from '@backend/app/[lang]/hook/useRequest'
import useCreateCard from '@little-tavern/shared/src/hook/useCreateCard'
import {base64ToBlob} from '@little-tavern/shared/src/module/base64ToBlob'
import TabSwitcher from '../components/TabSwitcher'
import useDialogLogin from '../hook/useDialogLogin'
import usePagesState from '@little-tavern/shared/src/hook/usePageState'
import useSWRImmutable from 'swr';
import Loader, {LoadingToast, LoadingToastFailed} from '@little-tavern/shared/src/ui/Loading'

const btn = cn(s.primBtn, 'mx-2 text-sm')

const Main = () => {
  const { t } = useTranslation()
  const params = useParams()
  const lang = params.lang as string
  const [userRoles, setUserRoles] = useState<any>([])
  const createCard = useCreateCard();
  const auth = useContext(AuthContext);
  const isLogin = auth?.isLogin;
  const request = useRequest();
  const [currentTag, setCurrentTag] = useState('精选');
  const [tagList, setTagList] = useState([])
  const dialogLogin = useDialogLogin();

  const { data, isLoading, error, mutate } = useSWRImmutable(`/roles/category/list?tags=${currentTag || ''}`, request, {
      revalidateOnFocus: false
  });

  useEffect(() => {
    data?.list.length > 0? setUserRoles(data?.list) : setUserRoles([]);
    data?.tags.length > 0 && setTagList(data?.tags);
  }, [data])

  const reflesh = () => {
    mutate();
  }
  
  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const formData = new FormData();
      const file = e.target.files[0];
      formData.append('image',file);
      formData.append('file_type', file.type);
      const hideLoading = Toast.showLoading('');
      try {
        const res = await request('/role_card/analysis/img', {
          method: 'POST',
          body: formData,
        });
        const data = res.role_config;
        data.role_book = res.role_book;
        data.role_token_count = res.role_token_count;
        // const data = await request('/api/parse-card', {
        //   method: 'POST',
        //   body: formData,
        // }, true);
        // const imgBlob = base64ToBlob(data.img_array_64);
        // data.role_avatar = URL.createObjectURL(imgBlob);
        // data.imgBlob = imgBlob;
        // console.log(data);
        hideLoading();
        // Toast.notify({type: 'success', message: '上传成功～'})
        const isSuccess = await createCard.show({msg: {isEdit: false, ...data, speakList: [], subTagList: [], admin: true, isFromImg: true, level_type: 'premium'}})
        isSuccess && reflesh();
      } catch (error) {
        Toast.notify({type: 'success', message: '上传失败～'})
        hideLoading();
        console.error('Error uploading image:', error);
      }
    }
    e.target.value = ''
  }
  const updateCards = async (userRoles: any) => {
    setUserRoles([...userRoles]);
  }
// console.log('userRoles', userRoles);
// console.log('tagList', tagList);
  return (
    <>
      <main className="main-height bg-black con-width">
        
        <div><div className='pt-8 pb-2 px-2'>
          <div>
            <button className={btn} onClick={async () => {
              const isSuccess = await createCard.show({msg: {isEdit: false, speakList: [], subTagList: [], admin: true, level_type: 'premium' }})
              if(isSuccess) {
                reflesh();
              }
            }}>创建</button>
            
            <label className={cn(btn, 'cursor-pointer')} htmlFor="cardInput">
              上传角色卡
              <input id="cardInput" name='cardInput' type="file" hidden accept="image/png" onChange={handleImageChange} />
            </label>
            </div>
        </div>
        <div className="px-2 pb-4 gap-6 w-full">
            <TabSwitcher userRoles={userRoles} speakList={[]} tagList={tagList} updateCards={updateCards} reflesh={reflesh} currentTag={currentTag} setCurrentTag={setCurrentTag} />
        </div>
        </div>
        {isLoading && <LoadingToast msg={t('app.common.loading')}/>}
        {error && !isLoading && <LoadingToastFailed retry={reflesh} error={error} />}
      </main>
      <Footer cl='justify-center ml-0 !m-auto left-0 right-0 w-full'/>
    </>
  )
}

export default Main
