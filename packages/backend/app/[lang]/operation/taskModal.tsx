import Modal from "@share/src/ui/dialog/Modal"
import { useState, useEffect, useRef } from 'react'
import useRequest from "../hook/useRequest"
import Toast from "@share/src/ui/toast"
import { DatePicker, Space, Pagination } from 'antd'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import { useForm, SubmitHandler } from "react-hook-form"
import TaskForm from "./taskForm"

type FormData = {
    task_list: any
}
const TaskModal = ({ onClose, reflesh, taskList, seasonId, isCreate, config }: any) => {
    const request = useRequest()
    const newTaskList = taskList.map((task: any) => {
        const _task: any = {};
        _task.role_ids = Object.keys(task.role_id_name_map).join(',')
        _task.real_return_rate = task.return_rate / 100;
        _task.real_grand_prize_return_rate = task.grand_prize_return_rate / 100;
        return {
            ...task,
            ..._task
        }
    })
    const {
        register,
        handleSubmit,
        watch,
        reset,
        setValue,
        control,
        setError,
        clearErrors,
        trigger,
        formState: { errors },
    } = useForm<FormData>({
        mode: 'onChange',
        defaultValues: {
            task_list: newTaskList || []
        }
    })

    const onSubmit: SubmitHandler<FormData> = async (data) => {
        console.log('data', data);
        const task_list = data.task_list.map((task: any) => {
            task.season_id = seasonId
            task.return_rate = task.real_return_rate * 100
            task.grand_prize_return_rate = task.real_grand_prize_return_rate * 100
            task.season_id = seasonId
            task.role_ids = task.role_ids.split(',').map((role_id: string) => Number(role_id))
            // task.remain_user_num_frequency = Number(task.remain_user_num_frequency)
            task.is_manual = task.is_manual === undefined ? false : task.is_manual
            // task.start_at = dayjs(task.start_at).unix()
            // task.end_at = dayjs(task.end_at).unix()
            // task.diamond_return_at = dayjs(task.diamond_return_at).unix()
            // task.diamond_expire_at = dayjs(task.diamond_expire_at).unix()
            return task
        })
        // 如果list元素不含task_id，放到task_created列表，如果含task_id，放到task_updated，如果对比taskList，存在删减的task_id，放到task_id_deleted
        const postData: any = {
            season_id: seasonId,
            task_created: [],
            task_updated: [],
            task_id_deleted: []
        };
        if (isCreate) {
            postData.task_created = task_list
        } else {
            task_list.forEach((task: any) => {
                if (task.task_id) {
                    postData.task_updated.push(task);
                } else {
                    postData.task_created.push(task);
                }
            });
            const currentTaskIds = task_list
                .filter((task: any) => task.task_id)
                .map((task: any) => task.task_id);

            const deletedTaskIds = taskList
                .map((task: any) => task.task_id)
                .filter((id: any) => !currentTaskIds.includes(id));

            postData.task_id_deleted = deletedTaskIds;
        }
        try {
            const res = await request(`/activity_diamond/task_batch_update`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(postData)
            })
            if (res.error_code === 0) {
                Toast.notify({
                    type: 'success',
                    message: '更新成功'
                })
                onClose()
                reflesh && reflesh()
            } else {
                Toast.notify({
                    type: 'error',
                    message: res.message
                })
            }

        } catch (error: any) {
            Toast.notify({
                type: 'error',
                message: error.message
            })
        } finally {
        }
    }
    // 提交前做一次全局检查并弹窗提示
    const handleFormSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        handleSubmit((data) => {
            onSubmit(data);
        }, async (errors) => {

        })(e);
    };
    return (
        <Modal isOpen={true} onClose={onClose} className='!max-w-[1530px]'>
            <div className='mb-5 h-[90vh] overflow-auto'>
                <h3 className='mt-5 mb-2'>编辑任务</h3>
                <form className='space-y-3' action="#" method="POST" onSubmit={handleFormSubmit}>

                    <TaskForm errors={errors} register={register} control={control} setError={setError} clearErrors={clearErrors} config={config} />

                    <div className='space-y-3'>
                        <div className='px-2 py-2 flex flex-row-reverse'>
                            <button
                                type="submit"
                                className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'
                            >
                                {isCreate ? '创建' : '更新'}
                            </button>
                            <button className='p-2 px-5 dark:bg-gray-800 bg-gray-300 rounded text-sm ' onClick={onClose}>取消</button>
                        </div>
                    </div>
                </form>
            </div>
        </Modal>
    )
}

export default TaskModal