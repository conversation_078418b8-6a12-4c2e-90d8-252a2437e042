import format from '@little-tavern/shared/src/module/formate-date'
import { useEffect, useState } from 'react';
import Toast from '@share/src/ui/toast';
import useRequest from '../hook/useRequest'
import CreateActivity from './createActivity';
import TaskModal from './taskModal';
import dayjs from 'dayjs'
import cn from 'classnames';
import React from 'react';

const titleCls = 'border py-2 px-1 border-slate-600';
const ConsumActivities = () => {
  const [showActivity, setShowActivity] = useState(false)
  const [showTaskModal, setShowTaskModal] = useState(false)
  const request = useRequest();
  const [list, setList] = useState<any>([])
  const [taskList, setTaskList] = useState<any>([])
  const [isUpdate, setIsUpdate] = useState(false)
  const [seasonId, setSeasonId] = useState<any>('')
  const [season, setSeason] = useState<any>({})
  // 创建还是编辑,默认创建
  const [isCreate, setIsCreate] = useState(true)
  const [showTaskIndex, setShowTaskIndex] = useState<number | null>(null)

  const createActivity = () => {
    console.log('createActivity')
    setShowActivity(true)
    setIsUpdate(false)
    setSeason({})
    setSeasonId('')
  }
  const reflesh = async () => {
    console.log('reflesh')
    getActivityList()
  }
  const refleshTask = async (seasonId: string) => {
    console.log('refleshTask')
    showTaskList(seasonId)
  }
  const getActivityList = async () => {
    Toast.showLoading('');
    const res = await request(`/activity_diamond/season_list`);
    Toast.hideLoading();
    if(res.error_code === 0) {
      setList(res.data?.list || [])
    } else {
      Toast.notify({
        type: 'error',
        message: res.message
      })
    }
  }
  const handleStatus = async (seasonId: string, status: number, e: any) => {
    e.stopPropagation()
    const res = await request(`/activity_diamond/season_status_update?season_id=${seasonId}&status=${status}`, {
      method: 'POST'
    })
    if(res.error_code === 0) {
      Toast.notify({
        type: 'success',
        message: status === 1 ? '发布成功' : '终止成功'
      })
      reflesh()
    } else {
      Toast.notify({
        type: 'error',
        message: res.message
      })
    }
  }
  
  const showTaskList = async (seasonId: string, index?: number) => {
    console.log('showTaskList')
    Toast.showLoading('');
    const res = await request(`/activity_diamond/task_list?season_id=${seasonId}`);
    Toast.hideLoading();
    if(res.error_code === 0) {
      index !== undefined && setShowTaskIndex(index)
      setTaskList(res.data?.list || [])
    } else {
      Toast.notify({
        type: 'error',
        message: res.message
      })
    }
  }
  const [config, setConfig] = useState<any>(null)
  const handleTask = async (seasonId: string, index: number,  e?: any, _isCreate?: boolean, copySeasonId?: string) => {
    e?.stopPropagation()
    Toast.showLoading('');
    const [_config, res] = await Promise.all([await request(`/global/config/all`), await request(`/activity_diamond/task_list?season_id=${seasonId}`)])
    Toast.hideLoading();
    if(res.error_code === 0) {
      setShowTaskModal(true)
      // 如果是复制，seasonId传copySeasonId，否则传实际的seasonId
      setSeasonId(copySeasonId || seasonId)
      setShowTaskIndex(index)
      setTaskList(res.data.list || [])
      setConfig(_config)
      if(res.data?.list?.length > 0 && !_isCreate) {
        setIsCreate(false)
      } else {
        setIsCreate(true)
      }
    } else {
      Toast.notify({
        type: 'error',
        message: res.message
      })
    }
  }
  const handleCopyTask = async (seasonId: string, index: number, e?: any) => {
    e?.stopPropagation()
    const seasonIndex = Number(prompt('请输入要复制的赛季序号'))
    handleTask(list[seasonIndex]?.season_id, index, e, true, seasonId)
  }
  useEffect(() => {
    getActivityList()
  }, [])

  const Activity = ({active, statusCls, statusTxt, index, className}: any) => {
    const isTask = index === undefined;
    const activityTime = format(active?.start_at * 1000 || 0, 'YYYY-MM-DD HH:mm:ss') + '至' + format(active?.end_at * 1000 || 0, 'YYYY-MM-DD HH:mm:ss');
    const taskTime = format(active?.start_at * 1000 || 0, 'HH:mm:ss') + '至' + format(active?.end_at * 1000 || 0, 'HH:mm:ss'); 

    return <tr className={`ml-3 my-1 border-1 text-gray-400 ${className}`} onClick={() => {
      if(index === undefined) {
        return
      }
      setShowTaskIndex(index);
      showTaskList(active?.season_id, index)
      }}>
    <td className='border border-slate-700 py-1 px-1'>{index}</td>
    <td className='border border-slate-700 py-1 px-1'>{!isTask && active?.season_id}</td>
    <td className='border border-slate-700 py-1 px-1'>{isTask && active?.task_id}</td>
    <td className='border border-slate-700 py-1 px-1'>{!isTask && activityTime}</td>
    <td className='border border-slate-700 py-1 px-1'>{isTask && taskTime}</td>
    <td className='border border-slate-700 py-1 px-1'>{active?.title}</td>
    <td className='border border-slate-700 py-1 px-1'>{active?.start_notice}</td>
    <td className='border border-slate-700 py-1 px-1'>{active?.end_notice}</td>
    <td className='border border-slate-700 py-1 px-1'>{isTask && active?.role_ids?.join(', ') || ''}</td>
    <td className='border border-slate-700 py-1 px-1'>{isTask && active?.required_diamond_amount}</td>
    <td className='border border-slate-700 py-1 px-1'>{isTask && `${active?.return_rate/100}%`}</td>
    <td className='border border-slate-700 py-1 px-1'>{isTask && `${active?.grand_prize_return_rate/100}%`}</td>
    <td className='border border-slate-700 py-1 px-1'>{isTask && active?.grand_prize_count}</td>
    <td className='border border-slate-700 py-1 px-1'>{active?.max_participants}</td>
    <td className='border border-slate-700 py-1 px-1'>{active?.allow_repeated_enrolled !== undefined && (active?.allow_repeated_enrolled? '可重复' : '不可重复')}</td>
    <td className={cn(statusCls, 'border border-slate-700 py-1 px-1' )}>
    {index !== undefined && statusTxt}
    </td>
    <td className='border border-slate-700 py-1 px-1'>
      {index !== undefined && <div className='flex flex-wrap gap-1'>
        <button type='button' className='p-1 px-3 bg-blue-500 text-white rounded text-sm' onClick={(e) => {
          e.stopPropagation()
          console.log('createActivity')
          setShowActivity(true)
          setIsUpdate(true)
          setSeason(active)
          setSeasonId(active?.season_id)
        }}>编辑赛季</button>
        <button type='button' className='p-1 px-3 bg-green-500 text-white rounded text-sm' onClick={(e) => handleTask(active?.season_id, index, e)}>编辑任务</button>
        <button type='button' className='p-1 px-3 bg-green-500 text-white rounded text-sm' onClick={(e) => handleStatus(active?.season_id, 1, e)}>发布</button>
        <button type='button' className='p-1 px-3 bg-red-500 text-white rounded text-sm' onClick={(e) => handleStatus(active?.season_id, 2, e)}>终止</button>
        <button type='button' className='p-1 px-3 bg-blue-500 text-white rounded text-sm' onClick={(e) => {
          e.stopPropagation()
          console.log('copy activity')
          setShowActivity(true)
          setIsUpdate(false)
          setSeason(active)
        }}>复制赛季</button>
        {<button type='button' className='p-1 px-3 bg-purple-500 text-white rounded text-sm' onClick={(e) => handleCopyTask(active?.season_id, index, e)}>复制任务</button>}
      </div> }
    </td>
  </tr>
  }
  return <div className='mt-2'>
    <button onClick={createActivity} type='button' className='btn'>新增</button>
    <table className='dark:bg-gray-900 bg-white text-gray-500 text-sm border-collapse mt-2'>
      <thead className='dark:bg-slate-700 bg-slate-300 dark:text-slate-300'>
        <tr className='text-center text-xs sm:text-sm'>
          <th className={titleCls}>序号</th>
          <th className={titleCls}>season_id</th>
          <th className={titleCls}>task_id</th>
          <th className={titleCls}>活动日期</th>
          <th className={titleCls}>活动时间段</th>
          <th className={titleCls}>活动标题</th>
          <th className={titleCls}>活动开始内容</th>
          <th className={titleCls}>活动结束内容</th>
          <th className={titleCls}>指定角色卡</th>
          <th className={titleCls}>活动消耗钻石数</th>
          <th className={titleCls}>返还比例</th>
          <th className={titleCls}>大奖返还比例</th>
          <th className={titleCls}>可获得大奖人数</th>
          <th className={titleCls}>可报名人数</th>
          <th className={titleCls}>是否可以重复报名</th>
          <th className={titleCls}>状态</th>
          <th className={titleCls}>操作</th>
        </tr>
      </thead>
      <tbody>
        {list?.map((active: any, index: number) => {
          let statusTxt = ''
          let statusCls = ''
          if(active?.status === 0) {
            statusTxt = '已创建，待发布'
            statusCls = 'text-yellow-500'
          } else if(active?.status === 1) {
            statusTxt = '已发布上线'
            statusCls = 'text-green-500'
          } else if(active?.status === 2) {
            statusTxt = '已手动终止'
            statusCls = 'text-red-500'
          }
          return <React.Fragment key={index}>
            <Activity index={index} active={active} statusTxt={statusTxt} statusCls={statusCls} />
            {showTaskIndex === index && taskList.map((task: any, i: number) => {
              return <Activity className='bg-slate-800' key={`${index}-${i}`} active={task} statusTxt={statusTxt} statusCls={statusCls} />
            })}
          </React.Fragment>
        })}
      </tbody>
    </table>
    {showActivity && <CreateActivity onClose={() => setShowActivity(false)} isUpdate={isUpdate} season={season} reflesh={reflesh} seasonId={seasonId} />}
    {showTaskModal && <TaskModal onClose={() => setShowTaskModal(false)}  reflesh={() => {
      refleshTask(seasonId)
    }} seasonId={seasonId} taskList={taskList} isCreate={isCreate} config={config} />}
  </div>
}

export default ConsumActivities