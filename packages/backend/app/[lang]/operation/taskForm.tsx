import { useTranslation } from 'react-i18next'
import { Switch } from '@headlessui/react'
import cn from 'classnames';
import { useState, useEffect, useCallback, useRef } from 'react';
import { Controller, useFieldArray, useWatch } from "react-hook-form"
import AutoTextareaHeight from '@share/src/ui/dialog/CreateCard/AutoTextareaHeight';
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import useComfirm from '@share/src/hook/useComfirm';
import Toast from '@share/src/ui/toast';
import _ from 'lodash';
import { getByteLength } from '@share/src/module/stringTool';
import { DatePicker, Space, Pagination } from 'antd'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'

const itemTokensLimit = 1000
const ipt = 'mt-1 block rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6 dark:bg-gray-800 dark:text-white flex-1 px-3 w-24'
const TaskForm = ({ errors, control, config }: any) => {
  const { t } = useTranslation()
  const comfirm = useComfirm();
  const watchFileds = useWatch({
    control,
    name: "task_list"
  });

  let { fields, append, remove } = useFieldArray({
    control,
    name: "task_list",
    rules: {
      validate: {

      }
    }
  });

  useEffect(() => {
    if(watchFileds.length < 1) {
      addRoleBook()
    }
  }, [])

  const addRoleBook = () => {
    append({
      start_at: '', // 活动开始时间
      end_at: '',  // 活动结束时间
      start_notice: '', // 进行中的内容
      end_notice: '', // 活动结束的内容
      allow_repeated_enrolled: false, // 单日用户是否可以重复参与
      required_diamond_amount: '', // 活动消耗钻石数
      real_return_rate: '', // 返还钻石比例
      real_grand_prize_return_rate: '', // 大奖返还比例
      grand_prize_count: '', // 获得大奖人数
      max_participants: '', // 可报名人数
      role_ids: '', // 指定角色卡
      diamond_gotten_manually: false,
      left_participants_notice_interval: '',
      allowed_chat_models: []
    })
  }
  return <>
    {<div className="col-span-full flex items-center space-x-2" onClick={() => { }}>
      {<div className='flex flex-wrap'>
        {watchFileds?.map((elem: any, index: number) => {
          return <div className={cn('mt-2 bg-gray-200 dark:bg-gray-800 dark:ring-gray-500 py-0.5 px-1 rounded-md flex items-center space-x-3 w-[480px] inline-block')} key={index}>
            <div className='relative space-y-2 py-2 px-2 w-96 inline-block'>
              <div className=''>
                <Controller
                  name={`task_list[${index}].start_at`}
                  control={control}
                  rules={{
                    required: '请选择开始时间'
                  }}
                  render={({ field: { value: startValue, onChange: onStartChange } }) => (
                    <Controller
                      name={`task_list[${index}].end_at`}
                      control={control}
                      rules={{
                        required: '请选择结束时间'
                      }}
                      render={({ field: { value: endValue, onChange: onEndChange } }) => (
                        <div className='mb-2 w-96'>
                          <div className='flex items-center space-x-2'>
                            <span className="text-sm">活动时间段*</span>
                            {(errors?.task_list?.[index]?.start_at || errors?.task_list?.[index]?.end_at) && (
                              <p className="text-sm text-red-600">
                                {errors.task_list[index].start_at?.message || errors.task_list[index].end_at?.message}
                              </p>
                            )}
                          </div>
                          <Space className="">
                            <DatePicker
                              showTime
                              placeholder="开始时间"
                              value={startValue ? dayjs.unix(startValue) : null}
                              onChange={(date) => {
                                onStartChange(date ? date.unix() : '');
                              }}
                              className="w-48"
                            />
                            <span className="text-sm">至</span>
                            <DatePicker
                              showTime
                              placeholder="结束时间"
                              value={endValue ? dayjs.unix(endValue) : null}
                              onChange={(date) => {
                                onEndChange(date ? date.unix() : '');
                              }}
                              className="w-48"
                            />
                          </Space>
                        </div>
                      )}
                    />
                  )}
                />
              </div>

              <div className=''>
                <Controller
                  name={`task_list[${index}].start_notice`}
                  control={control}
                  rules={{
                    required: '请输入进行中的内容',
                    maxLength: {
                      value: 1000,
                      message: '最多输入1000个字'
                    }
                  }}
                  render={({ field }) => <div className='mb-2 w-96'>
                    <div className='flex flex-wrap items-center space-x-2'>
                      <span className='text-sm'>进行中的内容*</span>
                      {errors?.task_list?.[index]?.start_notice && (
                        <p className="text-sm text-red-600">
                          {errors.task_list[index].start_notice.message}
                        </p>
                      )}
                    </div>
                    <AutoTextareaHeight
                      data-insert
                      {...field}
                      rows={3}
                      value={watchFileds?.[index]?.start_notice || ''}
                      className={`flex-1 dark:bg-gray-800 w-full`}
                      placeholder='最多输入1000个字（福利频道发消息）' /></div>}
                />
              </div>

              <div className=''>
                <Controller
                  name={`task_list[${index}].end_notice`}
                  control={control}
                  rules={{
                    required: '请输入活动结束的内容',
                    maxLength: {
                      value: 1000,
                      message: '最多输入1000个字'
                    }
                  }}
                  render={({ field }) => <div className='mb-2 w-96'>
                    <div className='flex items-center space-x-2'>
                      <span className="text-sm">活动结束的内容*</span>
                      {errors?.task_list?.[index]?.end_notice && (
                        <p className="text-sm text-red-600">
                          {errors.task_list[index].end_notice.message}
                        </p>
                      )}
                    </div>
                    <AutoTextareaHeight
                      data-insert
                      {...field}
                      rows={3}
                      value={watchFileds?.[index]?.end_notice || ''}
                      className={`flex-1 dark:bg-gray-800 w-full`}
                      placeholder='最多输入1000个字（福利频道发消息）' /></div>}
                />
              </div>

              <div className=''>
                <Controller
                  name={`task_list[${index}].allow_repeated_enrolled`}
                  control={control}
                  render={({ field: { value, onChange } }) => (
                    <div className="flex items-center">
                      <span>单日用户是否可以重复参与</span>
                      <Switch
                        checked={value}
                        onChange={onChange}
                        className={cn(
                          value ? 'bg-blue-600' : 'bg-gray-200',
                          'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 ml-2'
                        )}
                      >
                        <span className={cn(
                          value ? 'translate-x-5' : 'translate-x-0',
                          'pointer-events-none relative inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                        )} />
                      </Switch>
                    </div>
                  )}
                />
              </div>

              <div className=''>
                <Controller
                  name={`task_list[${index}].required_diamond_amount`}
                  control={control}
                  rules={{
                    required: '请输入活动消耗钻石数',
                    pattern: {
                      value: /^[0-9]+$/,
                      message: '请输入正整数'
                    },
                    validate: {
                      isInteger: (value) => {
                        if (!value) return true;
                        const num = Number(value);
                        return Number.isInteger(num) && num > 0 || '请输入正整数';
                      }
                    }
                  }}
                  render={({ field }) => (
                    <div className='flex gap-x-2 w-96 items-center'>
                      <span className="text-sm">活动消耗钻石数*</span>
                      <input
                        type="text"
                        {...field}
                        placeholder="请输入活动消耗钻石数"
                        className={cn(ipt)}
                      />
                      {errors?.task_list?.[index]?.required_diamond_amount && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.task_list[index].required_diamond_amount.message}
                        </p>
                      )}
                    </div>
                  )}
                />
              </div>

              <div className=''>
                <Controller
                  name={`task_list[${index}].real_return_rate`}
                  control={control}
                  rules={{
                    required: '请输入返还钻石比例',
                    pattern: {
                      value: /^[0-9]+(\.[0-9]{0,2})?$/,
                      message: '请输入正确的比例（最多两位小数）'
                    },
                    validate: {
                      isValidNumber: (value) => {
                        if (!value) return true;
                        const num = Number(value);
                        return !isNaN(num) && num >= 0 || '请输入有效的非负数';
                      }
                    }
                  }}
                  render={({ field }) => (
                    <div className='flex gap-x-2 w-96 items-center'>
                      <span className="text-sm">返还钻石比例*</span>
                      <input
                        type="text"
                        {...field}
                        onInput={(e) => {
                          const value = e.currentTarget.value;
                          e.currentTarget.value = value.replace(/[^\d.]/g, '').replace(/(\..*)\./g, '$1');
                        }}
                        placeholder="请输入比例"
                        className={cn(ipt)}
                      />
                      <span className="text-sm">%</span>
                      {errors?.task_list?.[index]?.real_return_rate && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.task_list[index].real_return_rate.message}
                        </p>
                      )}
                    </div>
                  )}
                />
              </div>

              <div className=''>
                <Controller
                  name={`task_list[${index}].real_grand_prize_return_rate`}
                  control={control}
                  rules={{
                    required: '请输入大奖返还比例',
                    pattern: {
                      value: /^[0-9]+(\.[0-9]{0,2})?$/,
                      message: '请输入正确的比例（最多两位小数）'
                    },
                    validate: {
                      isValidNumber: (value) => {
                        if (!value) return true;
                        const num = Number(value);
                        return !isNaN(num) && num >= 0 || '请输入有效的非负数';
                      }
                    }
                  }}
                  render={({ field }) => (
                    <div className='flex gap-x-2 w-96 items-center'>
                      <span className="text-sm">大奖返还比例*</span>
                      <input
                        type="text"
                        {...field}
                        onInput={(e) => {
                          const value = e.currentTarget.value;
                          e.currentTarget.value = value.replace(/[^\d.]/g, '').replace(/(\..*)\./g, '$1');
                        }}
                        placeholder="请输入比例"
                        className={cn(ipt)}
                      />
                      <span className="text-sm">%</span>
                      {errors?.task_list?.[index]?.real_grand_prize_return_rate && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.task_list[index].real_grand_prize_return_rate.message}
                        </p>
                      )}
                    </div>
                  )}
                />
              </div>

              <div className=''>
                <Controller
                  name={`task_list[${index}].grand_prize_count`}
                  control={control}
                  rules={{
                    required: '请输入获得大奖人数',
                    pattern: {
                      value: /^[0-9]+$/,
                      message: '请输入正整数'
                    },
                    validate: {
                      isInteger: (value) => {
                        if (!value) return true;
                        const num = Number(value);
                        return Number.isInteger(num) && num > 0 || '请输入正整数';
                      }
                    }
                  }}
                  render={({ field }) => (
                    <div className='flex gap-x-2 w-96 items-center'>
                      <span className="text-sm">获得大奖人数*</span>
                      <input
                        type="text"
                        {...field}
                        placeholder="请输入人数"
                        className={cn(ipt)}
                      />
                      <span className="text-sm">人</span>
                      {errors?.task_list?.[index]?.grand_prize_count && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.task_list[index].grand_prize_count.message}
                        </p>
                      )}
                    </div>
                  )}
                />
              </div>

              <div className=''>
                <Controller
                  name={`task_list[${index}].max_participants`}
                  control={control}
                  rules={{
                    required: '请输入可报名人数',
                    pattern: {
                      value: /^[0-9]+$/,
                      message: '请输入正整数'
                    },
                    validate: {
                      isInteger: (value) => {
                        if (!value) return true;
                        const num = Number(value);
                        return Number.isInteger(num) && num > 0 || '请输入正整数';
                      }
                    }
                  }}
                  render={({ field }) => (
                    <div className='flex gap-x-2 w-96 items-center'>
                      <span className="text-sm">可报名人数*</span>
                      <input
                        type="text"
                        {...field}
                        placeholder="请输入人数"
                        className={cn(ipt)}
                      />
                      <span className="text-sm">人</span>
                      {errors?.task_list?.[index]?.max_participants && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.task_list[index].max_participants.message}
                        </p>
                      )}
                    </div>
                  )}
                />
              </div>

              <div className=''>
                <Controller
                  name={`task_list[${index}].role_ids`}
                  control={control}
                  rules={{
                    required: '请输入指定角色卡',
                    pattern: {
                      value: /^(-1|[0-9]+(,[0-9]+)*)$/,
                      message: '请使用英文逗号分隔，不能包含空格或中文逗号'
                    },
                    validate: {
                      noChineseComma: (value) =>
                        !value.includes('，') || '请使用英文逗号(,)而不是中文逗号(，)',
                      noSpaces: (value) =>
                        !value.includes(' ') || '请删除空格',
                      validFormat: (value) => {
                        const parts = value.split(',');
                        return parts.every((part: any) => /^(-1|\d+$)/.test(part)) || '请输入正确的数字格式，用英文逗号分隔';
                      }
                    }
                  }}
                  render={({ field }) => {
                    return <>
                      <div>
                        {errors?.task_list?.[index]?.role_ids && (
                          <p className="mt-1 text-sm text-red-600">
                            {errors.task_list[index].role_ids.message}
                          </p>
                        )}
                      </div>
                      <div className='flex gap-x-2 w-96 items-center'>
                      <span className="text-sm">指定角色卡*</span>
                        <input
                          type="text"
                          {...field}
                          placeholder="请输入角色卡id，使用英文逗号分隔，如：1,2,3"
                          className={cn(ipt)}
                        />
                    </div>
                    </>
                  }}
                />
              </div>

              <div className=''>
                <Controller
                  name={`task_list[${index}].allowed_chat_models`}
                  control={control}
                  rules={{
                    required: '请选择指定模型'
                  }}
                  render={({ field }) => {
                    return <>
                      <div>
                        {errors?.task_list?.[index]?.allowed_chat_models && (
                          <p className="mt-1 text-sm text-red-600">
                            {errors.task_list[index].allowed_chat_models.message}
                          </p>
                        )}
                      </div>
                      <div className='flex flex-col gap-y-2 w-96'>
                        <span className="text-sm">请选择指定模型*</span>
                        <div className='flex flex-wrap gap-x-4 gap-y-2'>
                          {config?.product_list
?.map((model: any) => (
                            <label key={model.mid} className='flex items-center space-x-2 cursor-pointer'>
                              <input
                                type="checkbox"
                                {...field}
                                checked={field.value.indexOf(model.mid) !== -1}
                                value={model.mid}
                                onChange={(e) => {
                                  const selectedValue = model.mid;
                                  const newValue = [...field.value];
                                  if (e.target.checked) {
                                    if (newValue.indexOf(selectedValue) === -1) {
                                      newValue.push(selectedValue);
                                    }
                                  } else {
                                    const index = newValue.indexOf(selectedValue);
                                    if (index !== -1) {
                                      newValue.splice(index, 1);
                                    }
                                  }
                                  field.onChange(newValue);
                                }}
                                className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                              />
                              <span className="text-sm">{model.model_name}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </>
                  }}
                />
              </div>

              <div className=''>
                <Controller
                  name={`task_list[${index}].left_participants_notice_interval`}
                  control={control}
                  rules={{
                    required: '请输入正整数',
                    pattern: {
                      value: /^[0-9]+$/,
                      message: '请输入正整数'
                    },
                    validate: {
                      isInteger: (value) => {
                        if (!value) return true;
                        const num = Number(value);
                        return Number.isInteger(num) && num > 0 || '请输入正整数';
                      }
                    }
                  }}
                  render={({ field }) => (
                    <div className='flex gap-x-2 items-center'>
                      <span className="text-sm">剩余报名人数发送频次*</span>
                      <input
                        type="text"
                        {...field}
                        placeholder="请直接输入正整数"
                        className={cn(ipt, '!w-6')}
                      />
                      <span>单位：条</span>
                      {errors?.task_list?.[index]?.left_participants_notice_interval && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.task_list[index].left_participants_notice_interval.message}
                        </p>
                      )}
                    </div>
                  )}
                />
              </div>

              <div className=''>
                <Controller
                  name={`task_list[${index}].diamond_gotten_manually`}
                  control={control}
                  render={({ field: { value, onChange } }) => (
                    <div className="flex items-center">
                      <span>奖励手动领取</span>
                      <Switch
                        checked={value}
                        onChange={onChange}
                        className={cn(
                          value ? 'bg-blue-600' : 'bg-gray-200',
                          'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 ml-2'
                        )}
                      >
                        <span className={cn(
                          value ? 'translate-x-5' : 'translate-x-0',
                          'pointer-events-none relative inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                        )} />
                      </Switch>
                    </div>
                  )}
                />
              </div>

              <div className=''>
                <Controller
                  name={`task_list[${index}].task_id`}
                  control={control}
                  render={({ field }) => {
                    return <></>
                  }}
                />
              </div>
            </div>
            <div className='text-center space-y-2 pl-2'>
              <button className='!py-0 !px-3 !text-4xl text-blue-500 btn' type="button" onClick={
                async (e) => {
                  addRoleBook()
                }}>+</button>
              <button className='p-0.5 !px-3 text-xs text-blue-500 btn' type="button" onClick={
                async (e) => {
                  const currentValues = watchFileds[index];
                  const newValues = { ...currentValues };
                  delete newValues.task_id;
                  append(newValues);
                }}>复制</button>
              <button className='!py-0 !px-3 !text-4xl text-blue-500 btn' type="button" onClick={
                async (e) => {
                  remove(index)
                }}>-</button>
            </div>
          </div>
        })}
        <div>
        </div>
      </div>
      }
    </div>}
  </>
}

export default TaskForm