import Modal from "@share/src/ui/dialog/Modal"
import { useState, useEffect, useRef } from 'react'
import useRequest from "../hook/useRequest"
import Toast from "@share/src/ui/toast"
import { DatePicker, Space, Pagination } from 'antd'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'

const Anouncement = ({ onClose, reflesh, anouncementData, platformList, positionList, showPeriodList, positionAllowedPlatform ,actionTypeList,userScopeList}: any) => {
    const request = useRequest()
    const [loading, setLoading] = useState(false)
    const titleRef = useRef<any>(null)
    const contentRef = useRef<any>(null)
    const expireDaysRef = useRef<any>(null)
    const [startDate, setStartDate] = useState<Dayjs | null>(() => anouncementData?.start_at ? dayjs(anouncementData.start_at * 1000) : dayjs())
    const [endDate, setEndDate] = useState<Dayjs | null>(() => anouncementData?.end_at ? dayjs(anouncementData.end_at * 1000) : dayjs())
    const [chatPlatform, setChatPlatform] = useState<any>(anouncementData?.chat_platform || [])
    const [position, setPosition] = useState<string>(anouncementData?.position || 'HOME')
    const [showPeriod, setShowPeriod] = useState<string>(anouncementData?.show_period || 'ONCE')
    const [actionType, setActionType] = useState<string>(anouncementData?.button_action_type || '')
    const [userScopes, setUserScopes] = useState<any>(anouncementData?.user_scopes || [])
    const buttonTextRef = useRef<any>(null)
    const buttonLinkUrlRef = useRef<any>(null)
    const [showCloseIcon, setShowCloseIcon] = useState<boolean>(anouncementData?.show_close_icon || false)
    const handleSave = async () => {
        const title = titleRef?.current.value;
        const content = contentRef?.current.value;
        if (!title) {
            Toast.notify({
                type: 'info',
                message: '请输入标题'
            })
            return
        }
        if (!content) {
            Toast.notify({
                type: 'info',
                message: '请输入内容'
            })
            return
        }
        if (content.length > 500) {
            Toast.notify({
                type: 'info',
                message: '最多输入500个字'
            })
            return
        }
        if (!startDate || !endDate) {
            Toast.notify({
                type: 'info',
                message: '请选择日期'
            })
            return
        }
        if(chatPlatform.length === 0) {
            Toast.notify({
                type: 'info',
                message: '请选择平台'
            })
            return
        }
        try {
            setLoading(true)
            const res = await request(anouncementData?.id ? '/operation/popup/update' : '/operation/popup/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    "title": title,
                    "content": content,
                    "start_at": startDate?.unix(),
                    "end_at": endDate?.unix(),
                    "show_period": showPeriod,
                    "chat_platform": chatPlatform,
                    "position": position,
                    "button_action_type": actionType,
                    "button_link_url": buttonLinkUrlRef?.current.value,
                    "button_text": buttonTextRef?.current.value,
                    "show_close_icon": showCloseIcon,
                    "user_scopes": userScopes,
                    "id": anouncementData?.id
                })
            })
            if (res.error_code === 0) {
                Toast.notify({
                    type: 'success',
                    message: anouncementData?.id ? `更新成功` : `创建成功`
                })
                reflesh && reflesh()
                onClose && onClose()
            } else {
                Toast.notify({
                    type: 'error',
                    message: res.message
                })
            }
        } catch (e) {
            Toast.notify({
                type: 'error',
                message: anouncementData?.id ? '更新失败' : '创建失败'
            })
        } finally {
            setLoading(false)
        }
    }
    const handleDateChange = (dates: any, dateType: 'start' | 'end') => {
        if (dateType === 'start') {
            setStartDate(dates)
        } else {
            setEndDate(dates)
        }
    }

    return (
        <Modal isOpen={true} onClose={onClose} className='!max-w-4xl'>
            <div className='mb-5'>
                <div className='space-y-3'>
                    <div className="flex items-center w-full my-4">
                        <hr className="flex-grow border-gray-600" />
                        <h3 className="px-4 text-center font-medium">弹窗内容</h3>
                        <hr className="flex-grow border-gray-600" />
                    </div>
                    <div className='flex items-center'>
                        弹窗标题*
                        <input
                            type="text" ref={titleRef}
                            id="title"
                            defaultValue={anouncementData?.title}
                            autoComplete="title"
                            required
                            placeholder='最多输入15个字'
                            className="ml-2 rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 w-52 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 mr-1"
                        />
                    </div>
                    <div className='flex items-center'>
                        <span>弹窗内容*</span>
                        <textarea
                            defaultValue={anouncementData?.content}
                            ref={contentRef}
                            id="content"
                            autoComplete="content"
                            required
                            rows={5}
                            placeholder='最多输入500个字'
                            className="ml-2 rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 w-52 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 mr-1 w-[700px]"
                        />
                    </div>
                    <div className='flex items-center'>
                        <span>跳转类型</span>
                        <select
                            id="action_type"
                            ref={expireDaysRef}
                            defaultValue={anouncementData?.button_action_type}
                            onChange={(e) => {
                                setActionType(e.target.value)
                            }}
                            className="ml-2 rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 w-52 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 mr-1"
                        >
                            {actionTypeList.map((item: any) => {
                                return <option key={item.key} value={item.key}>{item.display}</option>
                            }
                            )}
                        </select>
                    </div>
                    <div className='flex items-center'>
                        <span>按钮文字</span>
                        <input
                            type="text"
                            id="btn"
                            ref={buttonTextRef}
                            defaultValue={anouncementData?.button_text}
                            autoComplete="btn"
                            placeholder='请输入按钮文字'
                            className="ml-2 rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 w-52 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 mr-1"
                        />
                    </div>
                    <div className='flex items-center'>
                        <span>跳转URL</span>
                        <input
                            type="text"
                            id="link_url"
                            ref={buttonLinkUrlRef}
                            defaultValue={anouncementData?.button_link_url}
                            autoComplete="link_url"
                            placeholder='请输入跳转URL'
                            className="ml-2 rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 w-52 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 mr-1"
                        />
                    </div>
                    <div className='flex items-center'>
                        <span>关闭按钮</span>
                        <input
                            type="checkbox"
                            id="show_close_icon"
                            defaultChecked={anouncementData?.show_close_icon}
                            onChange={(e) => {
                                setShowCloseIcon(e.target.checked)
                            }}
                            className="ml-2 mr-2 align-middle"
                        />是否展示
                    </div>
                    <div className="flex items-center w-full my-4">
                        <hr className="flex-grow border-gray-600" />
                        <h3 className="px-4 text-center font-medium">弹窗设置</h3>
                        <hr className="flex-grow border-gray-600" />
                    </div>
                    <div>
                        <span>时间区间*</span>
                        <Space className="ml-2">
                            <DatePicker
                                showTime
                                placeholder="开始时间"
                                onChange={(date: any) => handleDateChange(date, 'start')}
                                defaultValue={startDate}
                            />
                            <span>至</span>
                            <DatePicker
                                showTime
                                placeholder="结束时间"
                                onChange={(date: any) => handleDateChange(date, 'end')}
                                defaultValue={endDate}
                                disabledDate={(current: any) =>
                                    startDate ? current < startDate : false
                                }
                            />
                        </Space>
                    </div>

                    <div>
                    位置* {positionList.map((item: any) => {
                            return <label className='ml-2 mr-2 align-middle' key={item.key}><input className='mr-0.5' name='position' type="radio" value={item.key} checked={position === item.key} onChange={(e) => setPosition(e.target.value)} />{item.display}</label>
                        })}
                    </div>

                    <div>
                    平台* {platformList.map((item: any) => {
                        if(positionAllowedPlatform[position]?.includes(item.key)) {
                            return <label className='ml-2 mr-2 align-middle' key={item.key}><input className='mr-0.5' name='chat_platform' type="checkbox" value={item.key} onChange={(e) => {
                                if (e.target.checked) {
                                    setChatPlatform([...chatPlatform, e.target.value]);
                                } else {
                                    setChatPlatform(chatPlatform.filter((platform: any) => platform !== e.target.value));
                                }
                            }} defaultChecked={anouncementData?.chat_platform?.includes(item.key)} />{item.display}</label>
                        } else {
                            return null;
                        }
                        })}
                    </div>
                    
                    <div>
                    展示频率* {showPeriodList.map((item: any) => {
                            return <label className='ml-2 mr-2 align-middle' key={item.key}><input className='mr-0.5' name='show_period' type="radio" value={item.key} checked={showPeriod === item.key} onChange={(e) => setShowPeriod(e.target.value)} />{item.display}</label>
                        })}
                    </div>
                    <div>
                        用户范围* {userScopeList.map((item: any) => {
                            return <label className='ml-2 mr-2 align-middle' key={item.key}><input className='mr-0.5' name='user_scopes' type="checkbox" value={item.key} onChange={(e) => {
                                if (e.target.checked) {
                                    setUserScopes([...userScopes, e.target.value]);
                                } else {
                                    setUserScopes(userScopes.filter((scope: any) => scope !== e.target.value));
                                }
                            }} defaultChecked={anouncementData?.user_scopes?.includes(item.key)} />{item.display}</label>
                        })}
                    </div>
                    <div className='px-2 py-2 flex flex-row-reverse'>
                        <button
                            type="button"
                            className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'
                            onClick={handleSave}
                            disabled={loading}
                        >
                            {loading ? '处理中...' : anouncementData?.id ? '更新' : '保存'}
                        </button>
                        <button className='p-2 px-5 dark:bg-gray-800 bg-gray-300 rounded text-sm ' onClick={onClose}>取消</button>
                    </div>
                    

                </div>
            </div>
        </Modal>
    )
}

export default Anouncement