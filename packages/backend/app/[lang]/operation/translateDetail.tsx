import format from '@little-tavern/shared/src/module/formate-date'
import { useEffect, useRef, useState } from 'react';
import Toast from '@share/src/ui/toast';
import useRequest from '../hook/useRequest'
import { set } from 'lodash';
import Modal from "@share/src/ui/dialog/Modal"
import { DatePicker, Space, Pagination } from 'antd'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import { useForm } from 'react-hook-form';

const TranslateDetail = ({ onClose, resourceData ,refresh}: any) => {
  const request = useRequest()
  const id = resourceData?.id
  const zhCnRef = useRef<HTMLTextAreaElement>(resourceData?.zh_cn)
  const zhTwRef = useRef<HTMLTextAreaElement>(resourceData?.zh_tw)
  const enUsRef = useRef<HTMLTextAreaElement>(resourceData?.en_us)
  const [resourceForm, setResourceForm] = useState<any>(resourceData)

  useEffect(() => {
    if (resourceData) {
      if (zhCnRef.current) {
        zhCnRef.current.value = resourceData?.zh_cn
      }
      if (zhTwRef.current) {
        zhTwRef.current.value = resourceData?.zh_tw
      }
      if (enUsRef.current) {
        enUsRef.current.value = resourceData?.en_us
      }
    }
  }, [resourceData]);

  const reviewSave = async () => {
    try {
      const res = await request('/translate/resource/review', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          id: id,
          zh_cn: zhCnRef.current?.value,
          en_us: enUsRef.current?.value,
          zh_tw: zhTwRef.current?.value,
        })
      })
      if (res.error_code === 0) {
        Toast.notify({
          type: 'success',
          message: '操作成功'
        })
        onClose && onClose()
        refresh && refresh()
      }
      else {
        Toast.notify({
          type: 'error',
          message: res.message
        })
      }

    }
    catch (e) {
      console.log(e)
      Toast.notify({
        type: 'error',
        message: '操作失败'
      })
    }
  }
  const translate_retry = async () => {
    try {
      Toast.showLoading('');
      const id = resourceData?.id
      const res = await request(`/translate/resource/retry?id=${id}`)
      if (res.error_code === 0) {
        Toast.notify({
          type: 'success',
          message: '操作成功'
        })
        setResourceForm(res.data.resource)
        zhCnRef.current.value = res.data.resource.zh_cn
        zhTwRef.current.value = res.data.resource.zh_tw
        enUsRef.current.value = res.data.resource.en_us 
      } else {
        Toast.notify({
          type: 'error',
          message: res.message
        })
      }
      Toast.hideLoading()
    }
    catch (e) {
      console.log(e)
      Toast.notify({
        type: 'error',
        message: '操作失败'
      })
    }
  }
  return (
    <Modal isOpen={true} onClose={onClose} className='!max-w-4xl'>
      <div className='mb-5'>
        <h3 className='mt-5 mb-2'>翻译内容</h3>
        <div className='space-y-3'>
          <div className='flex items-center'>
            原文
            <textarea
              id="text"
              defaultValue={resourceForm?.text}
              autoComplete="text"
              readOnly
              className="ml-2 rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 w-52 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 mr-1 w-[700px]"
            />
          </div>
          <div className='flex items-center'>
            <span>中文</span>
            <textarea
              defaultValue={resourceForm?.zh_cn}
              id="zh_cn"
              autoComplete="zh_cn"
              ref={zhCnRef}
              required
              rows={5}
              className="ml-2 rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 w-52 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 mr-1 w-[700px]"
            />
          </div>
          <div className='flex items-center'>
            <span>繁体</span>
            <textarea
              defaultValue={resourceForm?.zh_tw}
              id="zh_tw"
              autoComplete="zh_tw"
              ref={zhTwRef}
              required
              rows={5}
              className="ml-2 rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 w-52 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 mr-1 w-[700px]"
            />
          </div>
          <div className='flex items-center'>
            <span>英文</span>
            <textarea
              defaultValue={resourceForm?.en_us}
              id="en_us"
              autoComplete="en_us"
              ref={enUsRef}
              required
              rows={5}
              className="ml-2 rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 w-52 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 mr-1 w-[700px]"
            />
          </div>
        </div>
        <div className='flex justify-center mt-5'>
          <button className="mr-2 px-3 py-1 text-sm bg-purple-500 hover:bg-purple-600 text-white rounded transition-colors whitespace-nowrap" onClick={translate_retry}>重翻译</button>
          <button className="ml-2 px-3 py-1 text-sm bg-purple-500 hover:bg-purple-600 text-white rounded transition-colors whitespace-nowrap" onClick={reviewSave}>确认</button>
        </div>
      </div>
    </Modal>
  )
}
export default TranslateDetail