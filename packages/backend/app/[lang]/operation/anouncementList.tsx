import format from '@little-tavern/shared/src/module/formate-date'
import Anouncement from './anouncement';
import { useEffect, useState } from 'react';
import Toast from '@share/src/ui/toast';
import useRequest from '../hook/useRequest'
import { set } from 'lodash';

const AnouncementList = () => {
  const [showAnouncement, setShowAnouncement] = useState(false)
  const [platformList, setPlatformList] = useState([])
  const request = useRequest();
  const [list, setList] = useState([])
  const [positionList, setPositionList] = useState([])
  const [showPeriodList, setShowPeriodList] = useState([])
  const [anouncementData, setAnouncementData] = useState({})
  const [positionAllowedPlatform, setPositionAllowedPlatform] = useState({})
  const [actionTypeList, setActionTypeList] = useState([])
  const [userScopes, setUserScopes] = useState([])

  const createAnouncement = () => {
    console.log('createAnouncement')
    setAnouncementData({})
    setShowAnouncement(true)
  }
  const reflesh = async () => {
    console.log('reflesh')
    getAlertList()
  }
  const getAlertList = async () => {
    Toast.showLoading('');
    const res = await request(`/operation/popup/list`);
    Toast.hideLoading();
    if (res.error_code === 0) {
      setList(res.data.list)
      setPlatformList(res.data.chat_platform_list)
      setPositionList(res.data.position_list)
      setShowPeriodList(res.data.show_period_list)
      setPositionAllowedPlatform(res.data.position_allowed_platform)
      setActionTypeList(res.data.action_type_list)
      setUserScopes(res.data.user_scope_list)
    }
  }
  useEffect(() => {
    getAlertList()
  }, [])
  const publish = async (id: string, published: boolean) => {
    try {
      Toast.showLoading('');
      const res = await request(`/operation/popup/update_published?popup_id=${id}&published=${published}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      if (res.error_code === 0) {
        Toast.notify({
          type: 'success',
          message: '操作成功'
        })
        reflesh && reflesh()
      } else {
        Toast.notify({
          type: 'error',
          message: res.message
        })
      }
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: '发布失败'
      })
    } finally {
      Toast.hideLoading()
    }
  }
  return <div className='mt-2'>
    <button onClick={createAnouncement} type='button' className='btn'>新增</button>
    <table className='dark:bg-gray-900 bg-white text-gray-500 text-sm border-collapse mt-2'>
      <thead className='dark:bg-slate-700 bg-slate-300 dark:text-slate-300'>
        <tr className='text-center text-xs sm:text-sm'>
          <th className='border py-2 px-1 border-slate-600'>序号</th>
          <th className='border py-2 px-4 border-slate-600'>平台</th>
          <th className='border py-2 px-4 border-slate-600'>弹窗标题</th>
          <th className='border py-2 px-4 border-slate-600'>弹窗内容</th>
          <th className='border py-2 px-3 border-slate-600'>弹窗有效期</th>
          <th className='border py-2 px-3 border-slate-600'>弹窗创建时间</th>
          <th className='border py-2 px-3 border-slate-600'>操作</th>
        </tr>
      </thead>
      <tbody>
        {list?.map((anouncement: any, index: number) => {
          return <tr key={index} className='ml-3 my-1 border-1 text-gray-400'>
            <td className='border border-slate-700 py-1 px-1'>{anouncement?.id}</td>
            <td className='border border-slate-700 py-1 px-1'>{anouncement?.chat_platform?.join(', ')}</td>
            <td className='border border-slate-700 py-1 px-1'>{anouncement?.title}</td>
            <td className='border border-slate-700 py-1 px-1'>{anouncement?.content}</td>
            <td className='border border-slate-700 py-1 px-1'>{format(anouncement?.start_at * 1000 || 0, 'YYYY-MM-DD HH:mm:ss')} - {format(anouncement?.end_at * 1000 || 0, 'YYYY-MM-DD HH:mm:ss')}</td>
            <td className='border border-slate-700 py-1 px-1'>{format(anouncement?.created_at * 1000 || 0, 'YYYY-MM-DD HH:mm:ss')}</td>
            <td className='border border-slate-700 py-1 px-1'>
              <div className='flex flex-wrap gap-1'>
                <button type='button' className='p-1 px-3 bg-sky-500 text-white rounded text-sm' onClick={() => {
                  setAnouncementData(anouncement)
                  setShowAnouncement(true)
                }}>编辑</button>
                {anouncement?.published? <button type='button' disabled className='p-1 px-3 bg-gray-500 text-white rounded text-sm cursor-not-allowed'>已发布</button> : <button type='button' className='p-1 px-3 bg-green-500 text-white rounded text-sm' onClick={() => { publish(anouncement?.id, true) }}>发布</button>}
                {anouncement?.published? <button type='button' className='p-1 px-3 bg-red-500 text-white rounded text-sm' onClick={() => { publish(anouncement?.id, false) }}>终止</button> : null}
              </div>
            </td>
          </tr>
        })}
      </tbody>
    </table>
    {showAnouncement && <Anouncement anouncementData={anouncementData} onClose={() => setShowAnouncement(false)} reflesh={reflesh} platformList={platformList} positionList={positionList} showPeriodList={showPeriodList} positionAllowedPlatform={positionAllowedPlatform} actionTypeList={actionTypeList} userScopeList={userScopes} />}
  </div>
}

export default AnouncementList