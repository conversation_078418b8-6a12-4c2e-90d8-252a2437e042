import format from '@little-tavern/shared/src/module/formate-date'
import Anouncement from './anouncement';
import { useEffect, useState } from 'react';
import Toast from '@share/src/ui/toast';
import useRequest from '../hook/useRequest'
import { set } from 'lodash';
import TranslateDetail from './translateDetail';

const TranslateList = () => {
  const request = useRequest();
  const [categories, setCategories] = useState([])
  const [status, setStatus] = useState([])
  const [resourceList, setResourceList] = useState([])
  const [resourceData, setResourceData] = useState({})
  const [showResource, setShowResource] = useState(false)
  const [selectCategory, setSelectCategory] = useState('全部')
  const [selectStatus, setSelectStatus] = useState('finished')
  const [selectKeywords, setSelectKeywords] = useState('')
  const [selectReview, setSelectReview] = useState('false')


  const getConfigs = async () => {
    const res = await request(`/translate/resource/configs`);
    if (res.error_code === 0) {
      let categories = res.data.category_list
      categories = ["全部", ...categories]
      setCategories(categories)
      setStatus(res.data.status_list)
      setSelectCategory(categories[0])
      setSelectStatus(res.data.status_list[0])
      setSelectKeywords('')
      setSelectReview('false')
    }
  }
  const searchList = async (selectCategory: string | undefined, selectStatus: string | undefined, selectKeywords: string | undefined, selectReview: string | undefined) => {
    let url = `/translate/resource/list?`
    url += 'category=' + selectCategory
    url += '&status=' + selectStatus
    url += '&keywords=' + selectKeywords
    url += '&review=' + selectReview
    const res = await request(url, {
      method: 'GET',
    })
    if (res.error_code === 0) {
      setResourceList(res.data.resource_list)
    } else {
      Toast.notify({
        type: 'error',
        message: res.message
      })
    }
  }
  const refresh = async () => {
    await searchList(selectCategory, selectStatus, selectKeywords, selectReview);
  }
  useEffect(() => {
    getConfigs()  // Changed from getAlertList() to getConfigs()
    searchList(selectCategory, selectStatus, selectKeywords, selectReview);
  }, [])
  const changeCategory = async (e: any) => {
    const value = e.target.value
    setSelectCategory(value)
    await searchList(value, selectStatus, selectKeywords, selectReview);
  }
  const changeStatus = async (e: any) => {
    const value = e.target.value
    setSelectStatus(value)
    await searchList(selectCategory, value, selectKeywords, selectReview);
  }
  const changeKeywords = async (e: any) => {
    const value = e.target.value
    setSelectKeywords(value)
    await searchList(selectCategory, selectStatus, value, selectReview);
  }
  const changeReview = async (e: any) => {
    const value = e.target.value
    await setSelectReview(value)
    await searchList(selectCategory, selectStatus, selectKeywords, value);
  }
  return <div className='mt-2'>
    <h2 className='text-xl'>翻译模块</h2>
    <div className='mt-3'>
      <h3 className='mb-2'>翻译查询</h3>
      {categories.length > 0 && <select name="category" className='w-48 bg-gray-800 p-1 mr-2' onChange={changeCategory}>
        {categories.map((item: any) => {
          return <option className='w-24 p-1' key={item} value={item}>
            {item}
          </option>
        })}</select>
      }
      {status.length > 0 && <select name="status" className='w-48 bg-gray-800 p-1 mr-2' onChange={changeStatus}>
        {status.map((item: any) => {
          return <option className='w-24 p-1' key={item} value={item}>
            {item}
          </option>
        })}</select>
      }

      关键词搜索 <input name='keywords' type="text" className='w-16 bg-gray-800 mx-3' placeholder='' onChange={changeKeywords} />
      手工确认 <select name="review" className='w-48 bg-gray-800 p-1 mr-2' onChange={changeReview}>
        <option className='w-24 p-1' key='false' value='false'>
          未确认
        </option>
        <option className='w-24 p-1' key='true' value='true'>
          已确认
        </option>
      </select>

    </div>
    <table className='dark:bg-gray-900 bg-white text-gray-500 text-sm border-collapse mt-2'>
      <thead className='dark:bg-slate-700 bg-slate-300 dark:text-slate-300'>
        <tr className='text-center text-xs sm:text-sm'>
          <th className='border py-2 px-1 border-slate-600'>序号</th>
          <th className='border py-2 px-4 border-slate-600'>创建时间</th>
          <th className='border py-2 px-4 border-slate-600'>标识key</th>
          <th className='border py-2 px-4 border-slate-600'>Text</th>
          <th className='border py-2 px-3 border-slate-600'>状态</th>
          <th className='border py-2 px-3 border-slate-600'>操作</th>
        </tr>
      </thead>
      <tbody>
        {
          resourceList?.map((resource: any) => {
            return <tr className='text-center text-xs sm:text-sm' key={resource.id}>
              <td className='border border-slate-700 py-1 px-1' key={resource.id}>{resource.id}</td>
              <td className='border border-slate-700 py-1 px-1'>{resource.created_at}</td>
              <td className='border border-slate-700 py-1 px-1'>{resource.key}</td>
              <td className='border border-slate-700 py-1 px-1'>{resource.text}</td>
              <td className='border border-slate-700 py-1 px-1'>{resource.status}</td>
              <td className='border border-slate-700 py-1 px-1'>
                <button onClick={() => {
                  setResourceData(resource)
                  setShowResource(true)
                }} className='bg-blue-500 text-white rounded p-1'>查看详情</button>
              </td>
            </tr>
          })
        }
      </tbody>
    </table>
    {showResource && <TranslateDetail resourceData={resourceData} onClose={() => setShowResource(false)} refresh={refresh} />}
  </div>
}

export default TranslateList