import Modal from "@share/src/ui/dialog/Modal"
import { useState, useEffect, useRef } from 'react'
import useRequest from "../hook/useRequest"
import Toast from "@share/src/ui/toast"
import { DatePicker, Space, Pagination } from 'antd'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import AutoTextareaHeight from '@share/src/ui/dialog/CreateCard/AutoTextareaHeight';
import cn from 'classnames';

const ipt = 'mt-1 block rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6 dark:bg-gray-800 dark:text-white flex-1 px-3 w-24'

const CreateActivity = ({ onClose, reflesh, isUpdate, season, seasonId }: any) => {
    const request = useRequest()
    const [loading, setLoading] = useState(false)
    const titleRef = useRef<any>(null)
    const [startDate, setStartDate] = useState<Dayjs | null>(season?.start_at? dayjs.unix(season?.start_at) : (() => dayjs()))
    const [endDate, setEndDate] = useState<Dayjs | null>(season?.end_at? dayjs.unix(season?.end_at) : (() => dayjs()))
    const [warmingUpStartDate, setWarmingUpStartDate] = useState<Dayjs | null>(season?.warming_up_start_at? dayjs.unix(season?.warming_up_start_at) : (() => dayjs()))
    const [warmingUpEndDate, setWarmingUpEndDate] = useState<Dayjs | null>(season?.warming_up_end_at? dayjs.unix(season?.warming_up_end_at) : (() => dayjs()))
    const [warmUpContent, setWarmUpContent] = useState(season?.warming_up_notice || '')
    const [warmUpFrequency, setWarmUpFrequency] = useState(season?.warming_up_notice_method?.interval || '')
    const [warmUpFrequencyMethod, setWarmUpFrequencyMethod] = useState(season?.warming_up_notice_method?.method || '')
    const handleActive = async () => {
        const activeTitle = titleRef?.current.value;
        if (!activeTitle || !startDate || !endDate || !warmingUpStartDate || !warmingUpEndDate || !warmUpContent || !warmUpFrequency || !warmUpFrequencyMethod) {   
            Toast.notify({
                type: 'info',
                message: '请输入必填项'
            })
            return
        }
        if (warmUpContent.length > 1000) {
            Toast.notify({
                type: 'info',
                message: '最多输入1000个字'
            })
            return
        }
        try {
            setLoading(true)
            const res = await request(isUpdate ? '/activity_diamond/season_update' : '/activity_diamond/season_create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    season_id: seasonId,
                    title: activeTitle,
                    start_at: startDate?.unix(),
                    end_at: endDate?.unix(),
                    warming_up_start_at: warmingUpStartDate?.unix(),
                    warming_up_end_at: warmingUpEndDate?.unix(),
                    warming_up_notice: warmUpContent,
                    warming_up_notice_method: {
                        method: warmUpFrequencyMethod,
                        interval: Number(warmUpFrequency)
                    }
                })
            })
            if (res.error_code === 0) {
                Toast.notify({
                    type: 'success',
                    message: `${isUpdate ? '更新' : '创建'}赛季成功`
                })
                reflesh && reflesh()
                onClose && onClose()
            } else {
                Toast.notify({
                    type: 'error',
                    message: res.message
                })
            }
        } catch (e) {
            Toast.notify({
                type: 'error',
                message: `${isUpdate ? '更新' : '创建'}赛季失败`
            })
        } finally {
            setLoading(false)
        }
    }
    const handleDateChange = (dates: any, dateType: 'start' | 'end' | 'warming_up_start_at' | 'warming_up_end_at') => {
        switch (dateType) {
            case 'start':
                setStartDate(dates)
                break
            case 'end':
                setEndDate(dates)
                break
            case 'warming_up_start_at':
                setWarmingUpStartDate(dates)
                break
            case 'warming_up_end_at':
                setWarmingUpEndDate(dates)
                break
        }
    }
    const handleWarmUpFrequencyMethodChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setWarmUpFrequencyMethod(Number(e.target.value))
    }
 
    return (
        <Modal isOpen={true} onClose={onClose} className='!max-w-4xl'>
            <div className='mb-5'>
                <h3 className='mt-5 mb-2'>新增赛季</h3>
                <div className='space-y-3'>
                    <div className='flex items-center'>
                        标题*
                        <input
                            type="text" ref={titleRef}
                            id="title"
                            defaultValue={season?.title || ''}
                            autoComplete="title"
                            required
                            placeholder='最多输入15个字'
                            className="ml-2 rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 w-52 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 mr-1"
                        />
                    </div>
                    <div>
                        <span>时间区间*</span>
                        <Space className="ml-2">
                            <DatePicker
                                showTime
                                placeholder="开始时间"
                                onChange={(date: any) => handleDateChange(date, 'start')}
                                value={startDate}
                            />
                            <span>至</span>
                            <DatePicker
                                showTime
                                placeholder="结束时间"
                                onChange={(date: any) => handleDateChange(date, 'end')}
                                value={endDate}
                                disabledDate={(current: any) =>
                                    startDate ? current < startDate : false
                                }
                            />
                        </Space>
                    </div>
                    <div>
                        <span>预热时间*</span>
                        <Space className="ml-2">
                            <DatePicker
                                showTime
                                required
                                placeholder="开始时间"
                                onChange={(date: any) => handleDateChange(date, 'warming_up_start_at')}
                                value={warmingUpStartDate}
                            />
                            <span>至</span>
                            <DatePicker
                                showTime
                                required
                                placeholder="结束时间"
                                onChange={(date: any) => handleDateChange(date, 'warming_up_end_at')}
                                value={warmingUpEndDate}
                                disabledDate={(current: any) =>
                                    warmingUpStartDate ? current < warmingUpStartDate : false
                                }
                            />
                        </Space>
                    </div>
                    
                    <div className='mb-2 w-96'>
                        <div className='flex flex-wrap items-center space-x-2'>
                            <span className='text-sm'>预热内容*</span>
                        </div>
                        <AutoTextareaHeight
                            data-insert
                            rows={3}
                            required
                            defaultValue={warmUpContent || ''}
                            className={`flex-1 dark:bg-gray-800 w-full`}
                            placeholder='最多输入1000个字'
                            onChange={(e: any) => (
                                setWarmUpContent(e.target.value)
                            )}
                        />
                    </div>

                    <div className='flex gap-x-2 items-center'>
                        <span className="text-sm">选择预热内容发送频次类型*</span>
                        <label className='ml-2 mr-2 align-middle'><input className='mr-0.5' id='warm_up_frequency1' type="radio" value={1} checked={warmUpFrequencyMethod === 1} name={'warmUpFrequencyMethod'} onChange={handleWarmUpFrequencyMethodChange} required />按时间</label>
                        <label className='mr-2 align-middle'><input className='mr-0.5' id='warm_up_frequency2' type="radio" value={2} checked={warmUpFrequencyMethod === 2} name={'warmUpFrequencyMethod'} onChange={handleWarmUpFrequencyMethodChange} required />按次数</label>
                    </div>
                    <div>
                    {warmUpFrequencyMethod === 1 && <div className='flex gap-x-2 items-center w-96'>
                      <span className="text-sm">预热内容发送频次一*</span>
                      <input
                        type="text"
                        placeholder="请直接输入正整数"
                        name="warm_up_num_frequency"
                        className={cn(ipt, '!w-6')}
                        defaultValue={warmUpFrequency}
                        onChange={(e: any) => setWarmUpFrequency(e.target.value)}
                        required
                      />
                      <span>单位：分钟</span>
                    </div>}
                    {warmUpFrequencyMethod === 2 && <div className='flex gap-x-2 items-center w-96'>
                      <span className="text-sm">预热内容发送频次二*</span>
                      <input
                        type="text"
                        placeholder="请直接输入正整数"
                        name="warm_up_num_frequency"
                        className={cn(ipt, '!w-6')}
                        defaultValue={warmUpFrequency}
                        onChange={(e: any) => setWarmUpFrequency(e.target.value)}
                        required
                      />
                      <span>单位：条</span>
                    </div>}
                    </div>
                    <div className='px-2 py-2 flex flex-row-reverse'>
                        <button
                            type="button"
                            className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'
                            onClick={handleActive}
                            disabled={loading}
                        >
                            {loading ? '处理中...' : isUpdate ? '更新' : '创建'}
                        </button>
                        <button className='p-2 px-5 dark:bg-gray-800 bg-gray-300 rounded text-sm ' onClick={onClose}>取消</button>
                    </div>

                </div>
            </div>
        </Modal>
    )
}

export default CreateActivity