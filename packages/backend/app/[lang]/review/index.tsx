'use client'
import React, { useCallback, useContext, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useParams } from 'next/navigation'
import Card from '@little-tavern/shared/src/ui/card'
import s from '@backend/app/[lang]/globals.module.css'
import Toast from '@little-tavern/shared/src/ui/toast'
import { AuthContext } from '../components/authContext'
import useRequest from '@backend/app/[lang]/hook/useRequest'
import Loader from '@little-tavern/shared/src/ui/Loading';
import {base64ToBlob} from '@little-tavern/shared/src/module/base64ToBlob'
import TabSwitcher from '../components/TabSwitcher'
import type { RoleType } from '@little-tavern/shared/src/ui/card';
import Review from '@little-tavern/shared/src/ui/dialog/CreateCard/review'
import ReactDiffViewer from 'react-diff-viewer';
import Modal from '@little-tavern/shared/src/ui/dialog/Modal';
import './style.css'
import useReview from '@share/src/hook/useReview'
import Image from 'next/image'

const btn = cn(s.primBtn, 'mx-2 text-sm')

const Pay = () => {
  const { t } = useTranslation()
  const params = useParams()
  const lang = params.lang as string
  const [roles, setRoles] = useState<any>([])
  const [uidRejectCountMap, setUidRejectCountMap] = useState<any>({})
  const auth = useContext(AuthContext);
  const isLogin = auth?.isLogin;
  const request = useRequest();
  const [loading, setIsLoading] = useState(true);
  const [subTagList, setSubTagList] = useState([])
  const [showReview, setShowReview] = useState(false);
  const [role, setRole] = useState({});
  const [isShowDiff, setIsShowDiff] = useState(false)
  const [isShowVersion, setIsShowVersion] = useState(false)
  const [versionList, setVersioinList] = useState<any>([])
  const [auditData, setAuditData] = useState<any>({})
  const review = useReview();

  const reflesh = () => {
    fetchRoles();
  }
  const fetchRoles = useCallback(async () => {
    const res = await request('/roles/auditing/filter_list');
    console.log('res', res);
    setRoles(res.ret_list || [])
    setUidRejectCountMap(res.uid_reject_count_map || {})
    setIsLoading(false);
  }, [])
  
  useEffect(() => {
    fetchRoles();
  }, [])

  const fetchVersionList = async (modeType: string, id: number) => {
    const res = await request(`/roles/publish/list?mode_type=${modeType}&mode_target_id=${id}`);
    if(res.error_code === 0) {
      setVersioinList(res.data?.publish_list)
      setAuditData(modeType === 'group'? res.data?.audit_detail.group : res.data?.audit_detail.role)
      setIsShowVersion(true);
    } else {
      Toast.notify({
        type: 'error',
        message: ''
      })
    }
  }

  const [oldVal, setOldVal] = useState<any>({})
  const [isGroup, setIsGroup] = useState(false)
  const showDiff = async (data: any, modeType: string, id: number) => {
      setIsShowDiff(true)
      setIsShowVersion(false)
      setOldVal(data)
      setIsGroup(modeType === 'group'? true : false)
      console.log(data, auditData)
  }
  const viewCard = async (role: any, modeType: string) => {
    const res = await review.show({msg: {...role, modeType: modeType, isEdit: true}});
    if(res) {
        reflesh();
    }
  }

  return (
    <>
      {<main className="main-height bg-black w-full con-width px-3 pt-6">
        <h2 className='text-xl'>{roles.length == 0? '暂无审核卡片' : '待审核列表'}</h2>
        {loading? <Loader/> : <div className='px-2 pt-2 pb-2 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2'>
          {roles.map((role: any, index: number) => {
            const _role = role.mode_type == 'group'? role.group : role.role
            return <div  key={index} className='mb-2 relative z-0 inline-block'>
              <div className='absolute right-[1px] top-[1px] rounded top-[1px] z-10 flex text-xs items-center bg-gray-900/50 px-1 py-0.5 text-blue-500'>
                {<span className='mx-1 inline-block text-center'>{`id: ${role.mode_target_id}`}</span>}
                {role?.submit_version && <span>版本: {role?.submit_version}</span>}
              </div>
              <Card role={{..._role,  admin: true}} reflesh={reflesh} modeType={role.mode_type} subTagList={subTagList}></Card>
              <div className='absolute left-28 bottom-14 rounded z-10 text-xs items-center bg-gray-900/50 px-1 py-0.5 text-blue-500'>
                <div>该用户ID {_role.author_id}</div>
                <div>累计被驳回{uidRejectCountMap[_role.author_id || 0]}次</div>
              </div>
              <div className='text-center'>
                <button className='mt-1 px-6 mb-3 py-1 rounded-lg px-5 bg-gray-800 text-white mr-2' onClick={() => {
                  window.open(`https://playai8.grafana.net/d/de086o0bsy0owc/df92f35?var-user_id=${_role.author_id || 0}&var-role_id=${role.mode_target_id}&var-group_id=${role.mode_target_id}
`)
                }}>查看聊天数据</button>
                <button className='mt-1 px-6 mb-3 py-1 rounded-lg px-5 bg-gray-800 text-white mr-2' onClick={() => {
                  fetchVersionList(role.mode_type, role.mode_target_id)
                 }}>对比</button>
                <button className='mt-1 px-6 mb-3 py-1 rounded-lg bg-purple-500 text-white' onClick={() => {viewCard(_role, role.mode_type)}}>审核</button>
              </div>
            </div>
          })
          }
        </div>}
      </main>}
      {loading && <Loader />}
      <Modal onClose={() => { setIsShowDiff(false) }} isOpen={isShowDiff} conWidth='sm:w-[1200px]'>
        <div className='pt-6  pb-12 h-[90vh] overflow-auto'>
          <div>
            <div>封面图</div>
            <div className='flex'>
              <div className='w-1/2'><Image src={oldVal?.role_avatar} alt='' width={100} height={100} /></div>
              <div className='w-1/2'><Image src={auditData?.role_avatar} alt='' width={100} height={100} /></div>
            </div>
          </div>
          {
            isGroup? <>
            <div className='mt-2 mb-0.5'>群聊名字：</div>
            <ReactDiffViewer oldValue={oldVal?.name} newValue={auditData?.name} splitView={true} showDiffOnly={false} />
            <div className='mt-2 mb-0.5'>群聊介绍：</div>
              <ReactDiffViewer oldValue={oldVal?.introduction} newValue={auditData?.introduction} splitView={true} showDiffOnly={false} />
            <div className='mt-2 mb-0.5'>初始场景：</div>
            <ReactDiffViewer oldValue={oldVal?.scenario} newValue={auditData?.scenario} splitView={true} showDiffOnly={false} />
            <div className='mt-2 mb-0.5'>营销文案：</div>
            <ReactDiffViewer oldValue={oldVal?.simple_intro} newValue={auditData?.simple_intro} splitView={true} showDiffOnly={false} />
            <div className='mt-2 mb-0.5'>是否显示作者名称：</div>
              <ReactDiffViewer oldValue={String(oldVal?.author)} newValue={String(auditData?.author)} splitView={true} showDiffOnly={false} />
              <div className='mt-2 mb-0.5'>标签：</div>
              <ReactDiffViewer oldValue={String(oldVal?.sub_tags)} newValue={String(auditData?.sub_tags)} splitView={true} showDiffOnly={false} />
            </> : <>
              <div className='mt-2 mb-0.5'>卡片名字：</div>
              <ReactDiffViewer oldValue={oldVal?.card_name} newValue={auditData?.card_name} splitView={true} showDiffOnly={false} />
              <div className='mt-2 mb-0.5'>卡片介绍：</div>
              <ReactDiffViewer oldValue={oldVal?.introduction} newValue={auditData?.introduction} splitView={true} showDiffOnly={false} />
              <div className='mt-2 mb-0.5'>营销文案：</div>
              <ReactDiffViewer oldValue={oldVal?.simple_intro} newValue={auditData?.simple_intro} splitView={true} showDiffOnly={false} />
              <div className='mt-2 mb-0.5'>是否显示作者名称：</div>
              <ReactDiffViewer oldValue={String(oldVal?.author)} newValue={String(auditData?.author)} splitView={true} showDiffOnly={false} />
              <div className='mt-2 mb-0.5'>声音：</div>
              <ReactDiffViewer oldValue={oldVal?.speaker_id} newValue={auditData?.speaker_id} splitView={true} showDiffOnly={false} />
              <div className='mt-2 mb-0.5'>标签：</div>
              <ReactDiffViewer oldValue={String(oldVal?.sub_tags)} newValue={String(auditData?.sub_tags)} splitView={true} showDiffOnly={false} />
              <div className='mt-2 mb-0.5'>AI角色名：</div>
              <ReactDiffViewer oldValue={oldVal?.name} newValue={auditData?.name} splitView={true} showDiffOnly={false} />
              <div className='mt-2 mb-0.5'>用戶角色名：</div>
              <ReactDiffViewer oldValue={oldVal?.user_role_name} newValue={auditData?.user_role_name} splitView={true} showDiffOnly={false} />
              <div className='mt-2 mb-0.5'>NSFW：</div>
              <ReactDiffViewer oldValue={String(oldVal.nsfw)} newValue={String(auditData.nsfw)} splitView={true} showDiffOnly={false} />
              <div className='mt-2 mb-0.5'>回复模式：</div>
              <ReactDiffViewer oldValue={oldVal?.chat_type} newValue={auditData?.chat_type} splitView={true} showDiffOnly={false} />
              <div className='mt-2 mb-0.5'>角色定义：</div>
              <ReactDiffViewer oldValue={oldVal?.description} newValue={auditData?.description} splitView={true} showDiffOnly={false} />
              <div className='mt-2 mb-0.5'>AI回复长度：</div>
              <ReactDiffViewer oldValue={String(oldVal.replay_len_ratio)} newValue={String(auditData.replay_len_ratio)} splitView={true} showDiffOnly={false} />
              <div className='mt-2 mb-0.5'>对话示例：</div>
              <ReactDiffViewer oldValue={JSON.stringify(oldVal?.muilte_examples)} newValue={JSON.stringify(auditData?.muilte_examples)} splitView={true} showDiffOnly={false} />
              <div className='mt-2 mb-0.5'>对话场景：</div>
              <ReactDiffViewer oldValue={JSON.stringify(oldVal?.muilte_scenes)} newValue={JSON.stringify(auditData?.muilte_scenes)} splitView={true} showDiffOnly={false} />
              <div className='mt-2 mb-0.5'>状态栏：</div>
              <h3 className='mt-1'>状态栏模版</h3>
              <ReactDiffViewer oldValue={JSON.stringify(oldVal?.status_block)} newValue={JSON.stringify(auditData?.status_block)} splitView={true} showDiffOnly={false} />
              <h3 className='mt-1'>初始状态栏内容</h3>
              <ReactDiffViewer oldValue={JSON.stringify(oldVal?.status_block_init)} newValue={JSON.stringify(auditData?.status_block_init)} splitView={true} showDiffOnly={false} />
              <h3 className='mt-1'>状态栏更新规则</h3>
              <ReactDiffViewer oldValue={JSON.stringify(oldVal?.status_block_rule)} newValue={JSON.stringify(auditData?.status_block_rule)} splitView={true} showDiffOnly={false} />
              <div className='mt-2 mb-0.5'>世界书：</div>
              <ReactDiffViewer oldValue={JSON.stringify(oldVal?.role_book)} newValue={JSON.stringify(auditData?.role_book)} splitView={true} showDiffOnly={false} />
              <div className='mt-2 mb-0.5'>适配模型：</div>
              <ReactDiffViewer oldValue={String(oldVal.support_product_ids)} newValue={String(auditData.support_product_ids)} splitView={true} showDiffOnly={false} />
            </>
          }
          
        </div>
      </Modal>
      <Modal onClose={() => {
        setIsShowVersion(false)
        }} isOpen={isShowVersion} conWidth='w-[800px]'>
        <>
          {versionList.length > 0? <div>
            {versionList.map((version: any) => {
              return <div key={version.publish_version}>
                <span className='text-gray-400'>版本：</span>
                <span>{version.publish_version}</span>
                <span className='ml-2 text-gray-400'> 发布时间：</span>
                <span> {version.published_at}</span>
                <button className='ml-2 mt-1 px-3 mb-3 py-0.5 rounded-lg bg-purple-500 text-white' onClick={() => {showDiff(version.publish_data, version.mode_type, version.mode_target_id)}}>对比版本</button>
                <button className='ml-2 mt-1 px-3 mb-3 py-0.5 rounded-lg bg-amber-500 text-white' onClick={() => {showDiff(version.original_data, version.mode_type, version.mode_target_id)}}>对比原始版本</button>
                <span className={cn('ml-2', version.status === 'rejected' ? 'text-red-500' : 'text-green-500')}>{version.status_desc}</span>
              </div>
            })}  
          </div> : <div className='text-center'>没有历史版本</div>}
        </>
      </Modal>
    </>
  )
}

export default React.memo(Pay)
