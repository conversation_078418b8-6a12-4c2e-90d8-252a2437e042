'use client'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import s from './style.module.css'
import cn from 'classnames'
import { useTranslation } from 'react-i18next'
import { useParams } from 'next/navigation'
import useRequest from '@backend/app/[lang]/hook/useRequest'
import { AuthContext } from '../components/authContext';
import { PencilSquareIcon } from '@heroicons/react/24/solid'
import { Switch } from '@headlessui/react'
import useAddReg from './useAddReg';
import useComfirm from '@little-tavern/shared/src/hook/useComfirm';
import Toast from '@little-tavern/shared/src/ui/toast';

export type IOperation = {
  id?: string,
  type: 'human' | 'ai' | 'img',
  avatar?: string,
  message_id?: string,
  version?: string,
  // 跟message_id一样，缓存判断是否重复请求
  imgId?: string,
  voice_url?: string,
  content: string,
  timestamp: number | null,
  isLoading?: boolean
}

const Operation = () => {
  const { t } = useTranslation()
  const params = useParams()
  const lang = params.lang as string
  const request = useRequest();
  const confirm = useComfirm();
  const [opt, setOpt] = useState<any>();

  const [regs, setRegs] = useState<any>([])
  const addReg = useAddReg();
  const queryTags = async () => {
    try {
      const res = await request(`/regex_all`)
      if(res?.all_regex.length > 0) {
        setRegs(res.all_regex)
      }
      setOpt({
        options: res.options,
        affects: res.affects
      });
    } catch(e) {
      console.log("e", e);
    }
  }
  useEffect(() => {
    queryTags();
  }, [])
  const addHandler = async () => {
    const res = await addReg.show({msg: {opt: opt}});
    console.log('res', res);
    if(res) {
      queryTags();
    }
  }
  const editHandler = async (reg: any) => {
    const res = await addReg.show({msg: {...reg, isEdit: true, opt: opt}});
    if(res) {
      queryTags();
    }
  }
  const toggleReg = async (reg: any) => {
    reg.enabled = !reg.enabled
    try {
      const res = await request('/updateReg', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(reg)
      })
      const newRegs = regs.map((_reg: any) => {
        if(_reg.rule_id == reg.rule_id) {
          _reg.enabled = reg.enabled
        }
        return _reg;
      })
      setRegs(newRegs)
      Toast.notify({
        type:'success',
        message: `操作成功`
      })
    } catch(e) {
      console.log("e", e);
    }
  }
  const delReg = async (id: string) => {
    const res = await confirm.show({});
    if (res.confirm) {
      try {
        const res = await request('/deleteReg?rule_id=' + id, {
          method: 'POST'
        })
        if(res.success) {
          queryTags();
          Toast.notify({
            type:'success',
            message: `删除成功`
          })
        } else {
          Toast.notify({
            type:'error',
            message: `删除失败`
          })
        }
      } catch(e) {
        console.log("e", e);
      }
    }
  }
  return (
    <>
     <main className="main-height bg-black con-width pt-4 px-3">
        <div>
        <h2 className='text-xl'>返回内容正则处理</h2>
          <div className='sm:w-full sm:max-w-[900px]'>
            <label htmlFor="name" className="block text-sm font-medium leading-6 ">
            正则列表：
            </label>
            <div className="mt-2">
              <ul className='flex gap-2 flex-wrap'>
              {regs.map((reg: any) => {
                return <li key={reg.rule_id} className='flex whitespace-nowrap border border-gray-400 px-2 py-1 rounded text-sm gap-1'>
                  <span className='text-gray-400'>{reg.title}</span>
                  <div className='flex ml-6 items-center'>
                    <Switch
                      checked={reg.enabled}
                      onChange={() => {}}
                      onClick={() => {toggleReg(reg)}}
                      className="group inline-flex h-3 w-5 items-center rounded-full bg-gray-500 transition data-[checked]:bg-blue-600"
                    >
                      <span className="size-2 translate-x-0.5 rounded-full bg-white transition group-data-[checked]:translate-x-2.5" />
                    </Switch>
                    <button onClick={() => {editHandler(reg)}} type='button' className='ml-1 px-1 text-xs text-gray-400 hover:text-gray-100 group'><PencilSquareIcon className="group-hover:text-gray-100 h-4 w-4 text-gray-500" aria-hidden="true" /></button>
                    <button onClick={() => {delReg(reg.rule_id)}} type='button' className='px-1 text-xs text-gray-400 hover:text-gray-100'><span className='inline-block w-2'>x</span></button>
                  </div>
                  </li>
              })}
              </ul>
            </div>
          </div>
          <div className='mt-5'>
            <button type='button' onClick={addHandler} className='p-2 px-5 bg-purple-500 text-white rounded text-sm'>添加</button>
          </div>
        </div>
      </main>
    </>
  )
}

export default Operation
