'use client'
import classNames from 'classnames'
import type { ReactEventHandler, ReactNode } from 'react'
import React, { useContext, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import AddReg from './AddReg'

type Iparam = {
  msg?: Record<string, any>
}
const useAddReg = () => {
  
  return {
    show: (msg: Iparam) => {
      let holder = document.createElement('div')
      holder.className = 'absolute z-50 w-full h-full left-0 top-0'
      document.body.appendChild(holder)
      const root = createRoot(holder)
      return new Promise<boolean>((resolve, reject) => {
        const onComfirm = (file: any) => {
          resolve(true)
          root.unmount();
          document.body.removeChild(holder);
        }
        const onCancel = () => {
          root.unmount();
          document.body.removeChild(holder);
          resolve(false);
        }
        root.render(<AddReg isOpen={true} onConfirm={onComfirm} onCancel={onCancel} msg={msg.msg} ></AddReg>);
      })
    },
    hide: () => {
      // if (holder) root.unmount();
    }
  }
}

export default useAddReg
