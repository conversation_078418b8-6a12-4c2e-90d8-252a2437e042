import Modal from "@share/src/ui/dialog/Modal"
import { useState, useEffect } from 'react'
import useRequest from "../hook/useRequest"
import Toast from "@share/src/ui/toast"

const DecreaseDiamon = ({ onClose, uid, balance, reflesh }: any) => {
    const request = useRequest()
    const [diamondAmount, setDiamondAmount] = useState('')
    const [loading, setLoading] = useState(false)

    const handleDecreaseDiamond = async () => {
        if (!diamondAmount || isNaN(Number(diamondAmount))) {
            Toast.notify({
                type: 'info',
                message: '请输入正确的钻石+金币数量'
            })
            return
        }

        try {
            setLoading(true)
            const res = await request(`/decrease_balance`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    id: uid,
                    amount: Number(diamondAmount)
                })
            });
            console.log('res', res)
            Toast.notify({
                type: 'success',
                message: '扣除钻石+金币成功'
            })
            reflesh();
        } catch (error: any) {
            Toast.notify({
                type: 'error',
                message: '扣除钻石+金币失败'
            })
        } finally {
            setLoading(false)
        }
    }

    return (
        <Modal isOpen={true} onClose={onClose} className='!max-w-4xl'>
            <div className='mb-5'>
                <h3 className='mt-5 mb-2'>扣钻石+金币</h3>
                <div className='space-y-3'>
                    <span>剩余钻石+金币：{balance}</span>
                    <div className='flex items-center'>
                        扣除钻石+金币：
                        <input 
                            type="text" 
                            placeholder="请输入扣除钻石+金币数量" 
                            value={diamondAmount}
                            onChange={(e) => setDiamondAmount(e.target.value)}
                            className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3"
                        />
                        <button 
                            type="button" 
                            className='ml-2 bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-4 rounded disabled:opacity-50' 
                            onClick={handleDecreaseDiamond}
                            disabled={loading}
                        >
                            {loading ? '处理中...' : '确定'}
                        </button>
                    </div>
                </div>
            </div>
        </Modal>
    )
}

export default DecreaseDiamon