import format from '@little-tavern/shared/src/module/formate-date'

const ellipseString = (str: string) => {
  if (!str) return '';
  if (str.length <= 16) {
    return <span className="text-red-500">{str}</span>;
  }
  return <><span className="text-red-500">{str.slice(0, 8)}</span>{str.slice(8, -8)}<span className="text-red-500">{str.slice(-8)}</span></>;
}

const UserUsdtChargeHistory = ({userUsdtChargeHistory}: any) => {
  console.log('usdtChargeHistory', userUsdtChargeHistory)
  return <>{userUsdtChargeHistory?.length > 0? <div className='mt-5'>
      <h3 className='mt-2 mb-2'>USDT充值历史</h3>
      <table className='dark:bg-gray-900 bg-white text-gray-500 text-sm border-collapse mt-2'>
        <thead className='dark:bg-slate-700 bg-slate-300 dark:text-slate-300'>
          <tr className='text-center text-xs sm:text-sm'>
            <th className='border py-2 px-1 border-slate-600'>id</th>
            <th className='border py-2 px-3 border-slate-600'>user_id</th>
            <th className='border py-2 px-4 border-slate-600'>final_fee</th>
            <th className='border py-2 px-4 border-slate-600'>status</th>
            <th className='border py-2 px-3 border-slate-600'>platform</th>
            <th className='border py-2 px-4 border-slate-600'>created_at</th>
          </tr>
        </thead>
        <tbody>
          {
            userUsdtChargeHistory?.map((item: any, index: number) => {
              return <tr key={index} className='ml-3 my-1 border-1 text-gray-400'>
                <td className='border border-slate-700 py-1 px-1'>{item?.id}</td>
                <td className='border border-slate-700 py-1 px-1'>{item?.user_id}</td>
                <td className='border border-slate-700 py-1 px-1'>{item?.final_fee / 100000}</td>
                <td className='border border-slate-700 py-1 px-1'>{item?.status}</td>
                <td className='border border-slate-700 py-1 px-1'>{item?.platform}</td>
                <td className='border border-slate-700 py-1 px-1'>{format(parseInt(item?.created_at || 0), 'YYYY-MM-DD HH:mm:ss')}</td>
                </tr>
          })
          }
        </tbody>
      </table></div> : <div className='mt-5'>
        <h3 className='mt-2 mb-2'>USDT充值历史</h3>
        <div className='text-gray-400'>暂无数据</div>
      </div>}
      </>
}

const UsdtChargeHistory = ({usdtChargeHistory}: any) => {
  console.log('usdtChargeHistory', usdtChargeHistory)
  const keys = Object.keys(usdtChargeHistory);
  return <div className='mt-5'>
    <h3 className='mt-2 mb-2'>USDT充值历史</h3>
    {keys.map((key: any, index: number) => {
      console.log('key', key)
      return <><h3 className='mt-2 mb-2' key={index}>{key}</h3>
      <table className='dark:bg-gray-900 bg-white text-gray-500 text-sm border-collapse mt-2'>
        <thead className='dark:bg-slate-700 bg-slate-300 dark:text-slate-300'>
          <tr className='text-center text-xs sm:text-sm'>
            <th className='border py-2 px-1 border-slate-600'>txId</th>
            <th className='border py-2 px-4 border-slate-600'>from</th>
            {/* <th className='border py-2 px-4 border-slate-600'>to</th> */}
            <th className='border py-2 px-4 border-slate-600'>amount</th>
            <th className='border py-2 px-3 border-slate-600'>symbol</th>
            <th className='border py-2 px-3 border-slate-600'>transactionTime</th>
            <th className='border py-2 px-3 border-slate-600'>chain</th>
          </tr>
        </thead>
        <tbody>
          {
            usdtChargeHistory[key]?.map((item: any, index: number) => {
              return <tr key={index} className='ml-3 my-1 border-1 text-gray-400'>
                <td className='border border-slate-700 py-1 px-1' title={item?.transaction_hash}>{ellipseString(item?.transaction_hash)}</td>
                <td className='border border-slate-700 py-1 px-1' title={item?.from_address}>{ellipseString(item?.from_address)}</td>
                {/* <td className='border border-slate-700 py-1 px-1' title={item?.to}>{ellipseString(item?.to)}</td> */}
                <td className='border border-slate-700 py-1 px-1'>{item?.value_decimal}</td>
                <td className='border border-slate-700 py-1 px-1'>{item?.token_symbol}</td>
                <td className='border border-slate-700 py-1 px-1'>{format(new Date(item?.block_timestamp || '').getTime(), 'YYYY-MM-DD HH:mm:ss')}</td>
                <td className='border border-slate-700 py-1 px-1'>{item?.chain}</td>
                </tr>
          })
          }
        </tbody>
      </table></>
    })}
</div>
}
export {
  UserUsdtChargeHistory
}
export default UsdtChargeHistory