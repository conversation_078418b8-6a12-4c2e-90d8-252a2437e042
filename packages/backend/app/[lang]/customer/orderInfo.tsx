import format from '@little-tavern/shared/src/module/formate-date'

const OrderInfo = ({orderInfo}: any) => {
    return (
        <div className='mt-5'>
            <h3 className='mt-2 mb-2'>用户信息</h3>
            <table className='dark:bg-gray-900 bg-white text-gray-500 text-sm border-collapse mt-2'>
                <thead className='dark:bg-slate-700 bg-slate-300 dark:text-slate-300'>
                    <tr className='text-center text-xs sm:text-sm'>
                        <th className='border py-2 px-1 border-slate-600'>id</th>
                        <th className='border py-2 px-1 border-slate-600'>用户id</th>
                        <th className='border py-2 px-4 border-slate-600'>外部订单id</th>
                        <th className='border py-2 px-4 border-slate-600'>充值订单id</th>
                        <th className='border py-2 px-4 border-slate-600'>充值产品id</th>
                        <th className='border py-2 px-4 border-slate-600'>bot来源id</th>
                        <th className='border py-2 px-3 border-slate-600'>总钻石</th>
                        <th className='border py-2 px-3 border-slate-600'>总金额</th>
                        <th className='border py-2 px-3 border-slate-600'>货币类型</th>
                        <th className='border py-2 px-3 border-slate-600'>充值渠道</th>
                        <th className='border py-2 px-3 border-slate-600'>状态</th>
                        <th className='border py-2 px-3 border-slate-600'>创建时间</th>
                        <th className='border py-2 px-3 border-slate-600'>完成时间</th>
                        <th className='border py-2 px-3 border-slate-600'>更新时间</th>
                    </tr>
                </thead>
                <tbody>
                    <tr className='ml-3 my-1 border-1 text-gray-400'>
                        <td className='border border-slate-700 py-1 px-1'>{orderInfo?.id}</td>
                        <td className='border border-slate-700 py-1 px-1'>{orderInfo?.user_id}</td>
                        <td className='border border-slate-700 py-1 px-1'>{orderInfo?.out_order_id}</td>
                        <td className='border border-slate-700 py-1 px-1'>{orderInfo?.recharge_order_id}</td>
                        <td className='border border-slate-700 py-1 px-1'>{orderInfo?.recharge_product_id}</td>
                        <td className='border border-slate-700 py-1 px-1'>{orderInfo?.from_bot_id}</td>
                        <td className='border border-slate-700 py-1 px-1'>{orderInfo?.amount}</td>
                        <td className='border border-slate-700 py-1 px-1'>{orderInfo?.pay_currency}</td>
                        <td className='border border-slate-700 py-1 px-1'>{(orderInfo?.pay_fee || 0) / 100000}</td>
                        <td className='border border-slate-700 py-1 px-1'>{orderInfo?.recharge_channel}</td>
                        <td className='border border-slate-700 py-1 px-1'>{orderInfo?.status}</td>
                        <td className='border border-slate-700 py-1 px-1'>{format(new Date(orderInfo?.created_at).getTime() || 0, 'YYYY-MM-DD HH:mm:ss')}</td>
                        <td className='border border-slate-700 py-1 px-1'>{format(new Date(orderInfo?.finished_at).getTime() || 0, 'YYYY-MM-DD HH:mm:ss')}</td>
                        <td className='border border-slate-700 py-1 px-1'>{format(new Date(orderInfo?.updated_at).getTime() || 0, 'YYYY-MM-DD HH:mm:ss')}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    )
}

export default OrderInfo