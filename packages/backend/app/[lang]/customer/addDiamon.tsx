import Modal from "@share/src/ui/dialog/Modal"
import { useState, useEffect, useRef } from 'react'
import useRequest from "../hook/useRequest"
import Toast from "@share/src/ui/toast"

const AddDiamon = ({ onClose, uid, payed_balance, reward_balance, reflesh }: any) => {
    const request = useRequest()
    const [loading, setLoading] = useState(false)
    const chargeRef = useRef<any>(null)
    const expireDaysRef = useRef<any>(null)
    const handleDecreaseDiamond = async () => {
        const amount = parseInt(chargeRef?.current.value, 10);
        const expireDays = parseInt(expireDaysRef?.current.value, 10);
        if (!amount || isNaN(Number(amount))) {
            Toast.notify({
                type: 'info',
                message: '请输入正确的金币数量'
            })
            return
        }
        if (amount > 100000) {
            Toast.notify({
                type: 'info',
                message: '单次最多充值100000金币'
            })
            return
        }
        if (!expireDays || isNaN(Number(expireDays))) {
            Toast.notify({
                type: 'info',
                message: '请输入正确的过期天数'
            })
            return
        }
        try {
            setLoading(true)
            const res = await request('/charge', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                id: uid,
                amount: amount,
                expire_days: expireDays,
              })
            })
            if(res.error_code === 0) {
                Toast.notify({
                    type:'success',
                    message: `充值成功`
                })
                reflesh && reflesh()
                onClose && onClose()
            } else {
                Toast.notify({
                    type:'error',
                    message: res.message
                })
            }
          } catch(e) {
            Toast.notify({
                type: 'error',
                message: '充值金币失败'
            })
        } finally {
            setLoading(false)
        }
    }

    return (
        <Modal isOpen={true} onClose={onClose} className='!max-w-4xl'>
            <div className='mb-5'>
                <h3 className='mt-5 mb-2'>发金币</h3>
                <div className='space-y-3'>
                    <span>剩余钻石：{payed_balance} 剩余金币：{reward_balance}</span>
                    <div className='flex items-center'>
                        发放金币：
                        <input
                    type="number" ref={chargeRef}
                    id="balance"
                    autoComplete="balance"
                    required
                    placeholder='输入要充值的金币数量'
                    className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 w-52 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 mr-1"
                  />
                  <input
                    type="number" ref={expireDaysRef}
                    id="expireDays"
                    autoComplete="expireDays"
                    required
                    placeholder='输入有效天数，永久有效输入-1'
                    className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 w-60"
                  />
                        <button 
                            type="button" 
                            className='ml-2 bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-4 rounded disabled:opacity-50' 
                            onClick={handleDecreaseDiamond}
                            disabled={loading}
                        >
                            {loading ? '处理中...' : '确定'}
                        </button>
                    </div>
                </div>
            </div>
        </Modal>
    )
}

export default AddDiamon