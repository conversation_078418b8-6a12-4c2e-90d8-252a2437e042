import Modal from "@share/src/ui/dialog/Modal"
import { DatePicker, Space, Pagination } from 'antd'
import { useState, useEffect } from 'react'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import useRequest from "../hook/useRequest"
import format from '@little-tavern/shared/src/module/formate-date'
import Toast from "@share/src/ui/toast"

const DiamonConsumeHistory = ({ onClose, uid }: any) => {
    const [startDate, setStartDate] = useState<Dayjs | null>(() => dayjs().subtract(1, 'day'))
    const [endDate, setEndDate] = useState<Dayjs | null>(() => dayjs())
    const [onlyRecharge, setOnlyRecharge] = useState(false)
    const [currentPage, setCurrentPage] = useState(1)
    const request = useRequest()
    const [diamondList, setDiamondList] = useState<any>([])

    useEffect(() => {
        handleSearch(1)
    }, [])

    const handleSearch = async (page = currentPage) => {
        if (startDate && endDate) {
            try {
                const res = await request(`/diamond_consumption_history?user_id=${uid}&only_recharge=${onlyRecharge}&start_date=${startDate.format('YYYY-MM-DD')}&end_date=${endDate.format('YYYY-MM-DD')}`)
                console.log('res', res)
                if (res.error_code === 0) {
                    setDiamondList(res.data.list)
                } else {
                    Toast.notify({
                        type: 'error',
                        message: res.message
                    })
                }
                // todo?
            } catch (e: any) {
                console.log("e", e);
                Toast.notify({
                    type: 'error',
                    message: String(e)
                })
            }
        }
    }

    const handleDateChange = (dates: any, dateType: 'start' | 'end') => {
        if (dateType === 'start') {
            setStartDate(dates)
        } else {
            setEndDate(dates)
        }
    }

    return (
        <Modal isOpen={true} onClose={onClose} conWidth='!max-w-6xl' className=''>
            <div className='mb-5'>
                <h3 className='mt-5 mb-2'>消费记录 (最大查询跨度7天)</h3>
                <div className='space-y-3'>
                    <span>时间区间：</span>
                    <Space>
                        <DatePicker
                            showTime
                            placeholder="开始时间"
                            onChange={(date: any) => handleDateChange(date, 'start')}
                            value={startDate}
                        />
                        <span>至</span>
                        <DatePicker
                            showTime
                            placeholder="结束时间"
                            onChange={(date: any) => handleDateChange(date, 'end')}
                            value={endDate}
                            disabledDate={(current: any) =>
                                startDate ? current < startDate : false
                            }
                        />
                    </Space>
                    <button className='ml-2 bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-4 rounded' onClick={() => {
                        setCurrentPage(1)
                        handleSearch(1)
                    }}>
                        查询
                    </button>
                    <div className='max-h-[70vh] overflow-y-auto'>
                        <table className='dark:bg-gray-900 bg-white text-gray-500 text-sm border-collapse mt-2'>
                            <thead className='dark:bg-slate-700 bg-slate-300 dark:text-slate-300'>
                                <tr className='text-center text-xs sm:text-sm'>
                                    <th className='border py-2 px-1 border-slate-600'>类型</th>
                                    <th className='border py-2 px-4 border-slate-600'>角色卡名</th>
                                    <th className='border py-2 px-4 border-slate-600'>角色卡ID</th>
                                    <th className='border py-2 px-4 border-slate-600'>聊天模式</th>
                                    <th className='border py-2 px-4 border-slate-600'>消耗钻石</th>
                                    <th className='border py-2 px-3 border-slate-600'>钻石消耗时间</th>
                                    <th className='border py-2 px-3 border-slate-600'>“消耗活动”的钻石消耗</th>
                                    <th className='border py-2 px-3 border-slate-600'>任务id</th>
                                </tr>
                            </thead>
                            <tbody>
                                {diamondList?.map((item: any, index: number) => {
                                    return <tr key={index} className='ml-3 my-1 border-1 text-gray-400'>
                                        <td className='border border-slate-700 py-1 px-1'>{item?.type}</td>
                                        <td className='border border-slate-700 py-1 px-1'>{item?.role_name}</td>
                                        <td className='border border-slate-700 py-1 px-1'>{item?.role_id}</td>
                                        <td className='border border-slate-700 py-1 px-1'>{item?.chat_desc}</td>
                                        <td className='border border-slate-700 py-1 px-1'>{item?.amount}</td>
                                        <td className='border border-slate-700 py-1 px-1'>{format(item?.created_at * 1000 || 0, 'YYYY-MM-DD HH:mm:ss')}</td>
                                        <td className='border border-slate-700 py-1 px-1'>{item?.extra_info?.payed_diamond_spending}</td>
                                        <td className='border border-slate-700 py-1 px-1'>{item?.extra_info?.diamond_task_id}</td>
                                    </tr>
                                })}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </Modal>
    )
}

export default DiamonConsumeHistory