import Modal from "@share/src/ui/dialog/Modal"
import { useState, useEffect, useRef } from 'react'
import useRequest from "../hook/useRequest"
import Toast from "@share/src/ui/toast"

const UpdateUserCard = ({ onClose, uid, cards, author, reflesh }: any) => {
    const request = useRequest()
    const [loading, setLoading] = useState(false)
    const authorRef = useRef<any>(null)
    const cardRef = useRef<any>(null)
    const updateAuthorProfile = async () => {
        const isAuthor = authorRef?.current.checked;
        const cardNumber = parseInt(cardRef?.current.value, 10);
        if (!cardNumber || isNaN(Number(cardNumber))) {
            Toast.notify({
                type: 'info',
                message: '请输入正确的卡片数量'
            })
            return
        }
        try {
            setLoading(true)
            const res = await request('/manage/user/update_author_profile', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: uid,
                    author: isAuthor,
                    role_count: cardNumber,
                })
            })
            if (res.error_code === 0) {
                Toast.notify({
                    type: 'success',
                    message: `更新成功`
                })
                reflesh && reflesh()
                onClose && onClose()
            } else {
                Toast.notify({
                    type: 'error',
                    message: res.message
                })
            }
        } catch (e) {
            Toast.notify({
                type: 'error',
                message: '更新失败'
            })
        } finally {
            setLoading(false)
        }
    }

    return (
        <Modal isOpen={true} onClose={onClose} className='!max-w-4xl'>
            <div className='mb-5'>
                <h3 className='mt-5 mb-2'>上传卡片数量限制和作者身份设置</h3>
                <div className='space-y-3'>
                    <div>最大上传卡片数：{cards}</div>
                    <label htmlFor="author" className='mr-2'>
                        是否设置为作者：
                        <input
                            type="checkbox" ref={authorRef}
                            id="author"
                            autoComplete="author"
                            required
                            defaultChecked={author}
                        />
                    </label>
                    <input
                        type="number" ref={cardRef}
                        id="card"
                        autoComplete="card"
                        required
                        placeholder='请输入最大上传卡片数量'
                        className="w-60 rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3"
                    />

                    <button
                        type="button"
                        className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'
                        onClick={updateAuthorProfile}
                        disabled={loading}
                    >
                        {loading ? '处理中...' : '确定'}
                    </button>
                </div>
            </div>
        </Modal>
    )
}

export default UpdateUserCard