import format from '@little-tavern/shared/src/module/formate-date'

const ActivityInfo = ({info}: any) => {
    console.log('info', info);
    return (
        <div className='mt-5'>
            <h3 className='mt-2 mb-2'>用户信息</h3>
            <table className='dark:bg-gray-900 bg-white text-gray-500 text-sm border-collapse mt-2'>
                <thead className='dark:bg-slate-700 bg-slate-300 dark:text-slate-300'>
                    <tr className='text-center text-xs sm:text-sm'>
                        <th className='border py-2 px-1 border-slate-600'>season_id</th>
                        <th className='border py-2 px-1 border-slate-600'>task_id</th>
                        <th className='border py-2 px-4 border-slate-600'>任务开始时间</th>
                        <th className='border py-2 px-4 border-slate-600'>任务结束时间</th>
                        <th className='border py-2 px-4 border-slate-600'>用户报名的时间</th>
                        <th className='border py-2 px-4 border-slate-600'>用户完成任务的时间 （ 0的话表示未记录该时间）</th>
                        <th className='border py-2 px-3 border-slate-600'>需要消耗的钻石数</th>
                        <th className='border py-2 px-3 border-slate-600'>活动期间用户消耗的钻石数量</th>
                        <th className='border py-2 px-3 border-slate-600'>用户已经领取完成的 返还金币数量</th>
                        <th className='border py-2 px-3 border-slate-600'>用户已经领取完成的 大奖金币数量</th>
                        <th className='border py-2 px-3 border-slate-600'>活动期间是否发送了 钻石不足提醒充值的消息</th>
                        <th className='border py-2 px-3 border-slate-600'>活动结束时是否发送了 任务未达标的消息</th>
                        <th className='border py-2 px-3 border-slate-600'>活动结束后是否发送了 领取奖励的消息</th>
                        <th className='border py-2 px-3 border-slate-600'>过期未领取奖励时， 是否发送了 奖励过期的消息</th>
                        <th className='border py-2 px-3 border-slate-600'>任务达标情况</th>
                        <th className='border py-2 px-3 border-slate-600'>是否中了大奖</th>
                    </tr>
                </thead>
                <tbody>
                        {info.map((item: any, index: number) => {
                            return <tr key={index} className='ml-3 my-1 border-1 text-gray-400'>    
                                <td className='border border-slate-700 py-1 px-1'>{item?.season_id}</td>
                                <td className='border border-slate-700 py-1 px-1'>{item?.task_id}</td>
                                <td className='border border-slate-700 py-1 px-1'>{format(item?.task_start_at * 1000, 'YYYY-MM-DD HH:mm:ss')}</td>
                                <td className='border border-slate-700 py-1 px-1'>{format(item?.task_end_at * 1000, 'YYYY-MM-DD HH:mm:ss')}</td>
                                <td className='border border-slate-700 py-1 px-1'>{format(item?.join_at * 1000, 'YYYY-MM-DD HH:mm:ss')}</td>
                                <td className='border border-slate-700 py-1 px-1'>{item?.finish_at == 0 ? '未记录' : format(item?.finish_at * 1000, 'YYYY-MM-DD HH:mm:ss')}</td>
                                <td className='border border-slate-700 py-1 px-1'>{item?.required_diamond_amount}</td>
                                <td className='border border-slate-700 py-1 px-1'>{item?.diamond_consumption}</td>
                                <td className='border border-slate-700 py-1 px-1'>{item?.obtained_returned_diamond}</td>
                                <td className='border border-slate-700 py-1 px-1'>{item?.obtained_lottery_diamond}</td>
                                <td className='border border-slate-700 py-1 px-1'>{item?.recharge_msg_sent? 
                                    <span className='text-green-500'>已发送</span> : <span className='text-red-500'>未发送</span>}</td>
                                <td className='border border-slate-700 py-1 px-1'>{item?.failure_msg_sent? 
                                    <span className='text-green-500'>已发送</span> : <span className='text-red-500'>未发送</span>}</td>
                                <td className='border border-slate-700 py-1 px-1'>{item?.reward_msg_sent? 
                                    <span className='text-green-500'>已发送</span> : <span className='text-red-500'>未发送</span>}</td>
                                <td className='border border-slate-700 py-1 px-1'>{item?.expire_msg_sent? 
                                    <span className='text-green-500'>已发送</span> : <span className='text-red-500'>未发送</span>}</td>
                                <td className='border border-slate-700 py-1 px-1'>{item?.status === 0 && <span className='text-red-500'>未达标</span>}{item?.status === 1 && <span className='text-green-500'>达标</span>}{item?.status === 2 && <span className='text-blue-500'>已领取</span>}</td>
                                <td className='border border-slate-700 py-1 px-1'>{item?.win_lottery? 
                                    <span className='text-green-500'>中奖</span> : <span className='text-red-500'>未中奖</span>}</td>
                            </tr>
                        })
                    }
                </tbody>
            </table>
        </div>
    )
}

export default ActivityInfo