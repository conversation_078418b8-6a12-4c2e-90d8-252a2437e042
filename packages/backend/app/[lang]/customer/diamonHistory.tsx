import Modal from "@share/src/ui/dialog/Modal"
import { DatePicker, Space, Pagination } from 'antd'
import { useState, useEffect } from 'react'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import useRequest from "../hook/useRequest"
import format from '@little-tavern/shared/src/module/formate-date'

const limit = 12
const DiamonHistory = ({ onClose, uid }: any) => {
    const [startDate, setStartDate] = useState<Dayjs | null>(() => dayjs().subtract(7, 'day'))
    const [endDate, setEndDate] = useState<Dayjs | null>(() => dayjs())
    const [onlyRecharge, setOnlyRecharge] = useState(false)
    const [currentPage, setCurrentPage] = useState(1)
    const [total, setTotal] = useState(0)
    const request = useRequest()
    const [diamondList, setDiamondList] = useState<any>([])

    useEffect(() => {
        handleSearch(1)
    }, [])

    const handleSearch = async (page = currentPage) => {
        if (startDate && endDate) {
            const offset = (page - 1) * limit
            const res = await request(`/diamond_earning_history?user_id=${uid}&only_recharge=${onlyRecharge}&limit=${limit}&offset=${offset}&start_date=${startDate.format('YYYY-MM-DD')}&end_date=${endDate.format('YYYY-MM-DD')}`)
            console.log('res', res)
            setDiamondList(res.data.list)
            setTotal(res.data.total)
        }
    }

    const handlePageChange = (page: number) => {
        setCurrentPage(page)
        handleSearch(page)
    }

    const handleDateChange = (dates: any, dateType: 'start' | 'end') => {
        if (dateType === 'start') {
            setStartDate(dates)
        } else {
            setEndDate(dates)
        }
    }

    return (
        <Modal isOpen={true} onClose={onClose} conWidth='!max-w-6xl' className=''>
            <div className='mb-5'>
                <h3 className='mt-5 mb-2'>钻石获取记录</h3>
                <div className='space-y-3'>
                    <span>时间区间：</span>
                    <Space>
                        <DatePicker
                            showTime
                            placeholder="开始时间"
                            onChange={(date: any) => handleDateChange(date, 'start')}
                            value={startDate}
                        />
                        <span>至</span>
                        <DatePicker
                            showTime
                            placeholder="结束时间"
                            onChange={(date: any) => handleDateChange(date, 'end')}
                            value={endDate}
                            disabledDate={(current: any) =>
                                startDate ? current < startDate : false
                            }
                        />
                    </Space>
                    <button className='ml-2 bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-4 rounded' onClick={() => {
                        setCurrentPage(1)
                        handleSearch(1)
                    }}>
                        查询
                    </button>
                    <div>
                        只看充值钻石
                        <input type="checkbox" className="ml-2" checked={onlyRecharge} onChange={(e) => setOnlyRecharge(e.target.checked)} />
                    </div>
                    <div className='max-h-[70vh] overflow-y-auto'>
                    <table className='dark:bg-gray-900 bg-white text-gray-500 text-sm border-collapse mt-2'>
                        <thead className='dark:bg-slate-700 bg-slate-300 dark:text-slate-300'>
                            <tr className='text-center text-xs sm:text-sm'>
                                <th className='border py-2 px-1 border-slate-600'>类型</th>
                                <th className='border py-2 px-4 border-slate-600'>充值金额</th>
                                <th className='border py-2 px-4 border-slate-600'>币种</th>
                                <th className='border py-2 px-4 border-slate-600'>RMB金额</th>
                                <th className='border py-2 px-4 border-slate-600'>钻石数量</th>
                                <th className='border py-2 px-4 border-slate-600'>创建时间</th>
                                <th className='border py-2 px-3 border-slate-600'>完成时间</th>
                                <th className='border py-2 px-3 border-slate-600'>订单ID</th>
                                <th className='border py-2 px-3 border-slate-600'>出库订单</th>
                            </tr>
                        </thead>
                        <tbody>
                            {diamondList?.map((item: any, index: number) => {
                                return <tr key={index} className='ml-3 my-1 border-1 text-gray-400'>
                                    <td className='border border-slate-700 py-1 px-1'>{item?.recharge_channel}</td>
                                    <td className='border border-slate-700 py-1 px-1'>{item?.pay_fee / 100000}</td>
                                    <td className='border border-slate-700 py-1 px-1'>{item?.pay_currency}</td>
                                    <td className='border border-slate-700 py-1 px-1'>{item?.cny_fee}</td>
                                    <td className='border border-slate-700 py-1 px-1'>{item?.amount}</td>
                                    <td className='border border-slate-700 py-1 px-1'>{format(item?.created_at * 1000 || 0, 'YYYY-MM-DD HH:mm:ss')}</td>
                                    <td className='border border-slate-700 py-1 px-1'>{format(item?.updated_at * 1000 || 0, 'YYYY-MM-DD HH:mm:ss')}</td>
                                    <td className='border border-slate-700 py-1 px-1'>{item?.recharge_order_id}</td>
                                    <td className='border border-slate-700 py-1 px-1'>{item?.out_order_id}</td>
                                </tr>
                            })}
                        </tbody>
                    </table>
                    </div>
                    {total > 0 && (
                        <div className="flex justify-end mt-4">
                            <Pagination
                                current={currentPage}
                                total={total}
                                pageSize={limit}
                                onChange={handlePageChange}
                                showSizeChanger={false}
                            />
                        </div>
                    )}
                </div>
            </div>
        </Modal>
    )
}

export default DiamonHistory