'use client'
import type { FC } from 'react'
import React, { useContext, Suspense, useState } from 'react'
import Link from 'next/link'
import OperationNav from './operation-nav'
import RegNav from './reg-nav'
import IndexNav from './index-nav'
import TagNav from './reg-nav'
import Setting from './setting-nav'
import Review from './review-nav'
import PresetsNav from './presets-nav'
import cn from 'classnames'
import { useParams, usePathname, useSelectedLayoutSegment } from 'next/navigation'
import { AuthContext } from '../authContext'
import RefLinkNav from './reflink-nav'
import CustomerNav from './customer-nav'
import s from './style.module.scss'
// import LoginInfo from './loginInfo'
import dynamic from 'next/dynamic';
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'
import { useTranslation } from 'react-i18next'
import Pay from './pay'
const ClientOnlyComponent = dynamic(() => import('./loginInfo'), {
  ssr: false, // 关闭服务器端渲染
});

const commonNavClass = 'flex items-center relative h-8 rounded-xl font-medium text-base cursor-pointer font-medium h-[57px] z-10'
const navClassName = `mr-3 sm:mr-7`
const navMobileClassName = `mr-0 h-8 rounded-xl border-x-8 border-black`

export type IHeaderProps = {
  title: string
}
const Header: FC<IHeaderProps> = ({
  title,
}) => {
  const params = useParams()
  const lang = params.lang as string
  const auth = useContext(AuthContext);
  const user = auth?.user;
  const [isShowMobileMenu, setIsShowMobileMenu] = useState(false)

  const toggleMenu = () => {
    setIsShowMobileMenu(!isShowMobileMenu)
  }

  const { t } = useTranslation()
  const selectedSegment = useSelectedLayoutSegment()

  return (
    <div className="fixed z-10 top-0 w-full left-0 border-b border-gray-200 space-x-2 bg-black">
      <div className='mx-auto flex items-center justify-between h-[57px]'>
        <div className='flex items-center'>
          <Link className='sm:ml-4 ml-2 items-center flex' href={`/${lang}/`}>
            <div className="text-xs sm:text-base rounded-2xl bg-purple-500 p-2.5 py-2 sm:p-3 sm:py-1.5 sm:ml-1.5 font-bold text-white">{title} {process.env.NEXT_PUBLIC_ENV === 'dev' && <span className='text-xs inlin-block bg-red-600 px-1.5 py-0.5 rounded-full'>测试</span>}</div>
          </Link>
          <div className='items-center ml-4 sm:ml-8 flex'>
            <IndexNav className={cn(commonNavClass, navClassName)} />
            {/* <TagNav className={cn(commonNavClass, navClassName)} /> */}
            <PresetsNav className={cn(commonNavClass, navClassName)} />
            <Link href={`/${lang}/label`} className={cn(commonNavClass, navClassName,  selectedSegment === 'label' ? s.navActive : s.navUnActive)}>
              标签
            </Link>
            <OperationNav className={cn(commonNavClass, navClassName)} />
            <RegNav className={cn(commonNavClass, navClassName)} />
            <Setting className={cn(commonNavClass, navClassName)} />
            <Pay className={cn(commonNavClass, navClassName)} />
            <Review className={cn(commonNavClass, navClassName)} />
            <RefLinkNav className={cn(commonNavClass, navClassName)} />
            <CustomerNav className={cn(commonNavClass, navClassName)} />
          </div>
          {/* <div className='ml-1 sm:hidden' onClick={toggleMenu}>
            <div className='h-[57px] px-2 flex items-center justify-center'>{isShowMobileMenu? <XMarkIcon className="h-6 w-6 text-white" /> : <Bars3Icon className="h-6 w-6 text-white" />}</div>
            {isShowMobileMenu && <div className='w-screen flex flex-col mt-1 absolute left-0 sm:hidden bg-black pb-6'>
              <IndexNav className={cn(commonNavClass, navMobileClassName)} />
              <ChatNav className={cn(commonNavClass, navMobileClassName)} />
              <MineNav className={cn(commonNavClass, navMobileClassName)} />
              <PresetsNav className={cn(commonNavClass, navMobileClassName)} />
            </div>}
          </div> */}
        </div>
        <ClientOnlyComponent />
      </div>
    </div>
  )
}

export default React.memo(Header)
