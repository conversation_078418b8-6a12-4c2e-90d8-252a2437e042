'use client'

import { useTranslation } from 'react-i18next'
import Link from 'next/link'
import { useParams, useSelectedLayoutSegment } from 'next/navigation'
import classNames from 'classnames'
import s from '../style.module.scss'

type OperationNavProps = {
  className?: string
}

const OperationNav = ({
  className,
}: OperationNavProps) => {
  const { t } = useTranslation()
  const selectedSegment = useSelectedLayoutSegment()
  const actived = selectedSegment === 'operation'
  const params = useParams()
  const lang = params.lang as string
  return (
    <Link href={`/${lang}/operation`} className={classNames(
      className,
      actived ? s.navActive : s.navUnActive,
    )}>
      {t('app.nav.operation')}
    </Link>
  )
}

export default OperationNav
