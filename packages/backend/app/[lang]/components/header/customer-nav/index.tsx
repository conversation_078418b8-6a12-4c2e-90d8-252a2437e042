'use client'

import { useTranslation } from 'react-i18next'
import Link from 'next/link'
import { useParams, useSelectedLayoutSegment } from 'next/navigation'
import classNames from 'classnames'
import s from '../style.module.scss'

type customerNavProps = {
  className?: string
}

const CustomerNav = ({
  className,
}: customerNavProps) => {
  const { t } = useTranslation()
  const selectedSegment = useSelectedLayoutSegment()
  const actived = selectedSegment == 'customer'
  const params = useParams()
  const lang = params.lang as string
  return (
    <Link href={`/${lang}/customer`} className={classNames(
      className,
      actived ? s.navActive : s.navUnActive,
    )}>
      客服
    </Link>
  )
}

export default CustomerNav
