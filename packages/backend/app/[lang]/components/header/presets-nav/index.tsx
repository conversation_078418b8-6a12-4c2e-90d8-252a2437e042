'use client'

import { useTranslation } from 'react-i18next'
import Link from 'next/link'
import { useParams, useSelectedLayoutSegment } from 'next/navigation'
import classNames from 'classnames'
import s from '../style.module.scss'

type PresetsNavProps = {
  className?: string
}

const PresetsNav = ({
  className,
}: PresetsNavProps) => {
  const { t } = useTranslation()
  const selectedSegment = useSelectedLayoutSegment()
  const actived = selectedSegment === 'presets'
  const params = useParams()
  const lang = params.lang as string
  return (
    <Link href={`/${lang}/presets`} className={classNames(
      className,
      actived ? s.navActive : s.navUnActive,
    )}>
      {t('app.nav.presets')}
    </Link>
  )
}

export default PresetsNav
