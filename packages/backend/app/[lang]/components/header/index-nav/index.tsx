'use client'

import { useTranslation } from 'react-i18next'
import Link from 'next/link'
import { useParams, useSelectedLayoutSegment } from 'next/navigation'
import classNames from 'classnames'
import s from '../style.module.scss'

type IndexNavProps = {
  className?: string
}

const IndexNav = ({
  className,
}: IndexNavProps) => {
  const { t } = useTranslation()
  const selectedSegment = useSelectedLayoutSegment()
  const actived = selectedSegment == null
  const params = useParams()
  const lang = params.lang as string
  return (
    <Link href={`/${lang}`} className={classNames(
      className,
      actived ? s.navActive : s.navUnActive,
    )}>
      {t('app.nav.index')}
    </Link>
  )
}

export default IndexNav
