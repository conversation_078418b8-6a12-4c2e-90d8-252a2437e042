'use client'

import { useTranslation } from 'react-i18next'
import Link from 'next/link'
import { useParams, useSelectedLayoutSegment } from 'next/navigation'
import classNames from 'classnames'
import s from '../style.module.scss'

type PayProps = {
  className?: string
}

const Pay = ({
  className,
}: PayProps) => {
  const { t } = useTranslation()
  const selectedSegment = useSelectedLayoutSegment()
  const actived = selectedSegment == 'pay'
  const params = useParams()
  const lang = params.lang as string
  return (
    <Link href={`/${lang}/pay`} className={classNames(
      className,
      actived ? s.navActive : s.navUnActive,
    )}>
      支付
    </Link>
  )
}

export default Pay
