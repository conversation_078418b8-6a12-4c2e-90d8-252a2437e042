'use client'

import { useTranslation } from 'react-i18next'
import Link from 'next/link'
import { useParams, useSelectedLayoutSegment } from 'next/navigation'
import classNames from 'classnames'
import s from '../style.module.scss'

type refLinkNavProps = {
  className?: string
}

const RefLinkNav = ({
  className,
}: refLinkNavProps) => {
  const { t } = useTranslation()
  const selectedSegment = useSelectedLayoutSegment()
  const actived = selectedSegment == 'reflink'
  const params = useParams()
  const lang = params.lang as string
  return (
    <Link href={`/${lang}/reflink`} className={classNames(
      className,
      actived ? s.navActive : s.navUnActive,
    )}>
      推广
    </Link>
  )
}

export default RefLinkNav
