'use client'

import { useTranslation } from 'react-i18next'
import Link from 'next/link'
import { useParams, useSelectedLayoutSegment } from 'next/navigation'
import classNames from 'classnames'
import s from '../style.module.scss'

type reviewNavProps = {
  className?: string
}

const ReviewNav = ({
  className,
}: reviewNavProps) => {
  const { t } = useTranslation()
  const selectedSegment = useSelectedLayoutSegment()
  const actived = selectedSegment == 'review'
  const params = useParams()
  const lang = params.lang as string
  return (
    <Link href={`/${lang}/review`} className={classNames(
      className,
      actived ? s.navActive : s.navUnActive,
    )}>
      审核
    </Link>
  )
}

export default ReviewNav
