
'use client'
import type { FC } from 'react'
import React, { useContext } from 'react'
import cn from 'classnames'
import { useParams, usePathname } from 'next/navigation'
import { AuthContext } from '../authContext'
import { Menu, Transition } from '@headlessui/react'
import { Fragment, useEffect, useRef, useState } from 'react'
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import useDialogLogin from '@little-tavern/shared/src/hook/useDialogLogin'
import useDialogRegist from '@little-tavern/shared/src/hook/useDialogRegist'
import { useTranslation } from 'react-i18next'

const LoginInfo = () => {
    const { t } = useTranslation()
    const params = useParams()
    const lang = params.lang as string
    const auth = useContext(AuthContext);
    const user = auth?.user;
    const dialogLogin = useDialogLogin();
    return (<>
      <div className='mr-2'><div className='relative z-10 flex items-center flex-shrink-0 text-gray-500'>
      {
      <div className="flex items-center text-sm">
      <Menu as="div" className="relative inline-block text-left">
      {({ open }) => (
        <>
        <div>
        <Menu.Button className="inline-flex w-full justify-center pl-2 pr-4 py-2 text-sm font-medium text-white focus:outline-none focus-visible:ring-2">
          <div className={cn('text-zinc-500 text-right overflow-hidden text-ellipsis whitespace-nowrap')}>
          {user?.nickname || ''}
          </div>
            <ChevronDownIcon
              className={cn("-mr-1 h-5 w-5 text-violet-500 dark:text-violet-200 dark:hover:text-violet-100 transition", open && "rotate-180")}
              aria-hidden="true"
            />
          </Menu.Button>
        </div>
        <Transition
          as={Fragment}
          enter="transition ease-out duration-100"
          enterFrom="transform opacity-0 scale-95"
          enterTo="transform opacity-100 scale-100"
          leave="transition ease-in duration-75"
          leaveFrom="transform opacity-100 scale-100"
          leaveTo="transform opacity-0 scale-95"
        >
          <Menu.Items className="border  dark:border-gray-700 absolute right-0 mt-2 mr-1 w-28 sm:w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white dark:bg-gray-900 shadow-lg ring-1 ring-purple-500 dark:ring-black/5 focus:outline-none">
            <div className="px-1 py-1 ">
              <Menu.Item>
                {({ active }) => (
                  <button
                    onClick={auth?.logout}
                    className={`${
                      active ? 'bg-violet-500 text-white' : ' text-zinc-500'
                    } group flex w-full items-center rounded-md px-2 py-2 text-sm`}
                  >{t('app.common.logout')}</button>
                )}
              </Menu.Item>
            </div>
          </Menu.Items>
        </Transition>
        </>
        )}
      </Menu>
    </div>}
  </div></div>
  </>)
}

export default React.memo(LoginInfo)