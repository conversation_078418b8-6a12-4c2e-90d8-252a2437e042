'use client'

import { useTranslation } from 'react-i18next'
import Link from 'next/link'
import { useParams, useSelectedLayoutSegment } from 'next/navigation'
import classNames from 'classnames'
import s from '../style.module.scss'

type SettingProps = {
  className?: string
}

const Setting = ({
  className,
}: SettingProps) => {
  const { t } = useTranslation()
  const selectedSegment = useSelectedLayoutSegment()
  const actived = selectedSegment == 'setting'
  const params = useParams()
  const lang = params.lang as string
  return (
    <Link href={`/${lang}/setting`} className={classNames(
      className,
      actived ? s.navActive : s.navUnActive,
    )}>
      {t('app.nav.setting')}
    </Link>
  )
}

export default Setting
