'use client'

import { useTranslation } from 'react-i18next'
import Link from 'next/link'
import { useParams, useSelectedLayoutSegment } from 'next/navigation'
import classNames from 'classnames'
import s from '../style.module.scss'

type RegNavProps = {
  className?: string
}

const RegNav = ({
  className,
}: RegNavProps) => {
  const { t } = useTranslation()
  const selectedSegment = useSelectedLayoutSegment()
  const actived = selectedSegment === 'reg'
  const params = useParams()
  const lang = params.lang as string
  return (
    <Link href={`/${lang}/reg`} className={classNames(
      className,
      actived ? s.navActive : s.navUnActive,
    )}>
      {t('app.nav.reg')}
    </Link>
  )
}

export default RegNav
