'use client'

import { createContext, useState, ReactNode, useEffect, useLayoutEffect } from 'react';
import Toast from '@little-tavern/shared/src/ui/toast'
import { getCookie, setCookie, deleteCookie } from 'cookies-next';
interface AuthContextValue {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  logoutSilent: () => void;
  isLogin: boolean,
  updateUserInfo: (userInfo: any) => void
}

interface User {
  id: string;
  nickname: string;
  email: string;
  avatar: string
}
const { notify } = Toast

export const AuthContext = createContext<AuthContextValue | null>(null);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [isTG, setIsTG] = useState(false)
  const token = getCookie('token')
  let storeUser = null;
  if (token) {
    storeUser = localStorage.getItem('user');
    if(storeUser) {
      storeUser = JSON.parse(storeUser);
    }
  }
  const [user, setUser] = useState<User | null>(storeUser);
  const [isLogin, setIsLogin] = useState(!!token)

  const updateUser = (res: any) => {
    setUser(res);
    setIsLogin(true);
    localStorage.setItem('user', JSON.stringify({
      id: res.id,
      nickname: res.nickname,
      email: res.email,
      avatar: res.avatar
    }))
    setCookie('token', res.token, { maxAge: 60*60*12*30 })
  }

  const login = async (email: string, password: string) => {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_HOST}/login`, {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: email,
            password: password
          })
      })
      const res = await response.json();
      if (response.ok && res.success) {
          notify({ type: 'success', message: '登录成功～' })
          console.log(res);
          updateUser(res);
          location.reload()
      } else {
          throw new Error(res?.msg || res?.reason);
      }
  };

  const logout = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_HOST}/logout`, {credentials: 'include'})
      if (response.ok) {
        deleteCookie('user')
        deleteCookie('token')
        // location.reload();
        location.href = '/'
      } else {
        throw new Error('退出登录异常')
      }
    } catch (e) {
      console.log('退出登录异常');
    }
  };
  const logoutSilent = () => {
    setUser(null);
    setIsLogin(false);
    deleteCookie('user')
    deleteCookie('token')
    localStorage.removeItem('user')
  }

  const updateUserInfo = (userInfo: any) => {
    updateUser({...user, ...userInfo})
  }

  return (
    <>
    <AuthContext.Provider value={{ user, login, logout, isLogin, logoutSilent, updateUserInfo }}>
      {children}
    </AuthContext.Provider>
    </>
  );
};