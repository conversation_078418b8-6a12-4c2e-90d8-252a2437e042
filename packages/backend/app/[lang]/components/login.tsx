"use client";

import React, { useContext, useState, useEffect } from 'react';
import type { FC, FormEvent } from 'react'
import Modal from '@share/src/ui/dialog/Modal';
// import googleSvg from './img/google.svg'
import Image from 'next/image';
import Toast from '@share/src/ui/toast'
import { useTranslation } from 'react-i18next'
import Link from 'next/link';
type IProps = {
  onClose: Function,
  isOpen: boolean,
  onOpen: Function,
  auth: any
}
const { notify } = Toast
declare global {
  interface Window {
    TelegramLoginWidget: any;
  }
}

const Login: FC<IProps> = ({ onClose, isOpen, onOpen, auth }) => {
  const { t } = useTranslation()
  const [errMsg, setErrMsg] = useState('');
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const handleChange = (e: any) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    setErrMsg('')
  };
  const onLogin = async (e: FormEvent) => {
    e.preventDefault();
    try{
      await auth?.login(formData.email, formData.password);
      onClose()
    } catch(e) {
      notify({ type: 'error', message: '抱歉，登录出现问题，请稍后再试～' })
      setErrMsg(String(e))
      console.error('Error logging in:', e);
    }
  }
  const onGoogleLogin = async () => {
    location.href = `${process.env.NEXT_PUBLIC_API_HOST}/login/google`
    // try{
    //   const response = await fetch(`${process.env.NEXT_PUBLIC_API_HOST}/login/google`);
    //   const data = await response.json();
    //   console.log(data);
    //   console.log('onGoogleLogin success:', response.body);
    // } catch(e) {
    //   console.error('Upload error:', e);
    // }
  }
  useEffect(() => {
    if (isOpen) {

      // Load Telegram Widget Script
      // const script = document.createElement('script');
      // script.src = 'https://telegram.org/js/telegram-widget.js?22';
      // script.setAttribute('data-telegram-login', process.env.NEXT_PUBLIC_TELEGRAM_BOT_NAME || '');
      // script.setAttribute('data-size', 'large');
      // script.setAttribute('data-radius', '4');
      // script.setAttribute('data-request-access', 'write');
      // script.setAttribute('data-userpic', 'false');
      // script.setAttribute('data-onauth', 'window.onTelegramAuth(user)');
      // script.async = true;

      // // Add global callback function
      // window.onTelegramAuth = async (user: any) => {
      //   try {
      //     await auth.telegramLogin(user);
      //     onClose();
      //   } catch (error) {
      //     notify({ type: 'error', message: t('app.login.login_failed') });
      //     console.error('Telegram login error:', error);
      //   }
      // };

      // const container = document.getElementById('telegram-login-container');
      // if (container) {
      //   container.innerHTML = '';
      //   container.appendChild(script);
      // }

      // return () => {
      //   if (container) {
      //     container.innerHTML = '';
      //   }
      //   delete window.onTelegramAuth;
      // };
    }
  }, [isOpen, auth, onClose, t]);

  return (
    <Modal onClose={() => {onClose()}} isOpen={isOpen}>
      <div className='min-h-20 mb-2'>
        <div className="flex min-h-full flex-1 flex-col justify-center px-6 pt-3">
        <div className="sm:mx-auto sm:w-full sm:max-w-sm">
          <h2 className=" text-center text-2xl font-bold leading-9 tracking-tight">
            {t('app.login.login_title')}
          </h2>
        </div>

        <div className="sm:mx-auto sm:w-full sm:max-w-sm">
          <form className="space-y-6" action="#" method="POST" onSubmit={onLogin}>
            <div>
              <label htmlFor="email" className="block text-sm font-medium leading-6 ">
                {t('app.login.email')}
              </label>
              <div className="mt-2">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="ipt py-2.5"
                />
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between">
                <label htmlFor="password" className="block text-sm font-medium leading-6 ">
                {t('app.login.password')}
                </label>
                <div className="text-sm">
                  <a href="#" className="font-semibold text-indigo-400 hover:text-indigo-500">
                    Forgot password?
                  </a>
                </div>
              </div>
              <div className="mt-2">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  className="ipt py-2.5"
                />
              </div>
              {errMsg && <p className='text-xs mt-1 text-red-500'>{errMsg}</p>}
            </div>

            <div className='pt-6'>
              <button
                type="submit"
                className="flex w-full justify-center rounded-md px-3 py-2.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-purple-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 bg-purple-600 text-white"
              >{t('app.common.login')}</button>
            </div>
          </form>
          {/* <p className='text-center mt-5 text-sm leading-6 text-gray-600'>{t('app.login.other_login')}</p> */}
        </div>
      </div>
      </div>
    </Modal>
  );
};

export default Login;
