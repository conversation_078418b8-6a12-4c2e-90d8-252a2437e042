// app/components/ui/TabSwitcher.tsx
"use client";

import React, { useState } from 'react';
import TabContent from '../components/TabContent'
import useRequest from '@backend/app/[lang]/hook/useRequest'
import Toast from '@little-tavern/shared/src/ui/toast';

type TabType = {
    name: string;
    content: React.ReactNode;
};

type TabSwitcherProps = {
    userRoles: any[];
    speakList: string[];
    tagList: string[];
    updateCards: Function
    reflesh: Function
    currentTag: string
    setCurrentTag: Function
};

const TabSwitcher = ({ userRoles, speakList, tagList, updateCards, reflesh, currentTag, setCurrentTag }: TabSwitcherProps) => {
    const request = useRequest();
    const handleDragEnd = async (dropResult: any) => {
        if (dropResult.removedIndex !== null && dropResult.addedIndex !== null && dropResult.removedIndex !== dropResult.addedIndex) {
            const newRoles = [...userRoles];
            const [removedTag] = newRoles.splice(dropResult.removedIndex, 1);
            newRoles.splice(dropResult.addedIndex, 0, removedTag);
            
            updateCards(newRoles);
            try {
                const res = await request('/save_role_orders', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        category: currentTag,
                        orders: newRoles.map(role => role.mode_target_id),
                        mode_type: removedTag.mode_type,
                        mode_target_id: removedTag.mode_target_id,
                        position: dropResult.addedIndex
                    })
                })
                Toast.notify({
                    type:'success',
                    message: `更新成功`
                })
            } catch(e) {
                console.log("e", e);
            }
        }
    };

    return (
        <div>
            <div className="pt-4 px-2">
                <div className='space-x-4'>
                    {tagList?.map((tag: any, index: number) => (
                        <button
                            key={index}
                            className={`px-6 mb-3 py-1 rounded-lg ${tag === currentTag ? 'bg-purple-500 text-white' : 'bg-gray-700 text-white hover:bg-gray-600'}`}
                            onClick={() => setCurrentTag(tag)}
                        >
                            {tag}
                        </button>
                    ))}
                </div>
            </div>
            <div className="px-2 pt-2 pb-2 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                {userRoles.length > 0 ? (
                    <TabContent
                        reflesh={reflesh}
                        roles={userRoles}
                        speakList={speakList}
                        handleDragEnd={handleDragEnd}
                    />
                ) : (
                    <p className="mt-1 text-sm leading-6 text-gray-300">你还没有创建的角色卡片，请点击创建或者上传</p>
                )}
            </div>
        </div>
    );
};

export default TabSwitcher;
