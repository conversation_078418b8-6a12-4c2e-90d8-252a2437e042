'use client'

import cn from 'classnames'
import { APP_INFO } from '@backend/config'
import ThemeSwitch from '../header/theme/theme-switch'
// import { languages } from '@little-tavern/shared/src/i18n/settings'
import { useParams, usePathname } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import { setLocaleOnClient } from '@little-tavern/shared/src/i18n/client'
import Link from 'next/link'

const Footer = (
  {
    cl,
  }: { cl?: string },
) => {
  const pathname = usePathname()
  const params = useParams()
  const lang = params.lang as string

  const change = (l: string) => {
    setLocaleOnClient(l)
    location.href = pathname.replace(lang, l);
  }

  const { t } = useTranslation()
  return (
    <>
      {/* copyright  */}
      <div className={cn('md:ml-[var(--layout-left)] justify-center md:w-content py-4 bottom-4 flex space-x-2 text-gray-400 font-normal text-xs', cl)}>
        {/* <div className="">© {APP_INFO.copyright || APP_INFO.title} {(new Date()).getFullYear()}</div>
        {APP_INFO.privacy_policy && (
          <>
            <div>·</div>
            <div>{t('app.generation.privacyPolicyLeft')}
              <a
                className='text-gray-500'
                href={APP_INFO.privacy_policy}>{t('app.generation.privacyPolicyMiddle')}</a>
              {t('app.generation.privacyPolicyRight')}
            </div>
          </>
        )} */}
        {/* <Link href="https://÷forms.gle/4AYk1F2FWVKMQrTe6" target='_blank'>联系我们</Link> */}
        {/* <Link href="https://t.me/playai888/13" target='_blank'>{t('app.footer.contact')}</Link> */}
        {/* <Link href={`/${lang}/privacy`}>{t('app.footer.privacy')}</Link> */}
        {/* <Link href={`/${lang}/faq`}>FAQ</Link> */}
        {/* <ThemeSwitch></ThemeSwitch> */}
        {/* <div className='flex items-center flex-shrink-0 text-gray-500 text-xs'>
          {languages.map((l, index) => {
            return (
              <div key={l} className='flex items-center'>
                <button className={cn(l === lang && 'text-zinc-200')} onClick={() => {
                  change(l)
                }}>
                  {t(`app.lang.${l}`)}
                </button>
                {languages.length !== (index + 1) && <div className='h-3 mx-1 border-r border-slate-400'></div>}
              </div>
            )
          })}
        </div> */}
      </div>
    </>
  )
}

export default Footer
