// app/components/ui/TabContent.tsx
"use client";
import Image from 'next/image'
import React, { useRef, useState } from 'react';
import Card from '@little-tavern/shared/src/ui/card';
import type { RoleType } from '@little-tavern/shared/src/ui/card';
import { useParams } from 'next/navigation'
import useRequest from '@backend/app/[lang]/hook/useRequest';
import Toast from '@little-tavern/shared/src/ui/toast';
import useReview from '@share/src/hook/useReview'
import { Move } from 'lucide-react';

import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
} from '@dnd-kit/core';
import {
    arrayMove,
    SortableContext,
    sortableKeyboardCoordinates,
    useSortable,
    rectSortingStrategy,
} from '@dnd-kit/sortable';

type TabContentProps = {
    roles: any[];
    reflesh: Function
    handleDragEnd?: any
    subTagList?: string[]
    speakList?: string[]
};

const SortableItem = ({ role, index, reflesh, speakList, viewCard, share, handleDragEnd }: any) => {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
    } = useSortable({id: role.mode_type + role.mode_target_id + index});

    const style = {
        transform: transform ? `translate3d(${transform.x}px, ${transform.y}px, 0)` : undefined,
        transition,
    };

    const _role = role.mode_type === 'group' ? role.group : role.role;

    return (
        <div ref={setNodeRef} style={style} className='mb-1.5 relative z-0'>
            <div className='rounded flex text-xs items-center bg-gray-900 mb-0.5 gap-x-2'>
                <div className='flex items-center gap-x-2 flex-1'>
                    <span className='inline-block text-center'>{`id: ${role.mode_target_id}`}</span>
                    <button className='flex items-center p-0.5 text-blue-500' onClick={() => {
                        handleDragEnd({ removedIndex: index, addedIndex: 0 })
                    }}>置顶</button>
                    <button className='flex items-center p-0.5 text-blue-500' onClick={() => { share(role.mode_target_id) }}>分享</button>
                    <Order index={index} handleDragEnd={handleDragEnd} />
                    <button className='flex items-center p-0.5 text-blue-500' onClick={() => {viewCard(_role, role.mode_type)}}>编辑</button>
                </div>
                <div className='p-1 cursor-move p-1 px-2' {...attributes} {...listeners}>
                    <Move className='w-3.5 h-3.5 text-gray-300' />
                </div>
            </div>
            <Card key={role.mode_type + role.mode_target_id} reflesh={reflesh} modeType={role.mode_type} role={{ ..._role, isEdit: true, admin: true, speakList: speakList, isShowExportCard: true, isShowDel: true }} isBlurImg={true}></Card>
        </div>
    );
};

const TabContent = ({ roles, reflesh, speakList, subTagList, handleDragEnd }: TabContentProps) => {
    const request = useRequest();
    const review = useReview();
    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const share = async (id: number) => {
        Toast.showLoading("")
        try {
            const res = await request('/roles/broadcast', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    role_id: id
                })
            })
            Toast.hideLoading()
            Toast.notify({
                type: 'success',
                message: '分享成功！'
            })
            console.log('res', res);
        } catch (e: any) {
            console.log('err', e);
            Toast.hideLoading()
            Toast.notify({
                type: 'error',
                message: e
            })
        }
    }

    const viewCard = async (role: any, modeType: string) => {
        const res = await review.show({msg: {...role, modeType: modeType, type: 'edit', isEdit: true}});
        if(res) {
            reflesh();
        }
    }

    const onDragEnd = (event: any) => {
        const {active, over} = event;
        
        if (active.id !== over.id) {
            const oldIndex = roles.findIndex((role: any, index: number) => 
                (role.mode_type + role.mode_target_id + index) === active.id
            );
            const newIndex = roles.findIndex((role: any, index: number) => 
                (role.mode_type + role.mode_target_id + index) === over.id
            );
            
            handleDragEnd({
                removedIndex: oldIndex,
                addedIndex: newIndex
            });
        }
    };

    return (
        <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={onDragEnd}
        >
            <SortableContext
                items={roles.map((role, index) => role.mode_type + role.mode_target_id + index)}
                strategy={rectSortingStrategy}
            >
                {roles.map((role, index) => (
                    <SortableItem
                        key={role.mode_type + role.mode_target_id + index}
                        role={role}
                        index={index}
                        reflesh={reflesh}
                        speakList={speakList}
                        viewCard={viewCard}
                        share={share}
                        handleDragEnd={handleDragEnd}
                    />
                ))}
            </SortableContext>
        </DndContext>
    );
};

export default TabContent;


const Order = ({ index, handleDragEnd }: any) => {
    const [focusCard, setFocusCard] = useState(-1)
    const ipt = useRef<any>(null)
    const onKeyDown = (e: any) => {
        if (e.code === 'Enter') {
          e.preventDefault()
          handleDragEnd({ removedIndex: index, addedIndex: ipt?.current?.value })
        }
    }
    return <div className='inline-block'>排序：<input ref={ipt} className='rounded border border-gray-500 text-center inline-block w-6 mr-1' type="text" onChange={(e) => {
        ipt.current.value = e.target.value
    }} defaultValue={index} onKeyDown={onKeyDown} onFocus={() => { setFocusCard(index); ipt.current.value = ''; }}  />
        {focusCard === index && <button type='button' onClick={() => {
            handleDragEnd({ removedIndex: index, addedIndex: ipt?.current?.value })
        }}>确定</button>}
    </div>
}