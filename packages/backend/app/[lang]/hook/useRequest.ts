import { useCallback } from "react";
import { AuthContext } from "../components/authContext";
import { useContext } from "react";
import { commonHeader} from "@little-tavern/shared/src/module/commonHeader"
import useDialogLogin from './useDialogLogin'
import useComfirm from "@little-tavern/shared/src/hook/useComfirm";

const useRequest = () => {
    // const {open} = useDialog();
    const auth = useContext(AuthContext);
    const dialogLogin = useDialogLogin();
    const comfirm = useComfirm();
    return useCallback((url: string, option?: RequestInit, host?: boolean) => {
        return new Promise<any>(async (resolve, reject) => {
            if(!option) {
              option = { headers: commonHeader }
            } else if (option.headers) {
              Object.assign(option.headers, commonHeader)
            } else {
              option.headers = commonHeader
            }
            try {
              const response = await fetch(`${host? url : process.env.NEXT_PUBLIC_API_HOST + url}`, {
                credentials: 'include',
                ...option
              });
              const res = await response.json();
              if(response.ok) {
                  resolve(res);
              } else if (response.status == 401) {
                console.log('授权问题');
                const comfirmRes = await comfirm.show({
                  title: '权限不足',
                  desc: '没有该页面的权限，是否要重新登录？',
                })
                if(comfirmRes?.confirm) {
                  auth?.logoutSilent();
                  dialogLogin.show();
                }
                // dialogLogin.show();
                // auth?.logoutSilent();
              } else if(response.status == 402) {
                console.log('response.status', res, res?.msg, response.status);
                reject(res.msg)
              } else {
                console.log('response.status', res, res?.msg,  response.status);
                reject('ohter err code')
                throw new Error(res.msg)
              }
            } catch (error) {
                console.error(`Failed to fetch ${url}`, error);
                reject(`Failed to fetch ${url} ${error}`)
                throw error
            }
        })
    }, []);
}

export default useRequest;