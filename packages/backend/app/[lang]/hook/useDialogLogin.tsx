'use client'
import classNames from 'classnames'
import type { ReactEventHandler, ReactNode } from 'react'
import React, { useContext, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import Login from '../components/login'
import { AuthContext } from '../components/authContext'

let promiseInstant: any = null;
const useDialogLogin = () => {
  const auth = useContext(AuthContext)
  return {
    show: () => {
      if(promiseInstant) return promiseInstant;
      let holder = document.createElement('div')
      holder.className = 'absolute z-50 w-full h-full left-0 top-0'
      document.body.appendChild(holder)
      const root = createRoot(holder)
      promiseInstant = new Promise((resolve, reject) => {
        const onComfirm = () => {
          root.unmount();
          document.body.removeChild(holder);
          promiseInstant = null;
          resolve(true)
        }
        const onCancel = () => {
          root.unmount();
          document.body.removeChild(holder);
          promiseInstant = null;
          resolve(false);
        }
        root.render(<Login isOpen={true} onClose={onCancel} onOpen={open} auth={auth} />);
      })
      return promiseInstant;
    },
    hide: () => {
      // if (holder) root.unmount();
    }
  }
}

export default useDialogLogin
