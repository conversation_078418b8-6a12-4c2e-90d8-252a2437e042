'use client'

import React, { createContext, useContext } from 'react'
import { getCookie } from 'cookies-next';
import { query } from '@share/src/module/urlTool'

interface ConfigContextType {
  config: any
}

export const ConfigContext = createContext<ConfigContextType | null>(null)

export const ConfigProvider = ({ children, config }: { children: React.ReactNode, config: any }) => {
  // 在客户端也保存一份配置
  if (typeof window !== 'undefined') {
    window.config = config
    window.config.sfwApp = true;
  }

  return (
    <ConfigContext.Provider value={{ config }}>
      {children}
    </ConfigContext.Provider>
  )
}

// 通过hook const config = useConfig() 调用
export const useConfig = () => {
  const context = useContext(ConfigContext)
  if (!context) {
    throw new Error('useConfig must be used within a ConfigProvider')
  }
  return context.config
}
