@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 0, 0, 0;
  --background-end-rgb: 0, 0, 0;
}

.light {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

button {
  background-color: transparent;
}

.con-width {
  @apply xl:w-[1280px] mx-auto w-full
}
.main-height {
  @apply min-h-[calc(100vh_-_116px)] min-h-[calc(100svh_-_116px)]
}
img{ -webkit-user-drag: none; }

.ipt{
  @apply block w-full rounded-md border-0 shadow-sm ring-1 ring-inset dark:ring-gray-500 focus:ring-inset sm:text-sm sm:leading-6 dark:bg-gray-800 px-3 ring-gray-300 focus:ring-purple-500 dark:focus:ring-purple-500 focus:outline-0 outline-none
}


.btn {
  @apply p-2 px-5 bg-purple-500 text-white rounded text-sm
}
