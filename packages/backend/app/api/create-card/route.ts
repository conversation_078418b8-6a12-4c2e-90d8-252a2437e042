import { apiHost } from "../global";
export const dynamic = 'force-dynamic' // defaults to auto
export async function POST(request: Request, res: Response) {
  try {
    const formData = await request.formData();
    console.log('formData', formData);
    return Response.json({ message: 'Form submitted successfully' });
  } catch (error) {
    return Response.json({ error: 'Failed to fetch role' });
  }
}
