import { apiHost } from "../global";
export const dynamic = 'force-dynamic' // defaults to auto
// https://developer.mozilla.org/docs/Web/API/ReadableStream#convert_async_iterator_to_stream
function iteratorToStream(iterator: any) {
  return new ReadableStream({
    async pull(controller) {
      const { value, done } = await iterator.next()
 
      if (done) {
        controller.close()
      } else {
        controller.enqueue(value)
      }
    },
  })
}
 
function sleep(time: number) {
  return new Promise((resolve) => {
    setTimeout(resolve, time)
  })
}
 
const encoder = new TextEncoder()

const data = {content: "你好啊，哈哈哈"}
 
async function* makeIterator() {
  yield encoder.encode(JSON.stringify({content: "你"}))
  await sleep(500)
  yield encoder.encode(JSON.stringify({content: "好"}))
  await sleep(500)
  yield encoder.encode(JSON.stringify({content: "啊"}))
}
 
export async function GET() {
  const iterator = makeIterator()
  const stream = iteratorToStream(iterator)
 
  return new Response(stream)
}