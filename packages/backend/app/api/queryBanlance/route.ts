import { NextRequest } from "next/server";
import { apiHost } from "../global";
export const dynamic = 'force-dynamic' // defaults to auto
export async function GET(request: NextRequest, res: Response) {
  try {
    const searchParams = new URLSearchParams(request.nextUrl.search);
    console.log('JSON.stringify(json)', searchParams.get('id'), request.url);
    return Response.json({ 
      banlance: 123
     });
  } catch (error) {
    return Response.json({ error: 'Failed to fetch' });
  }
}
