import { resolve } from "path";
import { apiHost } from "../global";
import { NextRequest } from "next/server";
export const dynamic = 'force-dynamic' // defaults to auto
export async function GET(request: NextRequest, res: Response) {
  try {
    const msgId = new URLSearchParams(request.nextUrl.search).get('msgId');
    await sleep(1500)
    console.log(msgId);
    return Response.json({src: 'https://sgp-ai-data-1323765209.cos.ap-singapore.myqcloud.com/image_1714650549_380.jpg?q-sign-algorithm=sha1&q-ak=IKIDVs31sb8SaXHuJWWRk3Bg2NbgZGl5OWVJ&q-sign-time=1714650489%3B1746186549&q-key-time=1714650489%3B1746186549&q-header-list=host&q-url-param-list=&q-signature=7f3d0f4266b28d014219bf920882df882d9037dd'});
  } catch (error) {
    return Response.json({ error: 'Failed to fetch role' });
  }
}

function sleep(time: number) {
  return new Promise((resolve) => {
    setTimeout(resolve, time)
  })
}