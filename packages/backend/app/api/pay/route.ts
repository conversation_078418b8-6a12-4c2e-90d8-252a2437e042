import { apiHost } from "../global";
export const dynamic = 'force-dynamic' // defaults to auto
export async function GET(request: Request, res: Response) {
  try {
    console.log(`${apiHost}/roles`);
    let tgData = request.headers.get("Tgdata") || ', query_id=AAFy4GtwAgAAAHLga3C2xeZ5&user=%7B%22id%22%3A6181085298%2C%22first_name%22%3A%22winson%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22winsonzone2023%22%2C%22language_code%22%3A%22zh-hans%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1713508498&hash=9863ce12201b5563b8141b4e5b6b3a0b503c67959488e9aab4e90532ace9f6b0';
    let tgId = request.headers.get("TgId") || 'youzi';
    console.log({
      Tgdata: tgData,
      TgId: tgId,
    });
    const response = await fetch(`${apiHost}/roles`, {
      cache: 'no-store',
      headers: {
        Tgdata: tgData,
        TgId: tgId,
      },
    });
    const roles = await response.json();
    console.log(roles);
    return Response.json({ 
      balance: 9999,
      payDesc: '钻石可以在所有角色共用，每个轮消耗100钻石',
      inventorys: [
        {
          id: 1,
          title: '6000钻石',
          price: '$3.99'
        },
        {
          id: 2,
          title: '6000钻石',
          price: '$3.99'
        },
        {
          id: 3,
          title: '6000钻石',
          price: '$3.99'
        },
        {
          id: 4,
          title: '6000钻石',
          price: '$3.99'
        },
        {
          id: 5,
          title: '6000钻石',
          price: '$3.99'
        }
      ]
     });
  } catch (error) {
    return Response.json({ error: 'Failed to fetch role' });
  }
}
