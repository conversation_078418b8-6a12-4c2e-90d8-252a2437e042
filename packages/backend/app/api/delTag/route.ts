import { apiHost } from "../global";
export const dynamic = 'force-dynamic' // defaults to auto
let tags = ['BSDM', 'NTR', '同人', '角色扮演', '人妻', '校园'];
export async function POST(request: Request, res: Response) {
  const {tag} = await request.json()
  console.log('tag', tag);
  tags = tags.filter(item => {
    return item !== tag;
  });
  try {
    return Response.json({
      tags: tags
    });
  } catch (error) {
    return Response.json({ error: 'Failed to fetch role' });
  }
}
