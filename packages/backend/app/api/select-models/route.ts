import { apiHost } from "../global";
import { NextRequest } from "next/server";
export const dynamic = 'force-dynamic' // defaults to auto
export async function GET(request: NextRequest, res: Response) {
  try {
    const model = new URLSearchParams(request.nextUrl.search).get('model');
    console.log(model);

    return Response.json({modelList: ['haiku', 'sonnet', 'gpt4'], currentModel: model});
  } catch (error) {
    return Response.json({ error: 'Failed to fetch role' });
  }
}