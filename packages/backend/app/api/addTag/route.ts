import { apiHost } from "../global";
export const dynamic = 'force-dynamic' // defaults to auto
const tags = ['BSDM', 'NTR', '同人', '角色扮演', '人妻', '校园'];
export async function POST(request: Request, res: Response) {
  const {tag} = await request.json()
  tags.push(tag)
  console.log('tag', tag);
  try {
    return Response.json({
      tags: tags
    });
  } catch (error) {
    return Response.json({ error: 'Failed to fetch role' });
  }
}
