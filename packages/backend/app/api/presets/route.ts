import { apiHost } from "../global";
export const dynamic = 'force-dynamic' // defaults to auto
export async function GET(request: Request, res: Response) {
  try {
    return Response.json({
      'claude-3-haiku': {
        'nsfw': {
          'chat': {
            'title': 'hello world'
          },
          'chat-summary': {
            'title': 'chat-summary'
          },
          'impersonate': {
            'title': 'impersonate'
          },
          'impersonate-summary': {
            'title': 'impersonate-summary'
          }
        },
        'sfw': {
          'chat': {
            
          },
          'chat-summary': {

          },
          'impersonate': {

          },
          'impersonate-summary': {

          }
        }
      },
      'claude-3-sonnet': {},
      'claude-3-opus': {}
    });
  } catch (error) {
    return Response.json({ error: 'Failed to fetch role' });
  }
}
