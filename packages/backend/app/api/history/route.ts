import { apiHost } from "../global";
export const dynamic = 'force-dynamic' // defaults to auto
export async function GET(request: Request, res: Response) {
    let tgData = request.headers.get("Tgdata") || ', query_id=AAFy4GtwAgAAAHLga3C2xeZ5&user=%7B%22id%22%3A6181085298%2C%22first_name%22%3A%22winson%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22winsonzone2023%22%2C%22language_code%22%3A%22zh-hans%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1713508498&hash=9863ce12201b5563b8141b4e5b6b3a0b503c67959488e9aab4e90532ace9f6b0';
    let tgId = request.headers.get("TgId") || 'youzi';
    console.log({
      Tgdata: tgData,
      TgId: tgId,
    });
    const role = request.headers.get("role") || "youzi";
    const response = await fetch(`${apiHost}/history?role=${role}`, {
      method: "GET",
      headers: {
        Tgdata: tgData,
        TgId: tgId,
      },
    });
    const history = await response.json();
    console.log(history, `length: ${history.length}`);
    const chatList = history.map((h: any) => ({
      isUser: h.type == "human",
      avatar: h.avatar || "/",
      content: h.content,
      voiceUrl: h.voice_url,
      date: new Date(h.timestamp * 1000).toLocaleString(),
    }));

    return Response.json(chatList);
}