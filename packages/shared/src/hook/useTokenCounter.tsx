import { useCallback, useRef } from 'react';
import useRequest from './useRequest';
import _ from 'lodash';

/**
 * A custom hook for counting tokens in text content
 * 
 * @param options Configuration options for the token counter
 * @returns An object containing the countTokens function and abort function
 */
const useTokenCounter = () => {
  const request = useRequest();
  const abortControllerRef = useRef<AbortController | null>(null);

  /**
   * Counts the number of tokens in the provided content
   * 
   * @param content The text content to count tokens for
   * @returns A promise that resolves to the token count or undefined if there was an error
   */
  const countTokens = useCallback(async (content: string, onSuccess?: any): Promise<number | undefined> => {
    // Cancel any pending requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort('debounce自动取消');
      abortControllerRef.current = null;
    }
    
    // Create a new abort controller for this request
    abortControllerRef.current = new AbortController();
    
    try {
      const res = await request('/tokenizers/count', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ content }),
        signal: abortControllerRef.current.signal
      });
      
      const tokenCount = res.token_count;
      
      // Clear the abort controller reference
      abortControllerRef.current = null;
      
      // Call the success callback if provided
      if (onSuccess) {
        onSuccess(tokenCount);
      }
      
      return tokenCount;
    } catch (error) {
      console.error('Error counting tokens:', error);
      return undefined;
    }
  }, []);

  /**
   * Debounced version of the countTokens function
   */
  const debouncedCountTokens = useCallback(
    _.debounce(countTokens, 500),
    []
  );

  /**
   * Manually abort any pending token counting requests
   */
  const abort = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort('Manual cancellation');
      abortControllerRef.current = null;
    }
  };

  return {
    countTokens,
    debouncedCountTokens,
    abort
  };
};

export default useTokenCounter;
