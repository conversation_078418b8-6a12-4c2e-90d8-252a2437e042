'use client'
import classNames from 'classnames'
import React, { useContext, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import <PERSON><PERSON>per from "react-easy-crop";
import getCroppedImg from '../cropImg'
import { useTranslation } from 'react-i18next'

interface MyCropperProps {
  tmpImg: string;
  aspect?: number;
  quality?: number;
  maxSize?: number;
  onCropHandler: (file: any) => void;
  onCancelCropHandle: () => void;
}

const MyCropper = ({tmpImg, aspect = 1, quality, maxSize, onCropHandler, onCancelCropHandle}: MyCropperProps) => {
  const { t } = useTranslation()
  const [isCrop, setIsCrop] = useState(false)
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1)
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null)
  const onCropComplete = (croppedArea: any, croppedAreaPixels: any) => {
    console.log(croppedArea, croppedAreaPixels);
    setCroppedAreaPixels(croppedAreaPixels)
  };
  const onComfirm = async () => {
    try {
      const file = await getCroppedImg(
        tmpImg,
        croppedAreaPixels,
        0,
        { horizontal: false, vertical: false },
        quality,
        maxSize
      )
      onCropHandler(file);
    } catch (e) {
      console.error(e)
    }
    setIsCrop(false)
  }

  return <div className='flex items-center justify-center absolute z-10 left-0 right-0 top-0 bottom-0 backdrop-blur-sm'><div className="w-3/4 h-3/4 relative z-40">
  <Cropper
    image={tmpImg}
    crop={crop}
    zoom={zoom}
    aspect={aspect}
    onCropChange={setCrop}
    onCropComplete={onCropComplete}
    classes={{containerClassName: 'bg-transparent'}}
    onZoomChange={setZoom}
  />
  <div className='absolute w-full -bottom-24 h-24 bg-black/55 text-center pt-8'>
    <button className='bg-gray-800 p-2 px-5 text-white rounded text-sm w-24 mx-auto mr-4' onClick={onCancelCropHandle}>{t('app.common.cancel')}</button>
    <button className='p-2 px-5 bg-red-500 text-white rounded text-sm w-24 mx-auto' onClick={onComfirm}>{t('app.common.comfirm')}</button>
  </div>
</div></div>
}

const useCrop = () => {
  return {
    /**
     * 显示图片裁剪界面
     * @param tmpImg 需要裁剪的图片URL
     * @param aspect 裁剪框宽高比，默认为1（正方形）
     * @param quality 输出图片质量，取值0-1，默认不压缩
     * @param maxSize 输出图片的最大尺寸（宽或高），超过会等比缩小，默认不限制
     * @returns Promise<Blob> 返回裁剪后的图片Blob对象
     */
    show: (tmpImg: string, aspect = 1, quality?: number, maxSize?: number) => {
      let holder = document.createElement('div')
      holder.className = 'fixed z-50 w-full h-full left-0 top-0'
      document.body.appendChild(holder)
      const root = createRoot(holder)
      return new Promise((resolve, reject) => {
        const onCropHandler = (file: any) => {
          const url = URL.createObjectURL(file)
          process.env.NEXT_PUBLIC_RM_CONSOLE != "true" && console.log('donee', url)
          resolve(file)
          root.unmount();
          document.body.removeChild(holder);
        }
        const onCancelCropHandle = () => {
          root.unmount();
          document.body.removeChild(holder);
          reject();
        }
        root.render(<MyCropper tmpImg={tmpImg} aspect={aspect} quality={quality} maxSize={maxSize} onCropHandler={onCropHandler} onCancelCropHandle={onCancelCropHandle} />);
      })
    },
    hide: () => {
      // if (holder) root.unmount();
    }
  }
}

export default useCrop
