import { useEffect, useLayoutEffect, useState } from 'react';

// 实现一个localStorage，使用localStorage接口
// initialState，如果客户端localStorage中没有数据，会使用initialState

const cache: any = {};
const useLocalStorage = (key: string, initialState: any) => {
  // 这里用 null 作为初始值，这样 TypeScript 就知道 state 的类型是 any | null
  // console.log('cache[key]', cache[key])
  // 如果还没有读取到缓存，先返回null
  const [state, setState] = useState<any | null>(cache[key] === undefined? null : cache[key]);
  const updateState = (value: any, isSetStorage = true) => {
    setState(value);
    isSetStorage && window.localStorage.setItem(key, JSON.stringify({[key]: value}));
  };
  useLayoutEffect(() => {
    const value = window.localStorage.getItem(key);
    if (value) {
      try {
        const val = JSON.parse(value)[key];
        cache[key] = val;
        updateState(val, false);
      } catch (error) {
        cache[key] = initialState;
        updateState(initialState)
        console.error('Error get to localStorage:', error);
      }
    } else {
      updateState(initialState)
    }
  }, [key, initialState]);

  const setValue = (value: any) => {
    try {
      cache[key] = value;
      updateState(value);
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  };

  return [state, setValue];
};

export default useLocalStorage;
