'use client'
import classNames from 'classnames'
import type { ReactEventHandler, ReactNode } from 'react'
import React, { useContext, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import Confirm from '../ui/dialog/Confirm'
import GiftConfirm from '../ui/dialog/GiftConfirm'
import { useTranslation } from 'react-i18next'

type Iresult = {
  confirm: boolean,
  cancel: boolean,
  ignore: boolean
}
let promise: Promise<Iresult> | null;
type IParam = {
  title?: string
  desc?: string
  comfirmBtn?: string
  showCancelBtn?: boolean
  icon?: ReactNode
  cancelBtn?: string,
  isShowIgnore?: boolean,
  showIcon?: boolean,
  isCloseIconShow?: boolean,
  // 取消按钮样式
  cancelBtnStyle?: string
}
const useComfirm = (type: 'normal' | 'gift' = 'normal') => {
  const { t } = useTranslation()
  return {
    show: ({
      title,
      desc,
      comfirmBtn,
      showCancelBtn,
      icon,
      cancelBtn,
      isShowIgnore = false,
      showIcon = true,
      isCloseIconShow = true,
      cancelBtnStyle = ''
    }: IParam) => {
      if(promise) {
        return promise;
      } else {
        let holder = document.createElement('div')
        holder.className = 'absolute z-50 w-full h-full left-0 top-0'
        document.body.appendChild(holder)
        const root = createRoot(holder)
        let ignore = false;
        promise = new Promise<Iresult>((resolve, reject) => {
          const onComfirm = (file: any) => {
            resolve({
              confirm: true,
              cancel: false,
              ignore: ignore
            })
            root.unmount();
            document.body.removeChild(holder);
            promise = null;
          }
          const onClose = () => {
            root.unmount();
            document.body.removeChild(holder);
            resolve({
              confirm: false,
              cancel: false,
              ignore: ignore
            });
            promise = null;
          }
          const onCancel = () => {
            root.unmount();
            document.body.removeChild(holder);
            resolve({
              confirm: false,
              cancel: true,
              ignore: ignore
            });
            promise = null;
          }
          const ignoreCb = (_ignore: boolean) => {
            ignore = _ignore;
          }
          let component;
          switch(type) {
            case 'normal':
            component = <Confirm onCancel={onCancel} cancelBtnStyle={cancelBtnStyle} isOpen={true} onConfirm={onComfirm} onClose={onClose} comfirmBtn={comfirmBtn} showCancelBtn={showCancelBtn} icon={icon} cancelBtn={cancelBtn} isShowIgnore={isShowIgnore} ignoreCb={ignoreCb} showIcon={showIcon} isCloseIconShow={isCloseIconShow}>
            <h1 className='text-base font-semibold leading-6'>{title || t('app.common.del')}</h1>
            <div className='text-sm mt-2 whitespace-pre-wrap pr-4'>{desc || t('app.dialog.delComfirm')}</div>
            </Confirm>
            break;
            case 'gift':
            component = <GiftConfirm isOpen={true} onConfirm={onComfirm} onClose={onCancel} comfirmBtn={comfirmBtn} showCancelBtn={showCancelBtn} icon={icon}>
            <h1 className='text-base font-semibold leading-6'>{title || t('app.common.del')}</h1>
            <div className='text-sm mt-2'>{desc || t('app.dialog.delComfirm')}</div>
            </GiftConfirm>
            break;
          }
          root.render(component);
        })
        return promise;
      }
    },
    hide: () => {
      // if (holder) root.unmount();
    }
  }
}

export default useComfirm
