'use client'
import classNames from 'classnames'
import type { ReactEvent<PERSON><PERSON>ler, ReactNode } from 'react'
import React, { useContext, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
// import CreateCard from '../ui/dialog/CreateCard'
import CreateCard from '../ui/dialog/CreateCard/edit'
import CreateGroupCard from '../ui/dialog/CreateCard/publicGroup'
import { AuthContext } from '../authContext'

type Iparam = {
  msg?: Record<string, any>
}
const useCreateCard = () => {
  const auth = useContext(AuthContext);
  const userInfo = auth?.user;
  // console.log('useCreateCard', userInfo);
  return {
    show: (msg: Iparam) => {
      let holder = document.createElement('div')
      holder.className = 'absolute z-50 w-full h-full left-0 top-0'
      document.body.appendChild(holder)
      const root = createRoot(holder)
      console.log('msg', msg)
      return new Promise((resolve, reject) => {
        const onComfirm = (file: any) => {
          resolve(true)
          root.unmount();
          document.body.removeChild(holder);
        }
        const onCancel = () => {
          root.unmount();
          document.body.removeChild(holder);
          resolve(false);
        }
        root.render(<CreateCard isOpen={true} onConfirm={onComfirm} onCancel={onCancel} msg={msg.msg} userInfo={userInfo}></CreateCard>);
      })
    },
    showPublicGroup: (msg: Iparam) => {
      let holder = document.createElement('div')
      holder.className = 'fixed z-50 w-full h-full left-0 top-0 right-0 bottom-0'
      document.body.appendChild(holder)
      const root = createRoot(holder)
      return new Promise((resolve, reject) => {
        const onComfirm = (file: any) => {
          resolve(true)
          root.unmount();
          document.body.removeChild(holder);
        }
        const onCancel = () => {
          root.unmount();
          document.body.removeChild(holder);
          resolve(false);
        }
        root.render(<CreateGroupCard isOpen={true} onConfirm={onComfirm} onCancel={onCancel} msg={msg.msg} userInfo={userInfo}></CreateGroupCard>);
      })
    },
    hide: () => {
      // if (holder) root.unmount();
    }
  }
}

export default useCreateCard
