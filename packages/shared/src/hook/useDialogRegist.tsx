'use client'
import classNames from 'classnames'
import type { ReactEvent<PERSON>andler, ReactNode } from 'react'
import React, { useContext, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import Regist from '../ui/dialog/regist';

const useDialogRegist = () => {
  return {
    show: (code = '') => {
      let holder = document.createElement('div')
      holder.className = 'absolute z-50 w-full h-full left-0 top-0'
      document.body.appendChild(holder)
      const root = createRoot(holder)
      return new Promise((resolve, reject) => {
        const onComfirm = () => {
          resolve(true)
          root.unmount();
          document.body.removeChild(holder);
        }
        const onCancel = () => {
          root.unmount();
          document.body.removeChild(holder);
          resolve(false);
        }
        root.render(<Regist isOpen={true} onClose={onCancel} code={code} />);
      })
    },
    hide: () => {
      // if (holder) root.unmount();
    }
  }
}

export default useDialogRegist
