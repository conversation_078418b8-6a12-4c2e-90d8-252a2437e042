'use client'
import { createRoot } from 'react-dom/client'
import { useTranslation } from 'react-i18next'
import Archive from '../ui/archive'
import useLinkToChat from '@/app/hook/useLinkToChat'
import Toast from '../ui/toast'
import useRequest from '@share/src/hook/useRequest'
import React, { EventHandler, useContext, useEffect, useState } from 'react';
import { AuthContext } from '../authContext'

const useLoadArchive = () => {
  const { t } = useTranslation()
  const linkToChat = useLinkToChat()
  const request = useRequest()
  const auth = useContext(AuthContext);
  const isTG = auth?.isTG
  return {
    show: ({ roleId, cardName, modeType }: any) => {
      const isGroupCard = modeType === 'group'
      return new Promise(async (resolve, reject) => {
        Toast.showLoading('Loading');
        const res = await request(`/chat/conversation/list?mode_type=${modeType}&mode_target_id=${roleId}`)
        Toast.hideLoading();
        if (res.error_code === 0) {
          const list = res.data?.list;
          if (list.length > 0) {
            let holder = document.createElement('div')
            holder.className = 'absolute z-50 w-full h-full left-0 top-0'
            document.body.appendChild(holder)
            const root = createRoot(holder)
            const onComfirm = (linkInfo: any) => {
              resolve(linkInfo)
              root.unmount();
              document.body.removeChild(holder);
            }
            const onCancel = () => {
              root.unmount();
              document.body.removeChild(holder);
              resolve(false);
            }
            root.render(<Archive archiveList={list} isGroup={isGroupCard} id={roleId} cardName={cardName} onClose={onCancel} onComfirm={onComfirm} isTG={isTG} modeType={modeType} />);
          } else {
            Toast.notify({
              type: 'info',
              message: t('app.history.no_archive')
            })
            resolve(false);
          }
        } else {
          Toast.notify({
            type: 'error',
            message: res.message
          })
          resolve(false);
        }
      })
    },
    hide: () => {
      // if (holder) root.unmount();
    }
  }
}

export default useLoadArchive
