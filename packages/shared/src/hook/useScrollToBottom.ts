import { useCallback, useEffect, useRef } from 'react';

function smoothScroll(element: HTMLElement, target: number, duration: number) {
    const start = element.scrollTop;
    const change = target - start;
    let currentTime = 0;
    let startTime = 0;
  
  let makeEaseOut = (t: number, b: number, c: number, d: number) => {
    return -c *(t /= d)*(t-2) + b;
  };
    
  function animateScroll(timestamp: number) {
    if (startTime) {
      currentTime = timestamp - startTime;
    } else {
      startTime = timestamp;
    }
    const val = makeEaseOut(currentTime, start, change, duration);
    element.scrollTop = val;
    if (currentTime < duration) {
      requestAnimationFrame(animateScroll);
    }
  }
  requestAnimationFrame(animateScroll);
}

const scrollToPosition = (container: any, target: number, duration: number = 1000) => {
  // console.log(container, target, duration);
  smoothScroll(container, target, duration);
};

// const useScrollToBottom = (chatList: any, container: React.RefObject<HTMLDivElement>) => {
//   useEffect(() => {
//     if (container.current && chatList.length != 0) {
//       console.log('scrollToPosition');
//       scrollToPosition(container.current, container.current.scrollHeight);
//     }
//   }, [chatList, container])
// }

export default scrollToPosition
