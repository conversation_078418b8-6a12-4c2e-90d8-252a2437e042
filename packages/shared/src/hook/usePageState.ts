import { useState, useEffect } from 'react';
import { useImmer } from "use-immer";

const cache: any = {}

const usePageState = (key: string, initialState: any) => {
  const [pageData, updatePageData] = useImmer(() => {
    return cache[key] || initialState;
  });

  useEffect(() => {
    cache[key] = pageData;
  }, [key, pageData]);

  return [pageData, updatePageData];
};

export default usePageState;
