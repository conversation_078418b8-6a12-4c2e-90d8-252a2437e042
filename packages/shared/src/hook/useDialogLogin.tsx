'use client'
import classNames from 'classnames'
import type { ReactEvent<PERSON><PERSON>ler, ReactNode } from 'react'
import React, { useContext, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import Login from '../ui/dialog/login';
import { AuthContext } from '../authContext';
import { useTheme } from 'next-themes'

let promiseInstant: any = null;
const useDialogLogin = () => {
  let auth = useContext(AuthContext)
  const { resolvedTheme } = useTheme()
  return {
    show: (_auth?: any) => {
      if(promiseInstant) return promiseInstant;
      let holder = document.createElement('div')
      holder.className = 'absolute z-50 w-full h-full left-0 top-0'
      document.body.appendChild(holder)
      const root = createRoot(holder)
      promiseInstant = new Promise((resolve, reject) => {
        const onComfirm = () => {
          root.unmount();
          document.body.removeChild(holder);
          promiseInstant = null;
          resolve(true)
        }
        const onCancel = () => {
          root.unmount();
          document.body.removeChild(holder);
          promiseInstant = null;
          resolve(false);
        }
        root.render(<Login isOpen={true} theme={resolvedTheme} onClose={onCancel} onOpen={open} auth={_auth || auth} />);
      })
      return promiseInstant;
    },
    hide: () => {
      // if (holder) root.unmount();
    }
  }
}

export default useDialogLogin
