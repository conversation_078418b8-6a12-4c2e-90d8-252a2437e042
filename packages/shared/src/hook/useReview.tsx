
'use client'
import classNames from 'classnames'
import type { ReactEventH<PERSON>ler, ReactNode } from 'react'
import React, { useContext, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
// import CreateCard from '../ui/dialog/CreateCard'
import { AuthContext } from '@share/src/authContext'
import Review from '@share/src/ui/dialog/CreateCard/review'

type Iparam = {
  msg?: Record<string, any>
}
const useReview = () => {
  const auth = useContext(AuthContext);
  const userInfo = auth?.user;
  // console.log('useCreateCard', userInfo);
  return {
    show: (msg: Iparam) => {
      let holder = document.createElement('div')
      holder.className = 'absolute z-50 w-full h-full left-0 top-0'
      document.body.appendChild(holder)
      const root = createRoot(holder)
      return new Promise((resolve, reject) => {
        const onComfirm = () => {
          resolve(true)
          root.unmount();
          document.body.removeChild(holder);
        }
        const onCancel = () => {
          root.unmount();
          document.body.removeChild(holder);
          resolve(false);
        }
        root.render(<Review isOpen={true} onConfirm={onComfirm} onCancel={onCancel} msg={msg.msg} userInfo={userInfo}></Review>);
      })
    },
    hide: () => {
      // if (holder) root.unmount();
    }
  }
}

export default useReview
