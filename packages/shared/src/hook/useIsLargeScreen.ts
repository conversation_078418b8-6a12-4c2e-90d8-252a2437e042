import { useState, useEffect } from "react";

function useIsLargeScreen(threshold = 640) {
  const [isLargeScreen, setIsLargeScreen] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsLargeScreen(window.innerWidth > threshold);
    };

    // 初始化检查屏幕尺寸
    checkScreenSize();

    // 在窗口尺寸变化时，更新状态
    window.addEventListener("resize", checkScreenSize);

    // 清除事件监听器
    return () => {
      window.removeEventListener("resize", checkScreenSize);
    };
  }, [threshold]);

  return isLargeScreen;
}

export default useIsLargeScreen