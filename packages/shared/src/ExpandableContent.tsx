import React, { useState, useRef, useEffect } from 'react';
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import cn from 'classnames'

interface ExpandableContentProps {
  children: React.ReactNode;
  maxHeight: number;
}
const styles = {
  content: ' rounded-lg overflow-hidden transition-all duration-300 ease-in-out',
  contentExpanded: '',
  contentCollapsed: 'after:content-[""] after:absolute after:bottom-0 after:left-0 after:w-full after:h-7 after:bg-gradient-to-b after:from-transparent after:via-85% dark:after:via-gray-900 after:via-white dark:after:to-gray-900 after:to-white pb-1 after:rounded-lg',
  button: 'mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600',
};

const ExpandableContent: React.FC<ExpandableContentProps> = ({ children, maxHeight }) => {
  const [expanded, setExpanded] = useState(true);
  const [height, setHeight] = useState(maxHeight);
  const [scrollHeight, setScrollHeight] = useState(0);
  const contentRef = useRef<HTMLDivElement>(null);
  // const lineClamp3 = expanded? {} : {
  //   display: '-webkit-box',
  //   '-webkit-box-orient': 'vertical',
  //   '-webkit-line-clamp': '7'
  // }
  useEffect(() => {
    if (contentRef.current) {
      const _scrollHeight = contentRef.current.scrollHeight;
      setScrollHeight(_scrollHeight)
      if (_scrollHeight > maxHeight) {
        setExpanded(false);
        setHeight(maxHeight);
      } else {
        setExpanded(true);
        setHeight(_scrollHeight);
      }
    }
  }, [maxHeight]);

  const toggleExpand = () => {
    setExpanded(!expanded);
    setHeight(expanded ? maxHeight : scrollHeight || 0);
  };

  return (
    <div className='relative w-full '>
      <div
        ref={contentRef}
        className={`${styles.content} ${expanded ? styles.contentExpanded : styles.contentCollapsed}`}
        style={{ maxHeight: `${height}px` }}
      >
        {children}
      </div>
      {contentRef.current && contentRef.current.scrollHeight > maxHeight && (
        <button onClick={toggleExpand} className='z-50 w-full absolute pr-2 text-gray-400 text-xs bottom-0 right-0 p-1 text-right rounded-b-lg'>
          {/* {expanded ? '收起' : '展开'} */}
          <ChevronDownIcon className={cn("dark:text-gray-300 inline-block h-5 w-5 text-violet-600 transition", expanded && "rotate-180")} aria-hidden="true" ></ChevronDownIcon>
        </button>
      )}
    </div>
  );
};

export default ExpandableContent;