import serverFetch from './serverFetch'

export interface GlobalConfig {
  sfw_tma?: string[]
  sfwApp?: boolean
  // 首页一级标签
  role_tags?: any[]
  // 上新标签的卡片更新时间，用来显示小红点
  latest_card_created_at?: number
  // 日榜/周榜/月榜标签
  summary_rank_tags?: any[]
}

let cachedConfig: GlobalConfig | null = null

export const getGlobalConfig =  async (lang: string) => {
  try {
    const config = await serverFetch({ url: '/config', lang: lang })
    cachedConfig = config
    return config
  } catch (error) {
    console.log('Failed to fetch global config:', error)
    return {}
  }
}
