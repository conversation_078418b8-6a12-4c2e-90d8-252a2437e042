/**
 * 处理返回逻辑的函数，支持语言参数和来源URL
 * 如果有from参数，会尝试保留页面状态或处理语言切换
 * 如果没有from参数，则返回首页
 * 
 * @param router Next.js路由器实例
 * @param lang 当前语言代码
 */
const back = (router: any, lang: string) => {
    // 获取URL中的from参数，表示来源页面
    const searchParams = new URLSearchParams(window.location.search);
    const fromUrl = decodeURIComponent(searchParams.get('from') || '');
    if(fromUrl) {
        // 匹配开头的任意语言代码（假设由2-5个字母和-组成）
        const regex = /^\/([a-zA-Z-]{2,5})(?=\/|[?#]|$)/;
        // 使用 replace 将匹配到的部分替换为 /{lang}
        const replacedUrl = fromUrl.replace(regex, `/${lang}`);
        // console.log('replacedUrl', fromUrl, replacedUrl, lang);
        if(fromUrl == replacedUrl) {
            // back方式，如果没有切换语言，能保留上一个页面的页面状态
            router.back()
        } else {
            router.replace(replacedUrl)
        }
    } else {
        router.replace('/')
    }
}

export default back