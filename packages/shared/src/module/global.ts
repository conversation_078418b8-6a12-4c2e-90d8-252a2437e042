import {enableES5} from "immer";

const isMobileDevice = function () {
    if (typeof navigator !== 'undefined') {
        const userAgent = navigator.userAgent.toLowerCase();
        return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
    }
    return false;
}()

const runsOnServerSide = typeof window === 'undefined'

const userAgent = runsOnServerSide? '' : navigator.userAgent;
const chromeInfo = userAgent.match(/Chrome\/(\d+)/);
const chromeVersion = (chromeInfo && chromeInfo.length > 1)? Number(chromeInfo[1]) : 0;
// 低版本不支持es5的, 需要兼容
// todo 等新的设备能测试，可以降低版本
if(chromeVersion < 88) {
    enableES5();
}
const isIOS = !runsOnServerSide && /iPad|iPhone|iPod|iOS/.test(navigator.userAgent);
const isTG = process.env.NEXT_PUBLIC_TG === 'true'? true : false
const isTGIOS = isTG && isIOS
const screenW = runsOnServerSide? 0 : window.innerWidth;
export {
    isMobileDevice,
    runsOnServerSide,
    chromeVersion,
    isIOS,
    isTG,
    isTGIOS,
    screenW
}