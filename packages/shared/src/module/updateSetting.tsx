import Toast from "../ui/toast";
// 
/**
 * 更新用户设置
 * @param {Object} params - 参数对象
 * @param {Object} params.data - 需要更新的设置数据，支持动态字段（如 nickname, avatar, user_model, show_nsfw_image, show_chat_tips 等）
 * @param {boolean} params.isShowToast - 是否显示更新成功/失败的提示
 * @param {File} [params.cropImg] - 可选的头像图片文件
 * @param {Function} params.request - 请求函数
 * @param {Object} params.auth - 认证上下文对象
 * @param {Function} params.t - 国际化函数
 * @returns {Promise<boolean>} - 返回更新是否成功
 */
const updateSetting = ({ data, isShowToast, cropImg, request, auth, t }: any) => {
    return new Promise(async (resolve, reject) => {
        Toast.showLoading('');
        const formData = new FormData()
        formData.append('setting', JSON.stringify(data));
        cropImg && formData.append('avatar_img', cropImg)
        try {
            const res = await request(`/setting`, {
                method: 'POST',
                body: formData
            });
            auth?.updateUserInfo(Object.keys(data).reduce((acc, key) => ({
                ...acc,
                [key]: res[key]
            }), {}));
            isShowToast && Toast.notify({ type: 'success', message: t('app.mine.update_success') })
            Toast.hideLoading();
            resolve(true)
        } catch (e) {
            console.error('Upload error:', e);
            isShowToast && Toast.notify({ type: 'success', message: t('app.mine.create_failed') })
            Toast.hideLoading();
            resolve(false)
        }
    })

}

export default updateSetting