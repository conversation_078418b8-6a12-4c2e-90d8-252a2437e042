import { runsOnServerSide } from "./global";

export const hash = {
    get: (name: string) => {
        const hashString = location.hash.slice(1); // 移除开头的 `#`
        const params = new URLSearchParams(hashString);
        return params.get(name);
    }
}

export const query = {
    get: (name: string) => {
        const queryString = location.search;
        const params = new URLSearchParams(queryString);
        return params.get(name);
    },
    set: (url: string, name: string, value: string) => {
        // Remove any existing query string from the URL
        const baseUrl = url.split('?')[0];
        const params = new URLSearchParams();
        params.set(name, value);
        return `${baseUrl}?${params.toString()}`
    },
    // 增加非键值对的query，例如&imageMogr2/blur/100x100或者?imageMogr2/blur/100x100
    append: (url: string, query: string) => { 
        const hasQuery = url.includes('?');
        return `${url}${hasQuery ? '&' : '?'}${query}`
    } 
}

// 对图片进行模糊处理
export const imgBlur = ({url, isBlurImg, canBlur = true}: {url: string, isBlurImg?: boolean, canBlur?: boolean}) => {
    // console.log('isBlurImg', isBlurImg, 'canBlur', canBlur);
    // console.log('window.config.sfwApp', window.config);
    // 对sfwApp小程序的图片进行模糊处理，管理后台的图片也进行模糊处理
    if(isBlurImg && canBlur) {
        if(url.indexOf("ai-data.424224.xyz") !== -1) {
            return 'https://static.image.424224.xyz/cdn-cgi/image/blur=100/' + url
        } else {
            return query.append(url, 'imageMogr2/blur/100x100')
        }
    } else {
        return url
    }
}