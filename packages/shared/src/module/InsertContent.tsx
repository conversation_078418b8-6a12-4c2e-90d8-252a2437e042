'use client'
// 快捷输入功能
import React, { useState, useEffect, useRef } from "react";
import { isMobileDevice } from "./global";
import { useTranslation } from "react-i18next";
const InsertContent = ({setValue}: any) => {
  const [focusedElement, setFocusedElement] = useState<HTMLInputElement | HTMLTextAreaElement | null>(null);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isShow, setIsShow] = useState(false);
  const { t } = useTranslation()
  // 记录当前聚焦的输入框
  useEffect(() => {
    const handleFocus = (e: Event) => {
      // input标签带有快捷输入属性才支持
      if ((e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) && e.target?.getAttribute('data-insert') === 'true') {
        setFocusedElement(e.target);
        isMobileDevice && setIsShow(true);
      }
    };

    const handleBlur = () => {
      setFocusedElement(null);
      setIsShow(false);
    }

    document.addEventListener("focus", handleFocus, true);
    document.addEventListener("blur", handleBlur, true);
    const updatePosition = () => {
      const viewport = window.visualViewport;
      if (viewport) {
        // 检测键盘弹起高度, 离底部距离12px
        const heightDiff = window.innerHeight - viewport.height + 6;
        setKeyboardHeight(heightDiff > 0 ? heightDiff : 0);
      }
    };
    // 监听 visualViewport 的变化
    window.visualViewport?.addEventListener("resize", updatePosition);
    window.visualViewport?.addEventListener("scroll", updatePosition);

    return () => {
      document.removeEventListener("focus", handleFocus, true);
      document.removeEventListener("blur", handleBlur, true);
    };
  }, []);

  // 插入内容到光标位置
  const insertText = (text: string) => {
    if (focusedElement) {
      // 将焦点重新设置到输入框
      focusedElement.focus();
      const { selectionStart, selectionEnd, value } = focusedElement;
      const newValue =
        value.slice(0, selectionStart || 0) +
        text +
        value.slice(selectionEnd || 0);
      if(focusedElement.name) {
        setValue(focusedElement.name, newValue);
      }
      // 更新输入框的值
      // focusedElement.value = newValue;

      // 更新光标位置
      const newCursorPosition = (selectionStart || 0) + text.length;
      focusedElement.setSelectionRange(newCursorPosition, newCursorPosition);
    }
  };

  return <>{isShow && <div className="fixed bottom-6 right-1 z-50 flex gap-2 rounded bg-white/95 dark:bg-gray-800/95 p-1 px-2.5 text-sm items-center border border-purple-600/75" style={{
    bottom: `${keyboardHeight}px`,
  }}>
    <span className="text-xs dark:text-gray-400">{t('app.cardEdit.quick_type')}:</span> <div className="rounded-lg bg-gray-300 dark:bg-gray-700 px-2.5 py-1 text-sm"
      onMouseDown={(e) => {
        e.preventDefault(); // 阻止默认行为，避免失焦
        insertText("{{user}}");
      }}>
      {'{{user}}'}
    </div>
    <div className="rounded-lg bg-gray-300 dark:bg-gray-700 px-2.5 py-1 text-sm" onMouseDown={(e) => {
      e.preventDefault(); // 阻止默认行为，避免失焦
      insertText("{{char}}");
    }}>
      {'{{char}}'}
    </div>
  </div>}</>
};

export default InsertContent;