
async function serverFetch({ url, lang }: { url: string, lang: string }) {
  if(process.env.NEXT_PUBLIC_ENV === 'dev') {
    return {}
  }
  try {
    const response = await fetch(process.env.NEXT_PUBLIC_API_HOST + url, {
      next: { 
        revalidate: 90 // 每90秒重新验证
      },
      headers: {
        'current-language': lang
      },
    })
    
    if (!response.ok) {
      console.log('fetchData failed', url, response.status)
      throw new Error('Failed to fetch data')
    }
    
    const data = await response.json();
    console.log('fetchData done', process.env.NEXT_PUBLIC_API_HOST + url, lang)
    return data;
  } catch (error) {
    console.log('Error serverFetch:', process.env.NEXT_PUBLIC_API_HOST + url, lang)
    // 没有缓存时返回默认空数据
    return {}
  }
}

export default serverFetch