const format = (timestamp: number, format: string) => {
    const date = new Date(timestamp)
    const padZero = (num: number) => String(num).padStart(2, '0');

    const tokens: any = {
        'YYYY': String(date.getFullYear()),
        'MM': pad<PERSON>ero(date.getMonth() + 1), // getMonth() 返回的月份是从0开始的
        'DD': padZero(date.getDate()),
        'HH': pad<PERSON><PERSON>(date.getHours()),
        'mm': padZero(date.getMinutes()),
        'ss': padZero(date.getSeconds())
    };

    return format.replace(/YYYY|MM|DD|HH|mm|ss/g, (match: string) => {
        return tokens[match]
    });
}

export default format