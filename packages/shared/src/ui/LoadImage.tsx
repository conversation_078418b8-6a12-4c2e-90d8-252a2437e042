import { useState } from 'react';
import Image from 'next/image';
import { RotateCw } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const LoadImage = ({src, alt, width, height, unoptimized, className}: any) => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [key, setKey] = useState(0); // 用于强制重新加载图片

  // 当图片加载完成时，触发此函数
  const handleLoadingComplete = () => {
    setIsLoading(false);
  };
  const handleError = () => {
    setIsLoading(false);
    setIsError(true); // 将加载失败状态设置为 true
  };
  // 处理重试加载
  const handleRetry = () => {
    setIsLoading(true); // 重置加载状态
    setIsError(false); // 重置错误状态
    setKey(prevKey => prevKey + 1); // 通过改变 key 强制重新加载图片
  };

  const aspect = Math.round(height / width * 100);

  return (
    <div className={`relative !max-w-full`} style={{paddingBottom: aspect + '%'}}>
      {/* 如果图片正在加载，显示加载中的图片 */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <RotateCw className="animate-spin text-blue-500" size={32} color="#a855f7" />
        </div>
      )}

      {/* 如果图片加载失败，显示错误占位图和重试按钮 */}
      {isError ? (
        <div className="absolute w-full inset-0 flex flex-col items-center justify-center">
          <span className="text-red-500 mb-2">{t('app.chat.load_fail')}</span>
          <button
            onClick={handleRetry}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            {t('app.chat.retry')}
          </button>
        </div>
      ) : (
        <div className='absolute w-full h-full overflow-hidden'><Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        unoptimized={unoptimized}
        className={`${className} duration-300 ease-in-out ${isLoading ? 'opacity-0' : 'opacity-100'}`}
        onLoad={handleLoadingComplete}
        onError={handleError}
        loading = 'lazy'
        /></div>
      )}
    </div>
  );
};

export default LoadImage;