'use client'
import classNames from 'classnames'
import type { ReactNode } from 'react'
import React from 'react'
import { createRoot } from 'react-dom/client'
import { LoadingToast } from '../Loading'
import { ArrowUpIcon } from '@heroicons/react/24/solid'

import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XCircleIcon,
} from '@heroicons/react/20/solid'

export type IToastProps = {
  type?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
  message: string
  children?: ReactNode
  onClose?: () => void
}

const defaultDuring = 2500

const Toast = ({
  type = 'info',
  message,
  children,
}: IToastProps) => {
  // sometimes message is react node array. Not handle it.
  if (typeof message !== 'string')
    return null

  return <div className={classNames(
    'fixed rounded-md p-4 my-12 z-50',
    'top-0',
    'left-[50%] -translate-x-1/2',
    type === 'success' ? 'bg-green-50' : '',
    type === 'error' ? 'bg-red-50' : '',
    type === 'warning' ? 'bg-yellow-50' : '',
    type === 'info' ? 'bg-blue-50' : '',
  )}>
    <div className="flex">
      <div className="flex-shrink-0">
        {type === 'success' && <CheckCircleIcon className="w-5 h-5 text-green-400" aria-hidden="true" />}
        {type === 'error' && <XCircleIcon className="w-5 h-5 text-red-400" aria-hidden="true" />}
        {type === 'warning' && <ExclamationTriangleIcon className="w-5 h-5 text-yellow-400" aria-hidden="true" />}
        {type === 'info' && <InformationCircleIcon className="w-5 h-5 text-blue-400" aria-hidden="true" />}
      </div>
      <div className="ml-3">
        <h3 className={
          classNames(
            'text-sm font-medium',
            type === 'success' ? 'text-green-800' : '',
            type === 'error' ? 'text-red-800' : '',
            type === 'warning' ? 'text-yellow-800' : '',
            type === 'info' ? 'text-blue-800' : '',
          )
        }>{message}</h3>
        {children && <div className={
          classNames(
            'mt-2 text-sm',
            type === 'success' ? 'text-green-700' : '',
            type === 'error' ? 'text-red-700' : '',
            type === 'warning' ? 'text-yellow-700' : '',
            type === 'info' ? 'text-blue-700' : '',
          )
        }>
          {children}
        </div>
        }
      </div>
    </div>
  </div>
}

Toast.notify = ({
  type,
  message,
  duration,
}: Pick<IToastProps, 'type' | 'message' | 'duration'>) => {
  if (typeof window === 'object') {
    const holder = document.createElement('div')
    const root = createRoot(holder)

    root.render(<Toast type={type} message={message} />)
    document.body.appendChild(holder)
    setTimeout(() => {
      if (holder)
        holder.remove()
    }, duration || defaultDuring)
  }
}

let holder: any = null;
let hideLoading: any = null;
Toast.showLoading = (message: string, isCover?: boolean) => {
  if (typeof window === 'object' && holder === null) {
    holder = document.createElement('div')
    if(isCover) {
      holder.className = 'absolute z-[51] w-full h-full left-0 top-0'
    }
    const root = createRoot(holder)
    root.render(<LoadingToast msg={message}/>)
    document.body.appendChild(holder)
  }
  !hideLoading && (hideLoading = () => {
    if (holder)
      holder.remove()
      holder = null;
      hideLoading = null;
  });
  return hideLoading
}
Toast.hideLoading = () => {
  if (holder) {
    holder.remove()
    holder = null;
    hideLoading = null;
  };
}


const GuideCloseApp = ({message}: {message?: string}) => {
  return <div className='absolute z-[51] left-3 top-0 text-red-500'>
    <div><ArrowUpIcon className='w-12 h-12' /></div>
    <div>{message}</div>
  </div>
}

export type IGuideProps = {
  duration?: number
  message?: string
  children?: ReactNode
  onClose?: () => void
}
// 弹出浮层引导
Toast.guide = ({
  message,
  duration,
}: IGuideProps) => {
  if (typeof window === 'object') {
    const holder = document.createElement('div')
    const root = createRoot(holder)

    root.render(<GuideCloseApp message={message} />)
    document.body.appendChild(holder)
    setTimeout(() => {
      if (holder)
        holder.remove()
    }, duration || 3000)
  }
}

export default Toast
