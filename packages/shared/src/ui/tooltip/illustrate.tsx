import useDialogAlert from '../../hook/useDialogAlert';
import { useTranslation } from 'react-i18next'

const Illustrate = ({children, title, desc, className, isMd = false}: {children?: React.ReactNode, title: string, desc: string, className?: string, isMd?: boolean}) => {
    const { t } = useTranslation()
    const showTips = (e: any) => {
        e.stopPropagation();
        dialogAlert.show({title: title, desc: desc, alertStatus: 2, isMd})
    }
    const dialogAlert = useDialogAlert();
    return <button type='button' className={`inline-block text-center text-gray-500 dark:hover:text-white border-gray-500 dark:hover:border-white rounded-full border w-4 h-4 text-xs ${className}`} onClick={showTips}>{children || '?'}</button>
}

export default Illustrate