import cn from 'classnames';

const colors = [
    'dark:border-gray-700 dark:bg-gray-900 text-blue-300 border-blue-300',
    'dark:border-gray-700 dark:bg-gray-900 text-orange-300 border-orange-300',
    'dark:border-gray-700 dark:bg-gray-900 text-red-300 border-red-300',
    'dark:border-gray-700 dark:bg-gray-900 text-pink-300 border-pink-300',
    'dark:border-gray-700 dark:bg-gray-900 text-purple-300 border-purple-300'
]

const Tags = ({ tags, switchSubtags, className }: any) => {
    return (
        <ul className={cn('flex flex-wrap text-xs', className)}>
            {tags?.map((cardLabel: any, index: any) => {
                return <li key={index} className={cn('border rounded overflow-hidden mr-1 mb-1 bg-white', colors[index % colors.length])}><button className={cn((switchSubtags && cardLabel !== 'NSFW')? 'hover:underline hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer' : 'cursor-default', 'relative px-1')} type='button' onClick={(e) => {
                    e.stopPropagation();
                    if(cardLabel === 'NSFW') return;
                    switchSubtags && switchSubtags(cardLabel);
                }}>{cardLabel}</button></li>
            })}
        </ul>
    );
};

export default Tags;