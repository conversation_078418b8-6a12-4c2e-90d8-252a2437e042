import React, { useContext, useState } from "react";
import Modal from '@share/src/ui/dialog/Modal'
import { useTranslation } from "react-i18next";
import { AuthContext } from "@share/src/authContext";
import Illustrate from '../tooltip/illustrate';
import { createPortal } from 'react-dom';

const AnalysisBtn = ({role}: any) => {
    const [openAnylisis, setOpenAnylisis] = useState(false)
    const { t } = useTranslation()
    const auth = useContext(AuthContext);
    const user = auth?.user
    return <div>
        <button className='flex items-center p-1 text-blue-500' onClick={async (e) => {
            e.stopPropagation();
            setOpenAnylisis(true)
        }}>{t('app.cardEdit.data')}</button>
        {openAnylisis && createPortal(<Modal isOpen={openAnylisis} onClose={() => { setOpenAnylisis(false) }}>
                <div>
                    <div className="w-full pt-10 pb-2">
                        <table className='dark:bg-gray-900 bg-white text-gray-500 border-collapse w-full'>
                            <thead className='dark:bg-slate-700 bg-slate-300 dark:text-slate-100'>
                                <tr className='text-center sm:text-sm'>
                                    <th className='border py-2 px-1 border-slate-600'>{t('app.cardEdit.cardName')}</th>
                                <th className='border py-2 px-4 border-slate-600'>{t('app.index.author')}</th>
                                {/* <th className='border py-2 px-4 border-slate-600'>{t('app.cardEdit.init_heat')}</th> */}
                                <th className='border py-2 px-4 border-slate-600'>{t('app.cardEdit.deduct_heat')} <Illustrate title={t('app.cardEdit.deduct_heat_title')} desc={t('app.cardEdit.deduct_heat_desc')}></Illustrate></th>
                                <th className='border py-2 px-4 border-slate-600'>{t('app.cardEdit.final_heat')}</th>
                                <th className='border py-2 px-4 border-slate-600'>{t('app.cardEdit.likeCount')}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr className='text-center sm:text-base dark:text-slate-300'>
                                <td className='border border-slate-700 py-1 px-1'>{role.card_name}</td>
                                <td className='border border-slate-700 py-1 px-1'>{user?.nickname}</td>
                                {/* <td className='border border-slate-700 py-1 px-1'>{role.popular_count || 0}</td> */}
                                <td className='border border-slate-700 py-1 px-1'>{role.deducted_popular_count || 0}</td>
                                <td className='border border-slate-700 py-1 px-1'>{role.final_popular_count || 0}</td>
                                <td className='border border-slate-700 py-1 px-1'>{role.like_count || 0}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </Modal>, document.body)}
    </div>
}

export default AnalysisBtn