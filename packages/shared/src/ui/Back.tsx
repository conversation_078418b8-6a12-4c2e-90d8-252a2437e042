import {ArrowUturnLeftIcon} from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next';
import { useBackButton } from '@share/src/hooks/useBackButton';

type BackProps = {
    // 是否隐藏文本
    hideTxt?: boolean
}
const Back: React.FC<BackProps> = ({
    hideTxt
}) => {
    const { backHandler } = useBackButton();
    const { t } = useTranslation();
    return <button className='p-2 flex text-base items-center pt-3' onClick={backHandler}>
          <ArrowUturnLeftIcon className='w-5 h-5 mr-1 text-purple-500' />
          {!hideTxt && t('app.common.back')}
      </button>
}

export default Back;