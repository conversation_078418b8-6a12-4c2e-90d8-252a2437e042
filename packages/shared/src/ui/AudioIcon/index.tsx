"use client";
import type { FC } from 'react'
import React from 'react';
import s from './style.module.css'
import cn from 'classnames'
import Image from 'next/image'
import IconPlayLoud1 from './play_loud_1.svg'
import IconPlayLoud2 from './play_loud_2.svg'
import IconAudio from './audio.svg'

export type IProps = {
    className?: string,
    status?: number
}
type IIcon = Pick<IProps, 'status'>;

export enum STATUS {
    'standby',
    'loading',
    'playing'
}

const Icon:FC<IIcon> = ({status = STATUS.standby}) => {
    switch (status) {
        case STATUS.standby:
            return <>
                <Image className={cn(s.IconAudio)} width={20} height={20} src={IconAudio} alt='audio' />
                <Image className={cn(s.loud)} width={22} height={22} src={IconPlayLoud1} alt='audio' />
            </>
            break;
        case STATUS.loading:
            return <>
                <div className={cn(s.playTransition)}></div>
            </>
            break;
        case STATUS.playing:
            return <>
                <Image className={cn(s.IconAudio)} width={20} height={20} src={IconAudio} alt='audio' />
                <Image className={cn(s.loud, s.playAnimation)} width={22} height={22} src={IconPlayLoud1} alt='audio' />
                <Image className={cn(s.loud, s.playAnimation1)} width={22} height={22} src={IconPlayLoud2} alt='audio' />
            </>
            break;
    }
    
}

const Loader: FC<IProps> = ({
    className,
    status,
}) => {
    return (
        <div className={cn('inline-block relative h-5 flex', className && `${className}`)}>
            <Icon status={status} />
        </div>
    );
};

export default Loader;
