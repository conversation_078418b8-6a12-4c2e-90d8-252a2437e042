// app/components/ui/Loader.tsx
"use client";

import React from 'react';
import { RotateCw, WifiOff } from 'lucide-react'; // Using a spinning icon from lucide-react
import { useTranslation } from 'react-i18next';

const Loader = ({msg}: ILoadingToast) => {
    return (
        <div className="fixed z-50 left-0 right-0 top-0 -translate-y-12 bottom-0 flex flex-col justify-center items-center w-24 h-24 m-auto dark:bg-black/75 bg-white rounded drop-shadow-u dark:drop-shadow-none">
            <RotateCw className="animate-spin text-blue-500" size={32} color="#a855f7" />
            <p className='mt-2 '>{msg}</p>
        </div>
    );
};

type ILoadingToast = {
    msg?: string
}
export const LoadingToast = ({msg}: ILoadingToast) => {
    return (
        <div className="fixed z-50 left-0 right-0 top-0 -translate-y-12 bottom-0 flex flex-col justify-center items-center w-24 h-24 m-auto dark:bg-black/75 bg-white rounded drop-shadow-u dark:drop-shadow-none">
            <RotateCw className="animate-spin text-blue-500" size={32} color="#a855f7" />
            <p className='mt-2'>{msg}</p>
        </div>
    );
}
export const LoadingToastFailed = ({error, retry}: any) => {
    const {t} = useTranslation();
    return (
        <div className="fixed z-50 left-0 right-0 top-0 -translate-y-12 bottom-0 flex flex-col justify-center items-center w-60 h-48 m-auto dark:bg-black/75 bg-white bg-white/75 rounded text-center">
            <div>
                <WifiOff className="text-blue-500 w-16 h-16 mx-auto" />
                <p>{t('app.common.load_err')}</p>
                {(error.name || error.message) && <p className='text-xs truncate w-46 mx-auto'>msg：{error.name} {error.message}</p>}
                <button className='mt-5 w-32 p-1.5 px-3 bg-purple-500 text-white rounded-lg text-sm' type='button' onClick={() => {retry && retry()}}>{t('app.common.retry')}</button>
            </div>
        </div>
    );
}

export default Loader;
