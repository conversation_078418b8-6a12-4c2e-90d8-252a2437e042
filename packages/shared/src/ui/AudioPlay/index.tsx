"use client";
import type { FC } from 'react'
import React, { useState } from 'react';
import AudioIcon, { STATUS } from '../AudioIcon'
import useRequest from '../../hook/useRequest';
import { useTranslation } from 'react-i18next'

const runsOnServerSide = typeof window === 'undefined'
let ttsCache
if(runsOnServerSide) {
  ttsCache = {}
} else {
  ttsCache = JSON.parse(localStorage.getItem('tts') || '{}')
}

export type IProps = {
  msgId?: string,
  url?: string,
  // 缓存msgid版本
  version?: string,
}

let audio: any;

export const stopAudio = () => {
  audio && audio.pause();
}

const AudioPlay: FC<IProps> = ({
  msgId = '',
  url = '',
  version = ''
}) => {
    const { t } = useTranslation()
    const request = useRequest();
    const [status, setStatus] = useState(STATUS.standby)
    const play = async () => {
      let voice_url = '';
      if(status === STATUS.standby) {
        audio && audio.pause();
        setStatus(STATUS.loading)
        if(url) {
          voice_url = url;
        } else if(ttsCache[msgId + version + '']) {
          voice_url = ttsCache[msgId + version + ''];
        } else {
          try{
            const ttsRes = await request(`/tts?message_id=${msgId}&version=${version}`)
            voice_url = ttsRes.voice_url
            ttsCache[msgId + version + ''] = voice_url
            localStorage.setItem('tts', JSON.stringify(ttsCache))
          } catch(e) {
            setStatus(STATUS.standby)
            return;
          }
        }
        audio = new Audio(voice_url);
        audio.play();
        audio.addEventListener("playing", function() {
          console.log('audio playing');
          setStatus(STATUS.playing)
          audio.play();
        });
        audio.addEventListener("pause", function() {
          console.log('audio pause');
          setStatus(STATUS.standby)
        });
        audio.addEventListener("ended", function() {
          console.log('audio ended');
          setStatus(STATUS.standby)
        });
      } else {
        audio.pause();
      }
    }

    return (
      <button type='button' className='mr-0.5 flex p-1 xs flex items-center dark:hover:text-white' onClick={play}>
        <AudioIcon className='' status={status} />
        {/* {t('app.dialog.play')} */}
      </button>
    );
};

export default AudioPlay;
