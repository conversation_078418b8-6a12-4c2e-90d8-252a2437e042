"use client";
import React, { useEffect, useRef, useState } from 'react';
import type { FC } from 'react'
import {stopAudio} from '../../AudioPlay';
import { useForm, SubmitHandler } from "react-hook-form"
import Toast from '../../toast'
import useRequest from '../../../hook/useRequest';
import type { IProps, FormData } from './type';
import s from './style.module.scss'
import useCountToken from './useCountToken';
import SimpleIntro from './form/simple_intro';
import Tags from './form/tags';
import SubTags from './form/subTags';
import cn from 'classnames';
import { useTranslation } from 'react-i18next'
import Modal from '../Modal';
import setDefault from './setGroupDefault';
import Author from './form/author';
import Introduction from '@share/src/ui/dialog/CreateCard/group-form/introduction';
import { RoleType } from '../../card';
import useDialogAlert from '@share/src/hook/useDialogAlert';
import Name from '@share/src/ui/dialog/CreateCard/group-form/name';

const runsOnServerSide = typeof window === 'undefined'
// 暂存用户更新的数据
let storeFormData: any = {};
const CreateGroupCard: FC<IProps> = ({ onConfirm, onCancel, isOpen, msg, userInfo }) => {
  const { t } = useTranslation()
  const [sumTokenLimit, setSumTokenLimit] = useState(10000)
  const [prevTokens, setPrevTokens] = useState<any>({});
  const [roleConfig, setRoleConfig] = useState<any>({})
  const request = useRequest()
  // 来源卡片编辑还是卡片上传
  const isReadMsg = msg?.isEdit || msg?.isFromImg;
  const [loading, setLoading] = useState(false)
  const dialogAlert = useDialogAlert()
  const [customLevelType, setCustomLevelType] = useState<any>(null)
  // 如果服务端是高级模式，不支持切换回普通模式
  const {
    register,
    handleSubmit,
    watch,
    reset,
    setValue,
    formState: { errors },
  } = useForm<FormData>({mode: 'onChange'})
  
  const onSubmit: SubmitHandler<FormData> = async (data) => {
    storeFormData = data;
    if(tokens.sum_book.count / tokens.sum_book.max > 1) {
      Toast.notify({
        type: 'warning',
        message: t('app.dialog.sum_token_exceed')
      })
      return;
    }
    if(msg?.roles?.some((role: RoleType) => role.private_card)) {
      dialogAlert.show({
        title: t('app.cardEdit.has_private_card_title'),
        desc: t('app.cardEdit.has_private_card_desc'),
        alertStatus: '-1'
      })
      return;
    }
    
    const formData = new FormData()
    if(storeFormData.sub_tags && typeof storeFormData.sub_tags == 'string') {
      storeFormData.sub_tags = [storeFormData.sub_tags]
    }
    storeFormData.publish_card = true;
    msg?.id && (storeFormData.id = msg?.id);
    if(roleConfig.chat_group_edit) {
      storeFormData.role_ids = roleConfig.chat_group_edit.role_ids
    }
    const hideLoading = Toast.showLoading(t('app.dialog.creating'))
    try{
      const data = await request('/roles/group/update', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(storeFormData)
      });
      console.log(data);
      hideLoading();
      if(data.error_code === 0) {
        if(msg?.isPublic) {
          Toast.notify({type: 'success', message: t('app.mine.public_success')})
        } else {
          Toast.notify({type: 'success', message: msg?.isEdit? t('app.mine.update_success') : t('app.dialog.create_success')})
        }
      } else {
        Toast.notify({type: 'error', message: data?.message})
      }
      onConfirm()
    } catch(e: any) {
      console.error('create or update Card error:', e);
      hideLoading();
      if(e.status == 403) {
        Toast.notify({type: 'error', message: t('app.cardEdit.auditing_desc')})
      } else {
        Toast.notify({type: 'error', message: t('app.dialog.create_fail')})
      }
    }
  }
  stopAudio();
  const formValues = watch();
  // console.log('formValues', formValues);
  const {tokens} = useCountToken(formValues, prevTokens, sumTokenLimit, userInfo)
  const getConfig = async () => {
    Toast.showLoading('Loading');
    try {
      const res = await request('/roles/group/create_config' + (msg?.id? `?group_id=${msg?.id}` : ''))
      setRoleConfig(res)
      setDefault(res.chat_group_edit, isReadMsg, msg, {}, reset);
      setLoading(true)
    } catch(e) {
      Toast.notify({
        type: 'error',
        message: t('app.common.load_err')
      })
      onCancel()
    } finally {
      Toast.hideLoading();
    }
  }
  useEffect(() => {
    getConfig();
  }, []);

  return (
    <>
    <Modal onClose={() => {onCancel()}} isOpen={loading && isOpen} conWidth='sm:w-[950px]'>
      <div className='min-h-20'>
        <div className="flex min-h-full flex-1 flex-col justify-center">
        <div className="max-h-[100vh]">
        {msg?.isPublic?
          <form className="" action="#" method="POST" onSubmit={handleSubmit(onSubmit)}>
            <div className='max-h-[88vh] overflow-auto space-y-6 px-1 sm:px-8 pt-4'>
              <Name errors={errors} register={register} s={s} />
              <SimpleIntro errors={errors} register={register} s={s} msg={msg} formValues={formValues} />
              {
                msg?.admin && <Tags errors={errors} register={register} s={s} msg={msg} roleConfig={roleConfig} />
              }
              <Introduction errors={errors} register={register} formValues={formValues} tokens={tokens} setValue={setValue} />
              <Author errors={errors} register={register} s={s} msg={msg} roleConfig={roleConfig} />
              <SubTags errors={errors} register={register} s={s} msg={msg} roleConfig={roleConfig} />
            </div>
            
            <div className='max-h-[12vh]'>
              <div className='px-8 pt-2 mb-0.5 flex flex-row-reverse'>
                <button key={1} className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3' type="submit">{msg?.isPublic? t('app.cardEdit.public') : msg?.isEdit? t('app.common.update') : t('app.common.submit')}</button>
                <button className='p-2 px-5 dark:bg-gray-800 bg-gray-300 rounded text-sm' type='button' onClick={() => {onCancel()}}>{t('app.common.cancel')}</button>
              </div>
            </div>
          </form> :
          <form className="" action="#" method="POST" onSubmit={handleSubmit(onSubmit)}>
            <div className='max-h-[88vh] overflow-auto space-y-6 px-1 sm:px-8 pt-4'>
            编辑
            </div>
          </form>}
        </div>
      </div>
      </div>
      </Modal>
    </>
  );
};

export default CreateGroupCard;