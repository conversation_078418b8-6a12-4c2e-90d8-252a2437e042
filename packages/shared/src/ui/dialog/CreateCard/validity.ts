
// 检验总tokens是否溢出
export const validity = ({storeFormData, tokens, Toast, t}: any) => {
    // console.log('storeFormData', storeFormData);
    // 统计总tokens是否溢出
    if(tokens.sum_book.count / tokens.sum_book.max > 1) {
        Toast.notify({
          type: 'warning',
          message: t('app.dialog.sum_token_exceed')
        })
        return true;
    }
}

// 检验是否包含{{user}}
export const validateNoUser = ({t}: any) => {
  return {
    noUser: (value: any) => {
      return !/\{\{user\}\}/.test(value) || t('app.cardEdit.no_user_or_Char')
    }
}
}
// 检验是否包含用户角色名
export const validateUserName = ({formValues, t, errMsg}: any) => {
  return {
      validateUserName:(value: any) => {
          if (!value ) return true;
          
          const userRoleName = formValues['user_role_name'];
          // 检查是否包含用户角色名
          const hasRoleName = userRoleName && value.includes(userRoleName);
          return !hasRoleName || errMsg || t('no_user_name', {userName: userRoleName, keyPrefix: 'app.cardEdit'})
      }
  }
}
// 检验是否包含用户角色名和AI名
export const validateCharName = ({formValues, t}: any) => {
  return {
      validateCharName:(value: any) => {
        // console.log('value', value);
          if (!value ) return true;
          const charName = formValues['name'];
          // 检查是否包含AI名字或用户角色名
          const hasAIName = charName && value.includes(charName);
          return !hasAIName || t('no_char_name', {charName: charName,  keyPrefix: 'app.cardEdit'})
      }
  }
}
