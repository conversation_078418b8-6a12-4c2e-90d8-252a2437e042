
export type IProps = {
    onCancel: Function,
    onConfirm: Function,
    isOpen?: boolean,
    msg?: Record<string, any>
    isPublic?: boolean
    userInfo?: any,
    config?: any
  }
  export type FormData = {
    id?: string,
    name: string
    user_role_name: string
    introduction: string
    description: string
    // first_message: string
    personality: string
    example_dialog: string
    scenario: string,
    tags?: string,
    sub_tags?: string[],
    card_name?: string,
    speaker_id: string,
    simple_intro: string,
    nsfw: boolean,
    statusBlockEnable: string,
    status_block: string,
    statusBlockType: string,
    status_block_init: string,
    status_block_rule: string,
    chat_type: string,
    role_avatar?: string
    book_id?: string
    support_product_ids: any[]
    card_model: 'normal' | 'expert'
    muilte_examples: any[]
    replay_len_ratio: number
    muilte_scenes: any[]
    support_all_language: boolean
    support_languages: string[]
    role_book: any
    author?: any
    image_nsfw: boolean
    real_role?: boolean
    // 玩法类型
    play_type?: string
  }