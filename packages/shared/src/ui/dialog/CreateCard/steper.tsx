import cn from "classnames"
import { ChevronDoubleRightIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
const Steper = ({step}: any) => {
    const { t } = useTranslation()
    return <div className="pt-3">
    <ol className="flex items-center w-full p-3 space-x-2 text-sm font-medium text-center text-gray-400 sm:text-base sm:p-4 sm:space-x-4 rtl:space-x-reverse justify-center">
        <li className={cn("flex items-center", step === 1 && 'text-blue-500')}>
            <span className={cn("flex items-center justify-center w-5 h-5 me-2 text-xs border rounded-full shrink-0", step === 1 && 'border-blue-500')}>
                1
            </span>
            {t('app.cardEdit.step1')}
            <ChevronDoubleRightIcon className="w-4 h-4 ms-2 sm:ms-4 rtl:rotate-180 text-gray-100" />
        </li>
        <li className={cn("flex items-center", step === 2 && 'text-blue-500')}>
            <span className={cn("flex items-center justify-center w-5 h-5 me-2 text-xs border rounded-full shrink-0", step === 2 && 'border-blue-500')}>
                2
            </span>
            {t('app.cardEdit.step2')}
            <ChevronDoubleRightIcon className="w-4 h-4 ms-2 sm:ms-4 rtl:rotate-180 text-gray-100" />
        </li>
        <li className={cn("flex items-center", step === 3 && 'text-blue-500')}>
        <span className={cn("flex items-center justify-center w-5 h-5 me-2 text-xs border rounded-full shrink-0", step === 3 && 'border-blue-500')}>
                3
            </span>
            状态栏
        </li>
    </ol>
    </div>
}

export default Steper