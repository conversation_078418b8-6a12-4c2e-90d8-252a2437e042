// 替换表单内容
const replaceCharAndUser = async ({matchKeys, setValue, formValues, t, userRoleName, name}: any) => {
    const reg = new RegExp(name, 'g');
    const userReg = new RegExp(userRoleName, 'g');
    matchKeys.forEach((key: string) => {
        console.log('replaceCharAndUser', key);
        const description = formValues[key];
        
        const descriptionWithChar = description.replace(reg, '{{char}}');
        setValue(key, descriptionWithChar);
    
        if(userRoleName !== '') {
            const descriptionWithUser = descriptionWithChar.replace(userReg, '{{user}}');
            setValue(key, descriptionWithUser);
        }
    })
}

// 替换数组表单内容
const replaceArrayFields = (
    formValues: any,
    config: { path: string; fields: string[] }[],
    { name, userRoleName, setValue }: { name: string; userRoleName: string; setValue: (path: string, value: string) => void }
) => {
    const reg = new RegExp(name, 'g');
    const userReg = new RegExp(userRoleName, 'g');
    config.forEach(({ path, fields }) => {
        const items = formValues[path];
        if (!items?.length) return;

        items.forEach((item: any, index: number) => {
            fields.forEach(field => {
                const content = item[field];
                if (!content) return;

                let newContent = content.replace(reg, '{{char}}');
                if (userRoleName !== '') {
                    newContent = newContent.replace(userReg, '{{user}}');
                }
                setValue(`${path}.${index}.${field}`, newContent);
            });
        });
    });
};

// 检查数组表单错误
const checkArrayFieldErrors = (
    formValues: any,
    errors: any,
    config: { path: string; fields: string[] }[]
): boolean => {
    for (const { path, fields } of config) {
        const items = formValues[path];
        if (!items?.length) continue;
        
        for (let i = 0; i < items.length; i++) {
            for (const field of fields) {
                const err = errors?.[path]?.[i]?.[field]?.type;
                if (err === 'validateCharName' || err === 'validateUserName') {
                    return true;
                }
            }
        }
    }
    return false;
};

// 如果存在validateCharName或者validateUserName类型的错误，弹窗提示，替换
const autoReplace = async ({errors, setValue, formValues, confirm, t, Toast, trigger}: any) => {
    console.log('errors', errors);
    // 检查一级表单错误
    const keys = ['description', 'status_block', 'status_block_init', 'status_block_rule', 'example_dialog'];
    let hasCharOrUserError = keys.some((key) => {
        return errors?.[key]?.type === 'validateCharName' || errors?.[key]?.type === 'validateUserName'
    })
    // 检查数组表单错误
    const validationConfig = [
        { path: 'muilte_scenes', fields: ['first_message', 'scenario'] },
        { path: 'muilte_examples', fields: ['user', 'char'] },
        { path: 'role_book', fields: ['content'] }
    ];

    hasCharOrUserError = hasCharOrUserError || checkArrayFieldErrors(formValues, errors, validationConfig);

    console.log('hasCharOrUserError', hasCharOrUserError);
    
    const matchKeys = keys.filter((key) => {
        return errors?.[key]?.type === 'validateCharName' || errors?.[key]?.type === 'validateUserName'
    })
    
    if(hasCharOrUserError) {
        const res = await confirm.show({
          title: t('app.dialog.cant_submit'),
          desc: t('app.dialog.cant_submit_desc'),
          comfirmBtn: t('app.dialog.yes'),
          cancelBtn: t('app.dialog.no'),
        })
        if(res.confirm) {
            const userRoleName = formValues['user_role_name'];
            const name = formValues['name'];
            replaceCharAndUser({matchKeys, setValue, formValues, t, userRoleName, name});
            replaceArrayFields(formValues, validationConfig, { name, userRoleName, setValue });
            
            trigger()
            Toast.notify({
                type: 'success',
                message: t('app.dialog.update_success')
            });
        }
    }
    
}

export default autoReplace