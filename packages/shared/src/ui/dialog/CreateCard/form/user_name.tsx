import { useTranslation } from 'react-i18next'

const UserName = ({errors, register, s, formValues}: any) => {
    const { t } = useTranslation()
    return <>
        <div className='sm:w-full sm:max-w-sm'>
            <label htmlFor="user_role_name" className="block text-sm font-medium leading-6 ">
            {t('app.cardEdit.user_role_name')} {errors.user_role_name && (
            <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.user_role_name.message}</span>
            )}
            </label>
            <div className="mt-2">
            <input
                id="user_role_name"
                {...register("user_role_name", {
                    maxLength: {
                        value: 18,
                        message: t('app.dialog.len_18')
                    },
                    validate: {
                        // 不能和AI角色名name重复
                        repeat: (value: any) => {
                            const aiName = formValues['name'];
                            return !aiName || value !== aiName || t('app.cardEdit.ai_name_repeat');
                        }
                    }
                })}
                type="text"
                autoComplete="user_role_name"
                placeholder={t('app.cardEdit.user_role_name_placeholder')}
                className={`${s.inputStyle} ipt`}
            />
            </div>
        </div>
    </>
}

export default UserName