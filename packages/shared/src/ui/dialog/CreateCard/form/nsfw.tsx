import { useTranslation } from 'react-i18next'

const NSFW = ({errors, register, s, msg, subTagList}: any) => {
    const { t } = useTranslation()
    return <>
        {<div className="col-span-full flex items-center">
                <label htmlFor="nsfw" className="text-sm font-medium leading-6 ">
                NSFW
                </label>
                <div className="ml-4">
                    <div className='flex gap-2 gap-y-1 text-gray-400 flex-wrap'>
                    <label className='text-white flex items-center'><input id='nsfw' className='mr-1' {...register("nsfw")} type="checkbox" />NSFW</label>
                    </div>
                </div>
              </div>}
    </>
}

export default NSFW