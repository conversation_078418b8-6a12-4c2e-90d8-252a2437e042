import { useEffect } from 'react';
import { useTranslation } from 'react-i18next'

const Lang = ({errors, register, userInfo, formValues, setValue, levelType}: any) => {
    const { t } = useTranslation()
    const support_all_language = formValues['support_all_language'];
    const support_languages = formValues['support_languages'];
    const langs = userInfo?.language_list?.map((elem: any) => {
      return elem.key
    });
    // console.log('langs', langs);
    // console.log('support_all_language', support_all_language, support_languages);
    useEffect(() => {
      if (support_all_language) {
          setValue('support_languages', langs);
      }
    }, [support_all_language]);
    return <>
        {levelType != 'normal' && <div className="col-span-full">
                <label htmlFor="subTags" className="block text-sm font-medium leading-6 ">
                {t('app.cardEdit.lang_title')} {errors.support_languages && (
                      <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.support_languages.message}</span>
                    )}
                </label>
                <div className="mt-2">
                    <div className='flex gap-2 gap-y-1 text-gray-400 flex-wrap'>
                    {userInfo?.language_list?.map((lang: any) => {
                      return <label className='mr-2' key={lang.key}><input className='mr-0.5' {...register("support_languages", { required: {
                        value: true, message: t('app.dialog.require')
                      } })} type="checkbox" value={lang.key} />{lang.name}</label>
                    })}
                    <label className='mr-2'><input className='mr-0.5' {...register("support_all_language")} type="checkbox" />{t('app.common.support_all_lang')}</label>
                    </div>
                </div>
              </div>}
    </>
}

export default Lang