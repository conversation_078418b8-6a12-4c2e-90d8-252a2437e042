import { useTranslation } from 'react-i18next'
import AutoTextareaHeight from '../AutoTextareaHeight'
import AudioPlay, {stopAudio} from '../../../AudioPlay';

const Speaker = ({errors, register, s, msg, tokens, watch, roleConfig}: any) => {
    const { t } = useTranslation()
    const speakId = watch("speaker_id") || msg?.speaker_id;
    // console.log('msg?.speakList', msg?.speakList);
    const speakers = roleConfig?.speakers;
    const speaker = speakers?.find((speaker: any) => {
      return speaker.speaker_id == speakId
    });
    let speakerSampleUrl: string = '';
    if(speaker) {
      speakerSampleUrl = speaker?.sample_url
    } else if(speakers) {
      speakerSampleUrl = speakers[0].sample_url
    }
    return <>
        <div className="col-button-full flex items-center mt-8">
                  <label htmlFor="speaker_id" className="text-sm font-medium leading-6 ">
                  {t('app.dialog.voice')}
                  </label>
                  <div className="ml-2  flex">
                  {speakers && <select className='w-36 dark:bg-gray-800 bg-gray-200 p-1 mr-2 outline-none' {...register("speaker_id")}>
                    {
                      speakers.map((speaker: any) => {
                        return <option className='w-24 p-1' key={speaker.speaker_id} value={speaker.speaker_id}>
                          {speaker.speaker_name}
                          </option>
                      })
                    }
                  </select>}
                  <AudioPlay url={speakerSampleUrl} />
                  </div>
                </div>
    </>
}

export default Speaker