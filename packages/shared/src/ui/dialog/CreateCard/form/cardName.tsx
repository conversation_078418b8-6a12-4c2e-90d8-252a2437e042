import { useTranslation } from 'react-i18next'

const CardName = ({errors, register, s}: any) => {
    const { t } = useTranslation()
    return <>
        <div className='sm:w-full sm:max-w-sm'>
            <label htmlFor="cardName" className="block text-sm font-medium leading-6 ">
            {t('app.dialog.cardName')} {errors.card_name? (
            <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.card_name.message}</span>
            ) : <span className='text-xs mt-1'>*</span>}
            </label>
            <div className="mt-2">
            <input
                id="cardName"
                {...register("card_name", { required: t('app.dialog.require'), maxLength: {
                value: 18,
                message: t('app.dialog.len_18')
                } })}
                type="name"
                autoComplete="cardName"
                placeholder={t('app.dialog.name_place_holder')}
                className={`${s.inputStyle} ipt`}
            />
            </div>
        </div>
    </>
}

export default CardName