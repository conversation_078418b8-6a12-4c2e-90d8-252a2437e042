
import AutoTextareaHeight from '../AutoTextareaHeight'
import { useTranslation } from 'react-i18next'
import Illustrate from '../../../tooltip/illustrate';

const ChatType = ({register, msg, levelType}: any) => {
    const { t } = useTranslation()
    return <>
    {levelType != 'normal' &&
        <div className="col-span-full">
                  <label htmlFor="chat_type" className="text-sm font-medium leading-6 mr-1 align-middle">
                    {t('app.cardEdit.ai_formate')}
                    <Illustrate title={t('app.cardEdit.reply_title')} desc={t('app.cardEdit.reply_desc')} className='ml-1 mr-1' />
                  </label>
                  <label className='mr-2 align-middle' htmlFor="chat_type"><input className='mr-0.5' id='chat_type' {...register("chat_type", { required: true })} type="radio" value="Chat" />{t('app.dialog.chat_model')}</label>
                  <label className='mr-2 align-middle' htmlFor="RolePlay"><input className='mr-0.5' id='RolePlay' {...register("chat_type", { required: true })} type="radio" value="RolePlay" />{t('app.dialog.role_model')}</label>
                  <label className='mr-2 align-middle' htmlFor="general"><input className='mr-0.5' id='general' {...register("chat_type", { required: true })} type="radio" value="General" />{t('app.cardEdit.normal_model')}</label>
                </div> }
    </>
}

export default ChatType