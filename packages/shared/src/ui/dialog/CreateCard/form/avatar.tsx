import { useState } from "react";
import { UserCircleIcon } from '@heroicons/react/24/solid'
import Image from 'next/image'
import <PERSON>ropper from "react-easy-crop";
import getCroppedImg from '../../../../cropImg'
import React from "react";
import cn from 'classnames';
import { useTranslation } from 'react-i18next'
import useDialogAlert from "@share/src/hook/useDialogAlert";


const Avatar = ({ errors, register, s, msg, setCropImg, hasImg, setHasImg, step }: any) => {
  const { t } = useTranslation()
  const [avatar, setAvatar] = useState('')
  const [tmpImg, setTmpImg] = useState('')
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null)
  const [isCrop, setIsCrop] = useState(false)
  const [zoom, setZoom] = useState(1)
  const dialogAlert = useDialogAlert()
  // console.log('avatar', avatar);
  const onCropComplete = (croppedArea: any, croppedAreaPixels: any) => {
    setCroppedAreaPixels(croppedAreaPixels)
  };
  const onCropHandler = async () => {
    try {
      const file = await getCroppedImg(
        tmpImg,
        croppedAreaPixels
      )
      const url = file ? URL.createObjectURL(file) : ''
      setCropImg(file)
      setAvatar(url)
      setHasImg(true)
    } catch (e) {
      console.error(e)
    }
    setIsCrop(false)
  }
  const onCancelCropHandle = () => {
    setIsCrop(false)
  }
  const handleFileChange = (e: any) => {
    const file = e.target.files[0];
    if (!file) return;
    const img = new window.Image();
    img.src = URL.createObjectURL(file);
    img.onload = () => {
      // 检查图片分辨率
      if (img.width < 400 || img.height < 600) {
        dialogAlert.show({
          title: t('app.cardEdit.low_img'),
          desc: t('app.cardEdit.low_img_desc'),
          alertStatus: 0
        })
        return;
      }
      setTmpImg(URL.createObjectURL(e.target.files[0]))
      setIsCrop(true)
      e.target.value = ''
    };
  }
  return <>
    <div className={cn("col-span-full")}>
      <label htmlFor="photo" className="block text-sm font-medium leading-6 ">
        {t('app.dialog.avatar')} {hasImg === false && (
          <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{t('app.dialog.avatar_err')}</span>
        )}
      </label>
      <label htmlFor="add_card_avatar_button" className="mt-2 cursor-pointer inline-flex items-center gap-x-3">
        {(avatar || msg?.role_avatar) ? <Image className='rounded-full h-12 w-12 object-cover' src={avatar || msg?.role_avatar} width={48} height={48} alt={'avatar'} /> : <UserCircleIcon className="h-12 w-12 text-gray-500" aria-hidden="true" />}
        <div className="rounded-md dark:bg-gray-800 px-2.5 py-1.5 text-sm  shadow-sm   dark:hover:bg-gray-700 border border-gray-300 hover:bg-gray-200"
        >Change</div>
        <input hidden type="file" onChange={handleFileChange} id="add_card_avatar_button" name="avatar" accept="image/*"></input>
      </label>
    </div>
    {isCrop && <div className='fixed left-0 right-0 top-0 bottom-0 backdrop-blur-sm z-50'><div className="absolute left-0 top-1/4 crop-container w-full h-1/2 z-1000">
      <Cropper
        image={tmpImg}
        crop={crop}
        zoom={zoom}
        aspect={2 / 3}
        onCropChange={setCrop}
        onZoomChange={setZoom}
        onCropComplete={onCropComplete}
        classes={{ containerClassName: 'bg-transparent' }}
      />
      <div className='absolute w-full -bottom-24 h-24 bg-black/55 text-center pt-4'>
        <div className="controls mb-2">
          <input
            type="range"
            value={zoom}
            min={1}
            max={3}
            step={0.1}
            aria-labelledby="Zoom"
            onChange={(e: any) => {
              setZoom(e.target.value)
            }}
            className="zoom-range"
          />
        </div>
        <button type="button" className='bg-gray-800 p-2 px-5 text-white rounded text-sm w-24 mx-auto mr-4' onClick={onCancelCropHandle}>{t('app.common.cancel')}</button>
        <button type="button" className='p-2 px-5 bg-red-500 text-white rounded text-sm w-24 mx-auto' onClick={onCropHandler}>{t('app.common.comfirm')}</button>
      </div>
    </div></div>}
  </>
}

export default React.memo(Avatar)