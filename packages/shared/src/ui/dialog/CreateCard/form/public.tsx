import { useTranslation } from 'react-i18next'
import useDialogAlert from '../../../../hook/useDialogAlert';
const Public = ({errors, register, s, msg, classify}: any) => {
  const { t } = useTranslation()
  const dialogAlert = useDialogAlert();
    return <>
        {!msg?.admin && <label htmlFor="isPublic" className="flex text-gray-300 text-sm font-medium leading-6" onClick={() => {dialogAlert.show({title: '暂不开放', desc: '内测期间暂不开放卡片公开功能哦', alertStatus: 0})}}>
                  <input id="isPublic" disabled className='mr-0.5 align-middle' type="checkbox"  />{t('app.dialog.is_public')}
                  </label>
                }
    </>
}

export default Public