import { useTranslation } from 'react-i18next'
import AutoTextareaHeight from '../AutoTextareaHeight'
import React, { useEffect, useRef, useState } from 'react'
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import cn from 'classnames'
import {validateCharName, validateUserName} from '../validity'
import Toast from '@share/src/ui/toast'

const Description = ({errors, register, s, tokens, formValues, setValue, levelType}: any) => {
    const { t } = useTranslation()
    // 新手模式，并且新建的时候才出现，编辑过的默认不用模版
    const [isNewModel, setIsNewModel] = useState(!formValues['description'])
    const [open, setOpen] = useState(true)
    const genderTitle = t('app.cardEdit.gender');
    const ageTitle = t('app.cardEdit.age');
    const personalityTitle = t('app.cardEdit.personality');
    const appearanceTitle = t('app.cardEdit.appearance');
    const sexualTitle = t('app.cardEdit.sexual');

    const [genderVal, setGenderVal] = useState('');
    const [ageVal, setAgeVal] = useState('');
    const [personalityVal, setPersonalityVal] = useState('');
    const [appearanceVal, setAppearanceVal] = useState('');
    const [sexualVal, setSexualVal] = useState('');
    const { onChange, onBlur, name, ref } = register("description", {
        required: t('app.dialog.require'),
        validate: {
            ...validateCharName({formValues, t}),
            ...validateUserName({formValues, t})
        }
    });
    // 判断本表单是否有错误有错误不收起
    useEffect(() => {
        if(errors.description) {
            setOpen(true);
        }
    }, [errors.description])

    // console.log('formValues', formValues);
    const toggleNewModel = () => {
        setIsNewModel(n => {
            n = !n;
            return n;
        })
    }
    useEffect(() => {
        if(genderVal !== '' || ageVal !== '' || personalityVal !== '' || appearanceVal !== '' || sexualVal !== '') {
            const val = `${genderTitle}: ${genderVal}
${ageTitle}: ${ageVal}
${personalityTitle}: ${personalityVal}
${appearanceTitle}: ${appearanceVal}
${sexualTitle}: ${sexualVal}`;
setValue('description', val);
        }
        
    }, [genderVal, ageVal, personalityVal, appearanceVal, sexualVal])
    return <>
        <div className="col-span-full">
            <label htmlFor="description" onClick={() => {
                if(errors.description && !open === false) {
                    Toast.notify({
                        type: 'warning',
                        message: t('app.cardEdit.collaspe_err')
                    })
                } else {
                    setOpen(!open);
                }
            }} className="block text-sm font-medium leading-6 flex justify-between bg-gray-200 dark:bg-gray-800 items-center pl-2">
            <div>{t('app.dialog.desc_title')}（{tokens.description.count} tokens）{errors.description? (
                <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.description.message}</span>
            ) : <span className='text-xs mt-1'>*</span>}</div>
            <button type='button' className='flex items-center px-3 py-1'><ChevronDownIcon className={cn("-mr-1 h-5 w-5 text-violet-500 dark:text-violet-200 dark:hover:text-violet-100 transition", open && "rotate-180")} /></button>
            </label>
            {<div className={cn(open? '' : 'hidden')}>
            {isNewModel && levelType === 'normal'? <div className={cn('mt-2 dark:bg-gray-800 dark:ring-gray-500', s.textareaStyle)}>
                <div className='relative'>
                    <div className='flex items-start'>
                        <label className='mr-2 mt-2 py-0.5 px-2 rounded bg-blue-500 flex items-center text-white' htmlFor="">{genderTitle}:</label>
                        <AutoTextareaHeight isRequired={true} onChange={(e: any) => {setGenderVal(e.target.value)}} value={genderVal} className="flex-1 w-full"  placeholder={t('app.cardEdit.gender_desc')} rows={1} minHeight={'min-h-8'} noStyle={true} />
                    </div>
                    <div className='flex items-start'>
                        <label className='mr-2 mt-2 py-0.5 px-2 rounded bg-blue-500 flex items-center text-white' htmlFor="">{ageTitle}:</label>
                        <AutoTextareaHeight isRequired={true} onChange={(e: any) => {setAgeVal(e.target.value)}} value={ageVal} className="flex-1 w-full"  placeholder={t('app.cardEdit.age_desc')} rows={1} minHeight={'min-h-8'} noStyle={true} />
                    </div>
                    <div className='flex items-start'>
                        <label className='mr-2 mt-2 py-0.5 px-2 rounded bg-blue-500 flex items-center text-white' htmlFor="">{t('app.cardEdit.personality')}:</label>
                        <AutoTextareaHeight isRequired={true} onChange={(e: any) => {setPersonalityVal(e.target.value)}} value={personalityVal} className="flex-1 w-full"  placeholder={personalityTitle} rows={1} minHeight={'min-h-8'} noStyle={true} />
                    </div>
                    <div className='flex items-start'>
                        <label className='mr-2 mt-2 py-0.5 px-2 rounded bg-blue-500 flex items-center text-white' htmlFor="">{appearanceTitle}:</label>
                        <AutoTextareaHeight onChange={(e: any) => {setAppearanceVal(e.target.value)}} value={appearanceVal} className="flex-1 w-full"  placeholder={t('app.cardEdit.appearance_desc')} rows={2} minHeight={'min-h-10'} noStyle={true} />
                    </div>
                    <div className='flex items-start'>
                        <label className='mr-2 mt-2 mb-2 py-0.5 px-2 rounded bg-blue-500 flex items-center text-white' htmlFor="">{sexualTitle}:</label>
                        <AutoTextareaHeight onChange={(e: any) => {setSexualVal(e.target.value)}} value={sexualVal} className="flex-1 w-full"  placeholder={t('app.cardEdit.sexual_desc')} rows={1} minHeight={'min-h-8'} noStyle={true} />
                    </div>
                </div>
                </div> : 
                <AutoTextareaHeight className={'dark:bg-gray-800 dark:ring-gray-500'} value={formValues.description} placeholder={t('app.dialog.desc_placeholder')} id="description" onChange={onChange} onBlur={onBlur} name={name} ref={ref} data-insert />
            }
            {levelType === 'normal' && <div>
                <label className='mr-2 text-sm flex items-center mt-1'><input className='mr-0.5' type="checkbox" checked={isNewModel} onChange={toggleNewModel} />{t('app.cardEdit.enable_beginer_tpl')}</label>
            </div>}
            </div>}
        </div>
    </>
}

export default Description