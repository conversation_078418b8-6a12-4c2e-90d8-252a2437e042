import { useTranslation } from 'react-i18next'
import AutoTextareaHeight from '../AutoTextareaHeight'

const Personality = ({errors, register, s, msg, tokens, formValues}: any) => {
    const { t } = useTranslation()
    return <>
        <div className="col-span-full">
                  <label htmlFor="personality" className="block text-sm font-medium leading-6 ">
                    {t('app.dialog.personality_title')}（{tokens.personality.count}/{tokens.personality.max} tokens）{errors.personality && (
                    <span className='text-xs mt-1 text-red-500'>{errors.personality.message}</span>
                  )}
                  </label>
                  <div className="mt-2">
                    <textarea
                      id="personality"
                      {...register("personality", {validate: {
                        value: () => {
                          return (tokens.personality.count) <= tokens.personality.max || t('app.dialog.desc_err_token');
                        },
                      }})}
                      rows={3}
                      className={`${s.textareaStyle}`}
                      placeholder={t('app.dialog.personality_placeholder')}
                      defaultValue={msg?.personality}
                    />
                  </div>
                </div>
    </>
}

export default Personality