import { useTranslation } from 'react-i18next'

const RealRole = ({errors, register, s, msg, formValues}: any) => {
    const { t } = useTranslation()
    // console.log('formValues', formValues['real_role']);
    return <>
        {<div className="col-span-full flex items-center">
                <label htmlFor="realRole" className="text-sm font-medium leading-6 ">
                独立角色
                </label>
                <div className="ml-4">
                    <div className='flex gap-2 gap-y-1 text-gray-400 flex-wrap'>
                    <label className='text-white flex items-center'><input id='realRole' className='mr-1' {...register("real_role", { required: '独立角色必须做选择' })} type="radio" defaultChecked={formValues['real_role'] == true} value={true} />是</label>
                    <label className='text-white flex items-center'><input id='realRole' className='mr-1' {...register("real_role", { required: '独立角色必须做选择' })} type="radio" defaultChecked={formValues['real_role'] == false} value={false} />否</label>
                    </div>
                </div>
                {errors.real_role && (
                    <div className="col-span-full text-red-500 ml-2">
                        {errors.real_role.message}
                    </div>
                )}
              </div>}
    </>
}

export default RealRole