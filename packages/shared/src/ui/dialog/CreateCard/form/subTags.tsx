import { useTranslation } from 'react-i18next'

const SubTags = ({ errors, register, s, msg, roleConfig }: any) => {
  const { t } = useTranslation()
  return <>
    {<div className="col-span-full">
      <label htmlFor="subTags" className="block text-sm font-medium leading-6 ">
        {t('app.dialog.label')} {errors.sub_tags && (
          <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.sub_tags.message}</span>
        )}
      </label>
      <div className="mt-2">
        <div className=''>
          {roleConfig?.sub_tag_category_list?.map((tag: any) => {
            return <div key={tag.category} className='mt-2 text-sm'>
              <div>{tag.category}</div>
              <div className='flex gap-2 gap-y-1 mt-0.5 flex-wrap'>
              {
                tag?.sub_tags?.map((sub_tag: any) => {
                  return <label className='mr-2 text-gray-400' key={sub_tag}><input className='mr-0.5' {...register("sub_tags", {
                    required: {
                      value: true,
                      message: t('app.dialog.label_err')
                    },
                    validate: (value: any) => {
                      const selectedTags = Array.isArray(value) ? value : [value];
                      return selectedTags.length <= 12 || t('app.dialog.max_tags_err');
                    }
                  })} type="checkbox" value={sub_tag} />{sub_tag}</label>
                })
              }
              </div>
            </div>
          })}
        </div>
      </div></div>}
  </>
}

export default SubTags