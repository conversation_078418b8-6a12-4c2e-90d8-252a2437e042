import { useTranslation } from 'react-i18next'

const Tags = ({errors, register, s, msg, roleConfig}: any) => {
  const { t } = useTranslation()
    return <>
        {<div className="w-28">
                  <label htmlFor="tags" className="block text-sm font-medium leading-6 ">
                  {t('app.dialog.cat')} 
                  </label>
                  <div className="mt-2 h-[39px]">
                    {roleConfig?.tag_orders?.length > 0 && <select id="tags" {...register("tags")} name="tags" autoComplete="tags" className={`${s.inputStyle}`}>
                      {roleConfig?.tag_orders.map((item: string) => {
                        return <option key={item}>{item}</option>
                      })}
                    </select>}
                  </div>
                </div>}
    </>
}

export default Tags