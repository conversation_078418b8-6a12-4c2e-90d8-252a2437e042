
import { useTranslation } from 'react-i18next'
import AutoTextareaHeight from '../AutoTextareaHeight'
import { validateCharName, validateUserName, validateNoUser } from '../validity';

const maxLen = 1500;
const Introduction = ({errors, register, s, msg, tokens, formValues}: any) => {
    const { t } = useTranslation()
    const { onChange, onBlur, name, ref } = register("introduction", {
        maxLength: {
            value: 1500,
            message: t('app.dialog.limit', {n: maxLen})
        },
        required: t('app.dialog.require'),
        validate: {
            ...validateNoUser({t}),
            ...validateCharName({formValues, t}),
            ...validateUserName({formValues, t, errMsg: t('app.cardEdit.no_user_name1', {userName: formValues['user_role_name']})})
        }
       });
    return <>
        <div className="col-span-full">
            <label htmlFor="introduction" className="block text-sm font-medium leading-6 ">
                {t('app.dialog.intro_name')}
                <span className='text-xs mt-1'>{t('app.dialog.limit_desc', {n: maxLen})}</span>
                {errors.introduction? (
                <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.introduction.message}</span>
            ) : <span className='text-xs mt-1'>*</span>}
            </label>
            <div className="mt-2">
                <AutoTextareaHeight className={'dark:bg-gray-800 dark:ring-gray-500'} value={formValues.introduction} placeholder={t('app.dialog.intro_placeholder')} id="introduction" isRequired={true} onChange={onChange} onBlur={onBlur} name={name} ref={ref}/>
            </div>
            </div>
    </>
}

export default Introduction