import { useTranslation } from 'react-i18next'
import AutoTextareaHeight from '../AutoTextareaHeight'

const FirstMessage = ({errors, register, s, msg, tokens, formValues}: any) => {
  const { t } = useTranslation()
    return <>
        <div className="col-span-full">
                <label htmlFor="first_message" className="block text-sm font-medium leading-6 ">
                  {t('app.dialog.first_title')}（{tokens.first_message.count} tokens）{errors.first_message && (
                  <span className='text-xs mt-1 text-red-500'>{errors.first_message.message}</span>
                )}
                </label>
                <AutoTextareaHeight value={formValues.first_message} placeholder={t('app.dialog.first_desc')} id="first_message" isRequired={true} attrs={{...register("first_message", { required: true, validate: {
                      value: () => {
                        // return tokens.first_message.count <= (tokens.first_message.max) || t('app.dialog.desc_err_token');
                      },
                    }})}} />
              </div>
    </>
}

export default FirstMessage