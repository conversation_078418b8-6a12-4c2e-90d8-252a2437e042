import { useTranslation } from 'react-i18next'
import AutoTextareaHeight from '../AutoTextareaHeight'
import s from './style.module.scss'
import { XMarkIcon } from '@heroicons/react/24/solid'
import cn from 'classnames';
import { Controller, useFieldArray, useWatch } from "react-hook-form"
import useDialogAlert from '../../../../hook/useDialogAlert';
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import { useEffect, useState } from 'react';
import Illustrate from '../../../tooltip/illustrate';
import { validateCharName, validateUserName } from '../validity';
import Toast from '@share/src/ui/toast'

const ExampleDialog = ({errors, register, s, control, tokens, formValues, levelType}: any) => {
    const { t } = useTranslation()
    const dialogAlert = useDialogAlert();
    const [open, setOpen] = useState(true)
    const { fields, append, remove } = useFieldArray({
      control,
      name: "muilte_examples"
    });
    const watchedDialog = useWatch({ 
      control, 
      name: "muilte_examples",
      defaultValue: formValues.muilte_examples 
    });
    // console.log('formValues[chat_type]', formValues['chat_type']);
    const isChatModel = formValues['chat_type'] === 'Chat';
    const userPlaceholder = isChatModel? t('app.cardEdit.example_dialog_user_placeholder_chat') : t('app.cardEdit.example_dialog_user_placeholder')
    const aiPlaceholder = isChatModel? t('app.cardEdit.example_dialog_ai_placeholder_chat') : t('app.cardEdit.example_dialog_ai_placeholder')
    // 判断本表单是否有错误有错误不收起
    useEffect(() => {
      if(errors.example_dialog || errors.muilte_examples) {
          setOpen(true);
      }
    }, [errors.example_dialog, errors.muilte_examples])
    // console.log('ExampleDialog', watchedDialog);
    return <>
        <div className="col-span-full">
                  <div onClick={() => {if((errors.example_dialog || errors.muilte_examples) && !open === false) {
                    Toast.notify({
                        type: 'warning',
                        message: t('app.cardEdit.collaspe_err')
                    })
                } else {
                    setOpen(!open);
                }}} className="block text-sm font-medium leading-6 flex justify-between  bg-gray-200 dark:bg-gray-800 items-center pl-2">
                    <div>
                      {t('app.dialog.example_title')}（{tokens.muilte_examples.count || tokens.example_dialog.count} tokens）{errors.example_dialog && (
                      <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.example_dialog.message}</span>
                    )}
                      <Illustrate title={t('app.cardEdit.replay_len_title')} desc={t('app.cardEdit.replay_len_desc')} />
                    </div>
                    <button type='button' className='flex items-center px-3 py-1'><ChevronDownIcon className={cn("-mr-1 h-5 w-5 text-violet-500 dark:text-violet-200 dark:hover:text-violet-100 transition", open && "rotate-180")} /></button>
                  </div>
                  {<div className={cn(open? '' : 'hidden')}>
                  {
                    formValues?.example_dialog != ''?
                    <AutoTextareaHeight className={'dark:bg-gray-800 dark:ring-gray-500'} value={formValues?.example_dialog} placeholder={t('app.cardEdit.example_placeholder')} id="example_dialog" isRequired={false} attrs={{...register("example_dialog", {
                      validate: {
                        ...validateCharName({formValues, t}),
                        ...validateUserName({formValues, t})
                      }
                    })}} /> :
                    <div>
                    
                    {fields.map((field: any, index: number) => (
                      <div className={cn('mt-2 dark:bg-gray-800 dark:ring-gray-500', s.textareaStyle)} key={field.id}>
                        <div className='relative'>
                          <div className='flex items-start'>
                              <label className='mr-2 mt-2 py-0.5 px-2 rounded bg-blue-500 flex items-center text-white' htmlFor="">{t('app.cardEdit.user')}:</label>
                              <Controller
                                name={`muilte_examples[${index}].user`}
                                control={control}
                                rules={{
                                  validate: {
                                    ...validateCharName({formValues, t}),
                                    ...validateUserName({formValues, t})
                                  }
                                }}
                                render={({ field }) => 
                                <div className='flex-1'>
                                  {errors?.muilte_examples?.[index]?.user && (
                                    <span className="text-xs text-white bg-red-700 px-1 rounded inline-block py-0.5">{errors.muilte_examples?.[index]?.user.message}</span>
                                  )}
                                  <AutoTextareaHeight id={`muilte_examples[${index}].user`} {...field} value={watchedDialog?.[index]?.user || ''} className="flex-1 w-full"  placeholder={userPlaceholder} rows={1} minHeight={'min-h-10'} noStyle={true} data-insert />
                                </div>}
                              />
                            </div>
                          <div className='flex pb-3 items-start'>
                            <label className='mr-2 mt-2 mb-2 py-0.5 px-2 rounded bg-blue-500 flex items-center text-white' htmlFor="">{t('app.cardEdit.char')}:</label>
                            <Controller
                              name={`muilte_examples[${index}].char`}
                              control={control}
                              rules={{
                                validate: {
                                  ...validateCharName({formValues, t}),
                                  ...validateUserName({formValues, t})
                                }
                              }}
                              render={({ field }) => <div className='flex-1'>
                              {errors?.muilte_examples?.[index]?.char && (
                                <span className="text-xs text-white bg-red-700 px-1 rounded inline-block py-0.5">{errors.muilte_examples?.[index]?.char.message}</span>
                              )}
                              <AutoTextareaHeight id={`muilte_examples[${index}].char`} {...field}  value={watchedDialog?.[index]?.char || ''} className="flex-1 w-full"  placeholder={aiPlaceholder} rows={1} minHeight={'min-h-10'} noStyle={true} data-insert /></div>}
                            />
                          </div>
                          {<button className='absolute right-0 top-1/2 -translate-y-1/2 bg-gray-500 overflow-hidden rounded-full p-0.5 text-white' type="button" onClick={() => remove(index)}><XMarkIcon className='w-4 h-4 ' /></button>}
                        </div>
                      </div>
                    ))}
                    
                    <button className='w-full dark:bg-gray-700 bg-gray-300 mt-1 py-1.5 text-sm rounded text-center' type="button" onClick={() => append({ user: '', char: '' })}>
                      {t('app.cardEdit.add_sample')}
                    </button>
                  </div>
                  }
                  </div>}
                </div>
    </>
}

export default ExampleDialog