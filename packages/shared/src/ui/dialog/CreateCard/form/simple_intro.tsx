import { useTranslation } from 'react-i18next'
import AutoTextareaHeight from '../AutoTextareaHeight'
import { validateCharName, validateUserName, validateNoUser } from '../validity';

const maxLen = 200;
const SimpleIntro = ({errors, register, s, msg, formValues}: any) => {
    const { t } = useTranslation()
    const { onChange, onBlur, name, ref } = register("simple_intro", {
        maxLength: {
            value: 200,
            message: t('app.dialog.limit', {n: maxLen})
        },
        required: t('app.dialog.require'),
        validate: {
            ...validateNoUser({t}),
            ...validateCharName({formValues, t}),
            ...validateUserName({formValues, t, errMsg: t('app.cardEdit.no_user_name1', {userName: formValues['user_role_name']})})
        }
        });
    return <>
        {<div className="col-span-full">
            <label htmlFor="simple_intro" className="block text-sm font-medium leading-6 ">
            {t('app.dialog.simplt_intro')}<span className='text-xs mt-1'>{t('app.dialog.limit_desc', {n: maxLen})}</span>{errors.simple_intro? (
            <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.simple_intro.message}</span>
            ) : <span className='text-xs mt-1'>*</span>}
            </label>
            <div className="mt-2">
            <AutoTextareaHeight className={'dark:bg-gray-800 dark:ring-gray-500'} value={formValues.simple_intro} placeholder={t('app.dialog.simplt_intro_placeholder')} id="simple_intro" isRequired={true} onChange={onChange} rows={2} minHeight={'min-h-16'} onBlur={onBlur} name={name} ref={ref}/>
            </div>
        </div>}
    </>
}

export default SimpleIntro