
import AutoTextareaHeight from '../AutoTextareaHeight'
import { useTranslation } from 'react-i18next'
import Illustrate from '../../../tooltip/illustrate';

const AdaptModel = ({errors, register, msg, roleConfig, levelType}: any) => {
    const { t } = useTranslation()
    const desc = roleConfig?.chat_products?.reduce((pre: any, next: any, index: number) => {
      return pre + next.model_name + ': ' + next.desc + '\n'
    }, '')
    // console.log('desc', desc);
    return <>
        {levelType != 'normal' && <div className="col-span-full">
                <div className='flex items-center'>
                  <label className="block text-sm font-medium leading-6 ">
                  {t('app.cardEdit.adapModel')} {errors.support_product_ids && (
                        <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.support_product_ids.message}</span>
                      )}
                  </label>
                  <Illustrate className='ml-1' title={t('app.cardEdit.adapt_model_title')} desc={desc} isMd={true} >!</Illustrate>
                </div>
                <div className="mt-2">
                    <div className='flex gap-2 gap-y-1 text-gray-400 flex-wrap'>
                    {roleConfig?.chat_products?.map((item: any) => {
                      return <label className='mr-2' key={item.mid}><input className='mr-0.5' {...register("support_product_ids", { required: {
                        value: true, message: t('app.cardEdit.adapt_model_at_least_one')
                      } })} type="checkbox" value={item.mid} />{item.model_name}</label>
                    })}
                    </div>
                    <p className='text-xs mt-2'>{t('app.cardEdit.adapt_model_desc1')}</p>
                </div>
              </div>}
    </>
}
export default AdaptModel