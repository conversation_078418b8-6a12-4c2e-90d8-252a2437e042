import type { FC } from "react";
import React from "react";
import { useTranslation } from 'react-i18next'
import Illustrate from '../../../tooltip/illustrate';

const Author = ({errors, register, s, msg, roleConfig}: any) => {
    const { t } = useTranslation('app', { keyPrefix: 'app.cardEdit' })
    return <>
        <div className="col-span-full flex items-center">
            <label htmlFor="subTags" className="text-sm font-medium leading-6 ">
            {t('author')}
            </label>
            <Illustrate className='ml-1' title={''} desc={t('author_desc')} >!</Illustrate>
            <div className="inline-block ml-3">
                <div className='flex gap-2 gap-y-1 text-gray-400 flex-wrap'>
                    <label className='mr-2'><input className='mr-0.5' {...register("author", {  })} type="checkbox" value={true} />{t('showAuthor')}</label>
                </div>
            </div>
            </div>
    </>
}

export default Author