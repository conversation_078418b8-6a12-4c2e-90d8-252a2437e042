import { useTranslation } from 'react-i18next'
import AutoTextareaHeight from '../AutoTextareaHeight'
import Link from 'next/link';
import cn from 'classnames'
import useDialogAlert from '../../../../hook/useDialogAlert';
import Illustrate from '../../../tooltip/illustrate';
import { validateCharName, validateUserName } from '../validity';

const StatusBlock = ({errors, register, levelType, msg, tokens, formValues}: any) => {
    const dialogAlert = useDialogAlert();
    const { t } = useTranslation()
    return <>
    {levelType != 'normal' &&
        <div className="col-span-full">
                  <label htmlFor="statusBlockCheckbox" className="inline-flex text-sm items-center font-medium leading-6 ">
                  <input id="statusBlockCheckbox" {...register("statusBlockEnable")} className='mr-0.5 align-middle' type="checkbox"  />{t('app.dialog.status_block')}
                  <Illustrate title={t('app.cardEdit.status_title')} desc={t('app.cardEdit.status_desc')} className="ml-2" />
                  </label>
                  <fieldset className={cn(!formValues.statusBlockEnable && 'hidden')}>
                    <div className='flex gap-2'>
                      <p className="mt-1 text-sm leading-6 inline-block">{t('app.dialog.status_block_select')}</p>
                      <div className="mt-1 gap-x-3 inline-flex items-center">
                        <div className="flex items-center gap-x-1">
                          <input {...register("statusBlockType")} type="radio" value="normal" id="statusBlockTypeNormal" className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600" />
                          <label htmlFor="statusBlockTypeNormal" className="block text-sm font-medium leading-6">{t('app.dialog.normal')}</label>
                        </div>
                        <div className="flex items-center gap-x-1">
                          <input {...register("statusBlockType")} id="statusBlockTypecollapse" type="radio" value="collapse" className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"/>
                          <label htmlFor="statusBlockTypecollapse" className="block text-sm font-medium leading-6">{t('app.dialog.collapse')}</label>
                        </div>
                        {msg?.admin && <div className="flex items-center gap-x-1">
                          <input {...register("statusBlockType")} id="statusBlockTypeHidden" type="radio" value="hidden" className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"/>
                          <label htmlFor="statusBlockTypeHidden" className="block text-sm font-medium leading-6">{t('app.dialog.hide')}</label>
                        </div>}
                      </div>
                    </div>
                    <div className='text-sm mt-3'>
                    {t('app.dialog.status_tmp')}（{tokens.status_block.count} tokens）{errors.status_block && (
                          <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.status_block.message}</span>
                        )}
                    </div>
                    <AutoTextareaHeight data-insert className={'dark:bg-gray-800'} minHeight={'min-h-40'} value={formValues.status_block} placeholder={t('app.dialog.status_sample')} id="status_block" isRequired={formValues.statusBlockEnable}  attrs={{...register("status_block", {validate: {
                          value: () => {
                            // return tokens.status_block.count <= (tokens.status_block.max) || t('app.dialog.desc_err_token');
                            return true
                          },
                          ...validateCharName({formValues, t}),
                          ...validateUserName({formValues, t})
                        }})}} />
                    <div className='text-sm mt-3'>
                    {t('app.dialog.status_init')} （{tokens.status_block_init.count} tokens）{errors.status_block_init && (
                          <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.status_block_init.message}</span>
                        )}
                    </div>
                    <AutoTextareaHeight data-insert className={'dark:bg-gray-800'} minHeight={'min-h-40'} value={formValues.status_block_init} placeholder={t('app.dialog.status_init_desc')} id="status_block_init" isRequired={formValues.statusBlockEnable}  attrs={{...register("status_block_init", {validate: {
                          value: () => {
                            // return tokens.status_block_init.count <= (tokens.status_block_init.max) || t('app.dialog.desc_err_token');
                            return true
                          },
                          ...validateCharName({formValues, t}),
                          ...validateUserName({formValues, t})
                        }})}} />
                    <div className='text-sm mt-3'>
                    {t('app.dialog.status_rule')}（{tokens.status_block_rule.count} tokens）{errors.status_block_rule && (
                          <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.status_block_rule.message}</span>
                        )}
                    </div>
                    <AutoTextareaHeight data-insert className={'dark:bg-gray-800'} minHeight={'min-h-44'} value={formValues.status_block_rule} placeholder={t('app.dialog.status_rule_desc')} id="status_block_rule" attrs={{...register("status_block_rule", {validate: {
                          value: () => {
                            // return tokens.status_block_rule.count <= (tokens.status_block_rule.max) || t('app.dialog.desc_err_token');
                            return true
                          },
                          ...validateCharName({formValues, t}),
                          ...validateUserName({formValues, t})
                        }})}} />
                  </fieldset>
                </div>
}
    </>
}

export default StatusBlock