import { useTranslation } from 'react-i18next'
import AutoTextareaHeight from '../AutoTextareaHeight'
import s from './style.module.scss'
import { XMarkIcon } from '@heroicons/react/24/solid'
import cn from 'classnames';
import { useForm, Controller, useFieldArray, useWatch } from "react-hook-form";
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import { useEffect, useState } from 'react';
import Illustrate from '../../../tooltip/illustrate';
import { validateCharName, validateUserName } from '../validity';
import Toast from '@share/src/ui/toast';

const MuilteScenes = ({errors, register, s, control, tokens, formValues, setError, clearErrors, isPublic}: any) => {
    const { t } = useTranslation('app', { keyPrefix: 'app.cardEdit' })
    const [open, setOpen] = useState(true)
    const { fields, append, remove } = useFieldArray({
      control,
      name: "muilte_scenes"
    });
    const watchedScenes = useWatch({ 
      control, 
      name: "muilte_scenes",
      defaultValue: formValues.muilte_scenes 
    });
    const isChatModel = formValues['chat_type'] === 'Chat';
    const firstMsgPlaceHolder = isChatModel? t('muilte_scenes_first_message_desc') : t('muilte_scenes_first_message_role_desc')
    // console.log('MuilteScenes', fields);
    // 头部验证函数
    const validateFields = () => {
      const hasEmptyField = watchedScenes?.length === 0 || watchedScenes?.some((field: any) => !field.scenario || !field.first_message);
      if (hasEmptyField) {
        setError("muilte_scenes", { message: t('require', {keyPrefix: 'app.dialog'}) });
      } else {
        clearErrors("muilte_scenes");
    }
    };
    useEffect(() => {
      isPublic && validateFields();
    }, [watchedScenes]);
    // 判断本表单是否有错误有错误不收起
    useEffect(() => {
      if(errors.muilte_scenes) {
          setOpen(true);
      }
    }, [errors.muilte_scenes])
    // console.log('formValues.muilte_scenes', watchedScenes);
    return <>
        <div className="col-span-full">
                  <div onClick={() => {if(errors.muilte_scenes && !open === false) {
                    Toast.notify({
                        type: 'warning',
                        message: t('collaspe_err')
                    })
                } else {
                    setOpen(!open);
                }}} className="block text-sm font-medium leading-6 flex justify-between  bg-gray-200 dark:bg-gray-800 items-center pl-2">
                    <div>
                      <span>{t('muilte_scenes_title')}
                    （{tokens.muilte_scenes.count || tokens.first_message.count} tokens）</span>
                    {isChatModel && <>
                      <span>{t('chat_model', { keyPrefix: 'app.dialog' })}</span>
                      <Illustrate className='ml-1' title={t('muilte_scenes_illustrate_title')} desc={t('muilte_scenes_illustrate')} >!</Illustrate>
                    </>}
                    {errors.muilte_scenes?.message && (
                      <span className='text-xs mt-1 ml-2 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.muilte_scenes.message}</span>
                    )}
                    </div>
                  <button type='button' className='flex items-center px-3 py-1'><ChevronDownIcon className={cn("-mr-1 h-5 w-5 text-violet-500 dark:text-violet-200 dark:hover:text-violet-100 transition", open && "rotate-180")} /></button>
                  </div>
                  {<div className={cn(open? '' : 'hidden')}>
                    {fields.map((field: any, index: number) => (
                      <div className={cn('mt-2 dark:bg-gray-800 dark:ring-gray-500', s.textareaStyle)} key={field.id}>
                        <div className='relative'>
                          <div className='flex items-start mb-2'>
                              <label className='mr-2 mt-2 py-0.5 px-2 rounded bg-blue-500 flex items-center text-white' htmlFor="">{t('muilte_scenes_init')}:</label>
                              <Controller
                                name={`muilte_scenes[${index}].scenario`}
                                control={control}
                                rules={{
                                  validate: {
                                    ...validateCharName({formValues, t}),
                                    ...validateUserName({formValues, t})
                                  }
                                }}
                                render={({ field }) => {
                                  return <div className='flex-1'>
                                  {errors?.muilte_scenes?.[index]?.scenario && (
                                    <span className="text-xs text-white bg-red-700 px-1 rounded inline-block py-0.5">{errors.muilte_scenes?.[index]?.scenario.message}</span>
                                  )}

                                  <AutoTextareaHeight 
                                    isRequired={isPublic? true : false} 
                                    {...field}
                                    value={watchedScenes?.[index]?.scenario || ''}
                                    className="flex-1 w-full" 
                                    placeholder={t('muilte_scenes_init_desc')} 
                                    rows={2} 
                                    minHeight={'min-h-12'} 
                                    noStyle={true} 
                                    data-insert 
                                  />
                                  </div>}
                                }
                              />
                            </div>
                          <div className='flex pb-3 items-start'>
                            <label className='mr-2 mt-2 mb-2 py-0.5 px-2 rounded bg-blue-500 flex items-center text-white' htmlFor="">{t('muilte_scenes_first_message')}:</label>
                            <Controller
                              name={`muilte_scenes[${index}].first_message`}
                              control={control}
                              rules={{
                                validate: {
                                  ...validateCharName({formValues, t}),
                                  ...validateUserName({formValues, t})
                                }
                              }}
                              render={({ field }) => <div className='flex-1'>
                              {errors?.muilte_scenes?.[index]?.first_message && (
                                <span className="text-xs text-white bg-red-700 px-1 rounded inline-block py-0.5">{errors.muilte_scenes?.[index]?.first_message.message}</span>
                              )}
                              <AutoTextareaHeight 
                                isRequired={isPublic? true : false}  
                                {...field}
                                value={watchedScenes?.[index]?.first_message || ''}
                                className="flex-1 w-full"  
                                placeholder={firstMsgPlaceHolder} 
                                rows={3} 
                                minHeight={'min-h-24'} 
                                noStyle={true} 
                                data-insert 
                              />
                              </div>}
                            />
                          </div>
                          {index !== 0 && <button className='absolute right-0 top-1/2 -translate-y-1/2 bg-gray-500 overflow-hidden rounded-full p-0.5 text-white' type="button" onClick={() => remove(index)}><XMarkIcon className='w-4 h-4 ' /></button>}
                        </div>
                      </div>
                    ))}
                    
                    <button className='w-full dark:bg-gray-700 bg-gray-300 mt-1 py-1.5 text-sm rounded text-center' type="button" onClick={() => append({ scenario: '', first_message: '', index: fields.length++ })}>
                      {t('add_dialog_context')}
                    </button>
                  </div>}
                </div>
    </>
}

export default MuilteScenes