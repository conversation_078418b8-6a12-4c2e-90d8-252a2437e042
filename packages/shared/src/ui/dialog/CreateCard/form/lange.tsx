
import AutoTextareaHeight from '../AutoTextareaHeight'
import { useTranslation } from 'react-i18next'

const AdaptModel = ({errors, register, msg, roleConfig, levelType}: any) => {
    const { t } = useTranslation()
    // console.log('chatProduct', chatProduct, formValues);
    return <>
        {levelType != 'normal' && <div className="col-span-full">
                <label className="block text-sm font-medium leading-6 ">
                {t('app.cardEdit.adapModel')} {errors.support_product_ids && (
                      <span className='text-xs mt-1 text-red-500'>{errors.support_product_ids.message}</span>
                    )}
                </label>
                <div className="mt-2">
                    <div className='flex gap-2 gap-y-1 text-gray-400 flex-wrap'>
                    {roleConfig?.chat_products?.map((item: any) => {
                      return <label className='mr-2' key={item.mid}><input className='mr-0.5' {...register("support_product_ids", { required: {
                        value: true, message: '至少要选择1个适用模型'
                      } })} type="checkbox" value={item.mid} />{item.model_name}</label>
                    })}
                    </div>
                </div>
              </div>}
    </>
}

export default AdaptModel