import { useTranslation } from 'react-i18next'
import AutoTextareaHeight from '../AutoTextareaHeight'

const Scenario = ({errors, register, s, msg, tokens, formValues}: any) => {
    const { t } = useTranslation()
    return <>
        <div className="col-span-full">
                  <label htmlFor="scenario" className="block text-sm font-medium leading-6 ">
                    {t('app.dialog.scenario_title')}（{tokens.scenario.count} tokens）{errors.scenario && (
                    <span className='text-xs mt-1 text-red-500'>{errors.scenario.message}</span>
                  )}
                  </label>
                  <AutoTextareaHeight className={'dark:bg-gray-800 dark:ring-gray-500'} placeholder='' id="scenario" isRequired={false}  attrs={{...register("scenario")}} />
                </div>
    </>
}

export default Scenario