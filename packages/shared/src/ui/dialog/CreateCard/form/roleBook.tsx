import { useTranslation } from 'react-i18next'
import { Switch } from '@headlessui/react'
import cn from 'classnames';
import { useState, useEffect, useCallback, useRef } from 'react';
import { Controller, useFieldArray, useWatch } from "react-hook-form"
import AutoTextareaHeight from '../AutoTextareaHeight'
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import useComfirm from '../../../../hook/useComfirm';
import useIsLargeScreen from '../../../../hook/useIsLargeScreen';
import Illustrate from '../../../tooltip/illustrate';
import Toast from '@share/src/ui/toast';
import useRequest from '../../../../hook/useRequest';
import _ from 'lodash';
import { validateCharName, validateUserName } from '../validity';
import { getByteLength } from '@share/src/module/stringTool';

const itemTokensLimit = 1000
const RoleBook = ({errors, levelType, register, s, control, msg, tokens, formValues, setError, clearErrors, trigger, constant = false}: any) => {
  const { t } = useTranslation()
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const comfirm = useComfirm();
  const request = useRequest();
  // 记录每个item的tokens
  const [contentTokens, setContentTokens] = useState<{[key: number]: number}>({});
  // 记录上一次的role_book，只有更新的内容才计算tokens
  const [prevRoleBook, setPrevRoleBook] = useState<any[]>([]);
  let abortControllers: any = useRef<any>(null);

  const countContentToken = async (content: string, index: number) => {
    // console.log('countContentToken', index);
    if (abortControllers?.current) {
      abortControllers.current.abort('debounce自动取消');
      abortControllers.current = null;
    }
    abortControllers.current = new AbortController();
    try {
      const res = await request('/tokenizers/count', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ content }),
        signal: abortControllers.signal
      });
      setContentTokens(prev => ({
        ...prev,
        [index]: res.token_count
      }));
      setTimeout(() => {
        // 触发校验特定的content字段校验
        trigger(`role_book.${index}.content`);
      }, 10);
      abortControllers.current = null;
      return res.token_count;
    } catch (error) {
      console.error('Error counting tokens:', error);
    }
  };
  const debouncedCountToken = useCallback(
    _.debounce(countContentToken, 500),
    []
  );

  const watchFileds = useWatch({
    name: "role_book",
    control
  });

  useEffect(() => {
    if (watchFileds && prevRoleBook) {
      watchFileds.forEach((item: any, index: number) => {
        const prevItem = prevRoleBook[index];
        // prevItem为空的时候，说明是新增的或者第一次进入，有内容并且内容发生了变化
        if (prevItem && (item.content && item.content !== prevItem?.content)) {
          if (item.content) {
            debouncedCountToken(item.content, index);
          } else {
            setContentTokens(prev => ({
              ...prev,
              [index]: 0
            }));
          }
        }
      });
    }
    setPrevRoleBook(watchFileds);
  }, [watchFileds]);

  let { fields, append, remove } = useFieldArray({
    control,
    name: "role_book",
    rules: {
      validate: {
        // maxConstantEntries: (value) => {
        //   if (!value) return true;
        //   const constantEntries = value?.filter((field: any) => field.constant) || [];
        //   // 常量最多3条 非常量最多6条
        //   if (constantEntries.length > 3) {
        //     return t('app.cardEdit.max_constant_entries');
        //   }
        //   return true;
        // },
        // maxNonConstantEntries: (value) => {
        //   if (!value) return true;
        //   const nonConstantEntries = value?.filter((field: any) => !field.constant) || [];
        //   // 常量最多3条 非常量最多6条
        //   if (nonConstantEntries.length > 6) {
        //     return t('app.cardEdit.max_non_constant_entries');
        //   }
        //   return true;
        // }
      }
    }
  });

  const addRoleBook = useCallback(() => {
    append({
      comment: '',
      insertion_order: 100,
      enabled: true,
      keys: '',
      probability: 100,
      content: '',
      constant: constant
    })
    const len = fields.length;
    setSelectedIndex(len);
  }, [append, constant, fields.length]);
  
  const toggleHandle = (index: number) => {
    if(selectedIndex !== -1 && selectedIndex === index) {
      // 如果校验出错，不支持收起
      if(errors?.role_book?.[index]) {
        Toast.notify({
          type: 'warning',
          message: t('app.cardEdit.collaspe_err')
        });
        return
      }
      setSelectedIndex(-1)
    } else {
      // console.log('setSelectedIndex', index);
      setSelectedIndex(index)
    }
  }

  useEffect(() => {
    // 校验contentTokens是否存在超出200tokens的情况，如果超出，弹toast提示，并展开
    for(const key in contentTokens) {
      if(contentTokens[key] > itemTokensLimit) {
        setSelectedIndex(parseInt(key))
        break;
      }
    }
  }, [contentTokens])
  // 判断本表单是否有错误有错误不收起
  useEffect(() => {
    if(errors.role_book) {
      // console.log('errors.role_book', errors.role_book);
      // 找出errors.role_book里面有错误的index
      for(const key in errors.role_book) {
        if(errors.role_book[key]) {
          setSelectedIndex(parseInt(key))
          break;
        }
      }
    }
  }, [errors.role_book])
  return <>
      {/* 世界书，上传卡片的时候展示 */}
      {levelType != 'normal' && <div className="col-span-full" onClick={() => {}}>
              <label htmlFor="" className="block text-sm font-medium leading-6 ">
                {constant? t('app.cardEdit.constant_role_book') : t('app.cardEdit.key_role_book')}
                <Illustrate className='ml-1' title={constant? t('app.cardEdit.role_book_constant_title') : t('app.cardEdit.role_book_key_title')} desc={constant? t('app.cardEdit.role_book_constant_desc') : t('app.cardEdit.role_book_key_desc')} />
                {errors?.role_book?.root?.message && (
                  <>{errors?.role_book?.root?.type !== "maxConstantEntries" && errors?.role_book?.root?.type !== "maxNonConstantEntries" && <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5 ml-1'>{errors.role_book.root.message}</span>}</>
                )}
                {/* {errors?.role_book?.root?.message && (
                  <>{errors?.role_book?.root?.type === "maxConstantEntries" && constant && <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5 ml-1'>{errors.role_book.root.message}</span>}</>
                )}
                {errors?.role_book?.root?.message && (
                  <>{errors?.role_book?.root?.type === "maxNonConstantEntries" && !constant && <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5 ml-1'>{errors.role_book.root.message}</span>}</>
                )} */}
              </label>
              {<div>
                  {fields.map((field, index) => {
                    const elem = watchFileds?.[index];
                    if (!elem || constant != !!elem.constant) {
                      return null;
                    }
                    return <div className={cn('mt-2 bg-gray-200 dark:bg-gray-800 dark:ring-gray-500 py-0.5 px-1 rounded-md')} key={field.id}>
                      <div className='relative space-y-2'>
                        <div className='flex justify-between items-end'>
                          <div className='flex items-start gap-x-2 sm:gap-x-3 items-end flex-1' onClick={() => {toggleHandle(index)}}>
                            <Controller
                              name={`role_book[${index}].comment`}
                              control={control}
                              rules={{
                                // required: t('app.dialog.require')
                                validate: {
                                  len: (value: any) => {
                                      const byteLength = getByteLength(value || '');
                                      // console.log('byteLength', byteLength, value);
                                      return byteLength <= 30 || t('app.dialog.name_len');
                                  }
                                }
                              }}
                              render={({ field }) => {
                                let errMsg: any = '';
                                if(errors?.role_book?.length > 0 && errors.role_book[index]?.comment?.message) {
                                  errMsg = <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5 mb-1'>{errors.role_book[index].comment.message}</span>
                                }
                                return <div className='flex-1 !max-w-36 sm:!max-w-[50%]' onClick={(e) => {e.stopPropagation()}}>
                                {errMsg}
                                <div className='flex items-center'>
                                  <span className='inline-block ml-1 mr-1.5 text-xs'>{index}.</span><input
                                    {...field}
                                    type="text"
                                    value={field.value || ''}
                                // 如果内容为空，默认使用keys内容占位，方便折叠状态了解世界书内容
                                    placeholder={elem.keys || t('app.cardEdit.comment_placeholder')}
                                    className={`${s.inputStyle} ipt !p-1 !text-xs !w-full`}
                            /></div></div>}}/>
                            <Controller
                              name={`role_book[${index}].insertion_order`}
                              control={control}
                              render={({ field }) => <div className='flex items-center' onClick={(e) => {e.stopPropagation()}}>
                                <span className='text-xs'>{t('app.cardEdit.order')}</span><input
                                {...field}
                                type="number"
                                value={field.value}
                                pattern="[0-9]*"
                                placeholder={''}
                                className={`${s.inputStyle} ipt !p-1 !text-xs !w-11 [&::-webkit-inner-spin-button]:appearance-none `}
                            /></div>}/>
                            <Controller
                              name={`role_book[${index}].enabled`}
                              control={control}
                              render={({ field }) => <div className='flex items-center h-6' onClick={(e) => {e.stopPropagation()}}>
                                <span className='text-xs'>{t('app.cardEdit.enable')}</span><Switch
                              checked={field.value}
                              onChange={field.onChange}
                              className="group inline-flex h-3 w-5 items-center rounded-full bg-gray-500 transition data-[checked]:bg-blue-600"
                            >
                              <span className="size-2 translate-x-0.5 rounded-full bg-white transition group-data-[checked]:translate-x-2.5" />
                            </Switch></div>}/>
                            <button className='p-0.5 text-xs text-blue-500 h-6' type="button" onClick={
                              async (e) => {
                                e.stopPropagation();
                                const isDel = await comfirm.show({});
                                console.log('isDel', isDel);
                                if(isDel?.confirm) {
                                  remove(index)
                                }
                              }}>{t('app.common.del')}</button>
                          </div>
                          <button type='button' onClick={() => {toggleHandle(index)}} className='flex items-center px-3 py-1 h-6'><ChevronDownIcon className={cn("-mr-1 h-5 w-5 text-violet-500 dark:text-violet-200 dark:hover:text-violet-100 transition", selectedIndex === index && "rotate-180")} /></button>
                        </div>
                        {<div className={cn(selectedIndex !== index && 'hidden')}>
                          <div className='flex items-start gap-x-6'>
                            {!constant &&<Controller
                                name={`role_book[${index}].keys`}
                                control={control}
                                rules={{
                                  // required: t('app.dialog.require')
                                  validate: {
                                    len: (value: any) => {
                                      // console.log('value', value);
                                      if (typeof value !== 'string') {
                                        return true;
                                      }
                                      const byteLength = getByteLength(value || '');
                                      return byteLength <= 40 || t('app.cardEdit.len_20');
                                    }
                                  }
                                }}
                                render={({ field }) => {
                                  let errMsg: any = '';
                                  if(errors?.role_book?.length > 0 && errors.role_book[index]?.keys?.message) {
                                    errMsg = <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5 mb-1'>{errors.role_book[index].keys.message}</span>
                                  }
                                return <div>
                                  <span className='text-xs'>{t('app.cardEdit.primy_key')}</span>
                                  {errMsg}
                                  <input
                                  {...field}
                                  type="text"
                                  value={field.value || ''}
                                  placeholder={t('app.cardEdit.primy_key_placeholder')}
                                  className={`${s.inputStyle} ipt !p-1 !text-xs !w-full sm:!w-72 !inline-block`}
                              /></div>}} /> }
                              <Controller
                                name={`role_book[${index}].probability`}
                                control={control}
                                render={({ field }) => <div>
                                  <span className='text-xs'>{t('app.cardEdit.trigger')}</span>
                                  <input
                                  {...field}
                                  type="number"
                                  value={field.value || ''}
                                  min={0}
                                  max={100}
                                  placeholder={''}
                                  className={`${s.inputStyle} ipt !p-1 !text-xs !w-full sm:!w-72 !inline-block`}
                              /></div>}/>
                          </div>
                          <div>
                            <Controller
                              name={`role_book[${index}].content`}
                              control={control}
                              rules={{
                                validate: {
                                  tokenLimit: async (value) => {
                                    // console.log('tokenLimit', value);
                                    if (!value) return true;
                                    if(constant) return true;
                                    if(contentTokens[index] > itemTokensLimit) {
                                      Toast.notify({
                                        type: 'warning',
                                        message: t('app.cardEdit.token_limit_exceeded', { limit: itemTokensLimit })
                                      })
                                      return t('app.cardEdit.token_limit_exceeded', { limit: itemTokensLimit })
                                    } else {
                                      return true;
                                    }
                                  },
                                  ...validateCharName({formValues, t}),
                                  ...validateUserName({formValues, t})
                                }
                              }}
                              render={({ field }) => <div className='mt-2'>
                                <span className='text-sm'>{t('app.cardEdit.content')}</span>
                                {contentTokens[index] > 0 && (
                                  <span className="text-sm ">
                                    ({contentTokens[index]} tokens)
                                  </span>
                                )}
                                {errors?.role_book?.[index]?.content && (
                                  <span className="ml-1 text-xs text-white bg-red-700 px-1 rounded inline-block py-0.5">{errors.role_book[index].content.message}</span>
                                )}
                                <AutoTextareaHeight 
                                  data-insert 
                                  {...field}
                                  rows={3}
                                  value={watchFileds?.[index]?.content || ''}
                                  className={`flex-1 dark:bg-gray-800 $${s.inputStyle} ipt`} placeholder={constant? t('app.cardEdit.role_content_placeholder') : t('app.cardEdit.role_noconstant_content_placeholder')} /></div>}
                            />
                          </div>
                          <input className='hidden' type="radio" defaultChecked={constant} value={constant} />
                        </div>}
                      </div>
                    </div>
                  })}
                  
                  <button className='w-full dark:bg-gray-700 bg-gray-300 mt-1 py-1.5 text-sm rounded text-center' type="button" onClick={addRoleBook}>
                    {t('app.cardEdit.add')}
                  </button>
                </div>
                }
            </div>}
    </>
}

export default RoleBook