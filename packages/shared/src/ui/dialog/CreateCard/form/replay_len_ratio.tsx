import { useTranslation } from 'react-i18next'

const ReplayLenRatio = ({register, formValues, levelType, roleConfig, msg}: any) => {
    const { t } = useTranslation()
    const maxTokens = formValues?.role_book?.length > 0? roleConfig.replay_max_token.premium : roleConfig.replay_max_token.normal;
    return <>
        {levelType != 'normal' && <div className="col-span-full">
            <label className="text-sm font-medium leading-6 ">
            {t('app.cardEdit.replay_len')}: {formValues.replay_len_ratio * maxTokens / 100 } tokens
            </label>
            <div className="w-[70%] mt-2">
                <input className='w-full' type="range" id="cowbell" name="cowbell" {...register("replay_len_ratio")} min="0" max={100} step="1" />
            </div>
        </div>}
    </>
}

export default ReplayLenRatio