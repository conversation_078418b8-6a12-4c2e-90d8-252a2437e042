
import { useTranslation } from 'react-i18next'
import Illustrate from '../../../tooltip/illustrate';

const PlayType = ({ register, msg, levelType }: any) => {
  const { t } = useTranslation()
  return <>
    <div className="col-span-full">
      <label htmlFor="play_type" className="text-sm font-medium leading-6 mr-1 align-middle">
        {t('app.mine.play_type')}
        <Illustrate title={t('app.cardEdit.play_type_title')} desc={t('app.cardEdit.play_type_desc')} className='ml-1 mr-1' />
      </label>
      <label className='mr-2 align-middle' htmlFor="play_type"><input className='mr-0.5' id='play_type' {...register("play_type", { required: true })} type="radio" value="RIVALRY" />{t('app.cardEdit.opponent_content')}</label>
      <label className='mr-2 align-middle' htmlFor="play_type_content"><input className='mr-0.5' id='play_type_content' {...register("play_type", { required: true })} type="radio" value="PLOT_INFERENCE" />{t('app.cardEdit.push_content')}</label>
      <label className='mr-2 align-middle' htmlFor="play_type_sys"><input className='mr-0.5' id='play_type_sys' {...register("play_type", { required: true })} type="radio" value="SYSTEM" />{t('app.cardEdit.sys_tool')}</label>
    </div>
  </>
}

export default PlayType