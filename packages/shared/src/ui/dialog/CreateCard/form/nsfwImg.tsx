import type { FC } from "react";
import React from "react";
import { useTranslation } from 'react-i18next'

const NFSWImg = ({errors, register, s, msg, roleConfig}: any) => {
    const { t } = useTranslation('app', { keyPrefix: 'app.cardEdit' })
    return <>
        <div className="col-span-full flex items-center">
            <label htmlFor="nsfw" className="text-sm font-medium leading-6 ">
                是否是NSFW图片
            </label>
            <div className="ml-4">
                <div className='flex gap-2 gap-y-1 text-gray-400 flex-wrap'>
                    <label className='mr-2'><input className='mr-0.5' {...register("image_nsfw", {  })} type="checkbox" />NFSW图片</label>
                </div>
            </div>
        </div>
    </>
}

export default NFSWImg