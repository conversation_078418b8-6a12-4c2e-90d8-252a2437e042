const setDefault = (res: any, isReadMsg: boolean, msg: any, cacheFormData: any, reset: any,levelType: string) => {
    const chatProductIds = res?.chat_products?.map((item: any) => item.mid)
    // 优先使用config的内容
    const role = {...msg, ...res?.role}
    // 过渡处理，把角色卡的性格字段合并到角色定义
    let desc = isReadMsg? role?.description || '' : cacheFormData.description;
    if(role?.personality) {
      desc += '\n性格: ' + role.personality;
    }
    let roleBook = role?.role_book?.entries;
    // 格式化role_book，把role_book的extensions字段probability提取到外面
    if(roleBook) {
      roleBook = roleBook.map((item: any) => {
        const probability = item?.extensions?.probability;
        item.probability = probability === undefined? 100 : probability;
        return item;
      })
    }
    // console.log('role?.real_role', role?.real_role, cacheFormData.real_role);
    reset({
      card_name: isReadMsg? role?.card_name : cacheFormData.card_name,
      simple_intro: isReadMsg? role?.simple_intro : cacheFormData.simple_intro,
      name: isReadMsg? role?.role_name || role?.name : cacheFormData.name,
      user_role_name: isReadMsg? role?.user_role_name : cacheFormData.user_role_name,
      introduction: isReadMsg? role?.introduction : cacheFormData.introduction,
      description: desc,
      // first_message: isReadMsg? role?.first_message : cacheFormData.first_message,
      personality: isReadMsg? role?.personality : cacheFormData.personality,
      example_dialog: isReadMsg? role?.example_dialog : cacheFormData.example_dialog,
      muilte_examples: isReadMsg? (role?.muilte_examples || [{ user: '', char: '' }]) : (cacheFormData.muilte_examples || [{ user: '', char: '' }]),
      muilte_scenes: isReadMsg? (role?.muilte_scenes || [{ scenario: '', first_message: '', index: 0 }]) : (cacheFormData.muilte_scenes || [{ scenario: '', first_message: '', index: 0 }]),
      scenario: isReadMsg? role?.scenario : cacheFormData.scenario,
      tags: isReadMsg? (!!msg?.tags? role?.tags : undefined) : cacheFormData.tags,
      nsfw: isReadMsg? role?.nsfw : cacheFormData.nsfw === undefined? true : cacheFormData.nsfw,
      sub_tags: isReadMsg? role?.sub_tags : cacheFormData.sub_tags,
      speaker_id: isReadMsg? role?.speaker_id : cacheFormData.speaker_id,
      status_block: isReadMsg? role?.status_block : cacheFormData.status_block,
      status_block_init: isReadMsg? role?.status_block_init : cacheFormData.status_block_init,
      status_block_rule: isReadMsg? role?.status_block_rule : cacheFormData.status_block_rule,
      statusBlockEnable: isReadMsg? role?.statusBlockEnable : cacheFormData.statusBlockEnable,
      statusBlockType: isReadMsg? role?.statusBlockType || 'normal' : cacheFormData.statusBlockType || 'normal',
      chat_type: isReadMsg? role?.chat_type || 'RolePlay' : cacheFormData.chat_type || levelType === 'normal'? 'Chat' : 'RolePlay',
      play_type: isReadMsg? role?.play_type || 'RIVALRY' : cacheFormData.play_type || 'RIVALRY',
      support_product_ids: isReadMsg? (role?.support_product_ids?.length > 0? role?.support_product_ids : chatProductIds) : (cacheFormData.support_product_ids || chatProductIds),
      replay_len_ratio: isReadMsg? role?.replay_len_ratio : cacheFormData.replay_len_ratio,
      support_all_language: isReadMsg? role?.support_all_language : cacheFormData.support_all_language,
      support_languages: isReadMsg? role?.support_languages : cacheFormData.support_languages,
      role_book: isReadMsg? roleBook : cacheFormData.role_book || [],
      author: isReadMsg? role?.author : cacheFormData.author === undefined? true : false,
      image_nsfw: isReadMsg? role?.image_nsfw : cacheFormData.image_nsfw,
      real_role: isReadMsg? role?.real_role : cacheFormData.real_role,
    });
  }

  export default setDefault;