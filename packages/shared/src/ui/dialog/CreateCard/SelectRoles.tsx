"use client";
import React, { useEffect, useRef, useState } from 'react';
import cn from 'classnames';
import { useTranslation } from 'react-i18next'
import { MagnifyingGlassIcon, CheckIcon, XMarkIcon } from '@heroicons/react/24/solid'
import useSWRImmutable from 'swr';
import useRequest from '@share/src/hook/useRequest'
import Image from 'next/image';
import debounce from 'lodash.debounce';
import { LoadingToast } from '@share/src/ui/Loading'
import Toast from '@share/src/ui/toast';

const runsOnServerSide = typeof window === 'undefined'
const limit = 22;
const SelectRoles = ({ userRoles, selectRoles, setSelectRoles }: any) => {
  const { t } = useTranslation()
  const [enabledNSFW, setEnabledNSFW] = useState(!runsOnServerSide && !(localStorage.getItem('sfw') == '1' ? true : false))
  const seachIptRef = useRef<any>(null)
  const request = useRequest();
  const [roles, setRoles] = useState<any>([])
  const [offset, setOffset] = useState(0)
  const [tabIndex, setTabIndex] = useState(0)
  const [keyword, setKeyword] = useState('')
  const [] = useState<any[]>([])
  const tabs = [
    {
      title: t('app.history.rec_card'),
      url: `/roles/filter_list?nsfw=${enabledNSFW}&tag=&sub_tag=&offset=${offset}&limit=${limit}`
    },
    {
      title: t('app.history.my_card'),
      url: null
    },
    {
      title: '',
      url: `/roles/search?nsfw=${enabledNSFW}&limit=${limit}&keyword=${keyword}&offset=${offset}&real_role=true`
    }
  ]
  const { data, isLoading } = useSWRImmutable(tabs[tabIndex].url, request, {
    revalidateOnFocus: false
  });
  const { data: userCards } = useSWRImmutable(tabIndex === 1 && userRoles.length === 0? `/user/chat/roles` : null, request, {
    revalidateOnFocus: false
  });
  const scrollDom = useRef<any>(null)

  const searhHandler = async () => {
    const query = seachIptRef.current.value;
    // console.log('seachIptRef.current.value', query);
    if (!valid(query)) return
    setTabIndex(2)
    setKeyword(query)
    resetRoles()
  }
  const valid = (query: string) => {
    if ((!query || query.trim() === '')) {
      Toast.notify({
        type: 'warning',
        message: t('app.search.noMsg')
      })
      return false
    }
    if (query === keyword) {
      return false
    }
    return true
  }
  const handleKeyDown = (e: any) => {
    if (e.code === 'Enter' || e.key === 'Enter') {
      e.preventDefault()
      searhHandler()
    }
  }

  useEffect(() => {
    if (data?.roles.length > 0) {
      setRoles([...roles, ...data?.roles])
    }
  }, [data]);

  const resetRoles = () => {
    setRoles([])
    setOffset(0)
  }
  let hasMore = true;

  if (data?.count) {
    // console.log('offset < data.count - limit', offset, data.count, limit);
    hasMore = offset < data.count - limit;
  }

  // 初始化时监听滚动事件
  useEffect(() => {
    const dom = scrollDom?.current;
    const loadMore = () => {
      if (!hasMore || isLoading) return;
      setOffset(offset + limit)
    };
    const handleScroll = debounce(() => {
      if (
        dom.clientHeight + dom.scrollTop + 800 >
        dom.scrollHeight
      ) {
        loadMore();
      }
    }, 50); // 50ms 的防抖延迟
    if (dom) {
      dom.addEventListener('scroll', handleScroll);
    }
    return () => { dom.removeEventListener('scroll', handleScroll); }
  }, [scrollDom, hasMore, isLoading, offset]);
  useEffect(() => {
    if (tabIndex === 0) {} else if (tabIndex === 1) {
      if(userRoles) {
        setRoles(userRoles)
      }
    }
  }, [tabIndex])
  useEffect(() => {
    userCards && setRoles(userCards?.create_roles)
  }, [userCards])

  return <><div className='mr-1 text-sm inline-block mb-2'>
    {t('app.history.select_role')}
    <div className='text-xs dark:text-gray-200 text-gray-600 '>{t('app.history.select_role_desc')}</div>
    </div>
    <div className="">
      <div className={cn('', selectRoles.length > 0 && 'min-h-24')}>
        {selectRoles.length > 0 && selectRoles.map((role: any, index: number) => (
            <div key={role.id} className='w-16 relative rounded inline-block mr-5'>
              <div className='pb-[110%] relative'>
                <Image className='absolute left-0 top-0 rounded w-full h-full dark:bg-gray-800 bg-gray-200 object-cover' src={role.role_avatar || '/dot.png'} width={64} height={96} alt={role.card_name} onClick={(e) => { }} />
                <button onClick={() => {
                  setSelectRoles((n: any) => {
                    return n.filter((item: any) => {
                      return item !== role
                    })
                  })
                }} className='absolute -right-3.5 -top-3.5 p-2'><XMarkIcon className='w-3 h-3 text-white border border-1 border-purple-500 rounded-full bg-purple-500' /></button>
              </div>
              <h2 className='w-full text-center text-xs mt-1 dark:text-gray-300 text-gray-500 text-ellipsis overflow-hidden h-4 whitespace-nowrap'>{role.card_name}</h2>
            </div>
          ))}
      </div>
      <div className="flex justify-between mb-3">
        <div className='whitespace-nowrap space-x-1.5 w-full overflow-auto flex-1 text-sm flex'>
          {tabs.map((item: any, index: number) => {
            // 搜索框是特殊样式，不展示
            if (item.title === '') {
              return null
            }
            return <button
              key={index}
              type='button'
              className={`dark:border-0 px-1.5 text-sm py-1 rounded-lg ${index === tabIndex ? 'bg-purple-700 text-white' : 'dark:bg-gray-700 dark:text-white bg-white text-purple-500 border-solid border border-purple-500 dark:hover:bg-gray-600 hover:bg-gray-100'}`}
              onClick={() => {
                if (index === tabIndex) return;
                // 我的角色卡不需要提前清空
                if (index !== 1) resetRoles()
                setTabIndex(index)
                setKeyword('');
              }}
            >
              {item.title}
            </button>
          })}
          <div className='relative flex-1 mr-2'>
            <input
              ref={seachIptRef}
              type="text"
              onKeyDown={handleKeyDown}
              enterKeyHint={'search'}
              placeholder={t('app.history.search_placeholder')}
              className='pl-3.5 rounded-full border-0 py-2 sm:py-2.5 shadow-sm ring-1 ring-inset dark:ring-gray-500 ring-purple-500 focus:ring-inset focus:outline-0 text-sm sm:leading-6 dark:bg-gray-800 px-3 outline-none w-full' />
            <button className='absolute rounded-full dark:bg-gray-800 bg-white text-gray-500 right-1 top-[2px] p-1.5 sm:p-2.5' type='button' onClick={searhHandler}>
              <MagnifyingGlassIcon className='w-5 h-5' />
            </button>
          </div>
        </div>
      </div>
      <div className={cn(selectRoles.length > 0? 'h-[calc(88vh_-_182px)]' : 'h-[calc(88vh_-_86px)]', 'overflow-auto')} ref={scrollDom}>
        <div className='grid grid-cols-4 gap-3 sm:gap-5 grid-cols-2'>
          {roles.length > 0 && roles.map((role: any, index: number) => (
            <div key={role.id} className='relative rounded inline-block' onClick={() => {
              if(selectRoles.some((elem: any) => elem.id === role.id)) {
                const _roles = selectRoles.filter((item: any) => {
                  return item.id !== role.id
                })
                setSelectRoles(_roles)
              } else {
                if(selectRoles.length >= 4) {
                  Toast.notify({
                    type: 'info',
                    message: t('app.history.tip1')
                  })
                } else {
                  setSelectRoles([...selectRoles, role])
                }
              }
              
            }}>
              <div className='w-full pb-[125%] relative'>
                <Image className='absolute left-0 top-0 rounded w-full h-full dark:bg-gray-800 bg-gray-200 object-cover' src={role.role_avatar || '/dot.png'} width={60} height={90} alt={role.card_name} onClick={(e) => { }} />
                {selectRoles.some((elem: any) => elem.id === role.id) && <CheckIcon className='absolute right-1 bottom-1 w-3 h-3 text-white border border-1 border-purple-500 rounded-full bg-purple-500' />}
              </div>
              <h2 className='w-full text-center text-xs sm:text-base mt-1 dark:text-gray-300 text-gray-500 break-all line-clamp-2 h-8'>{role.card_name}</h2>
            </div>
          ))}
        </div>
        {roles.length === 0 && tabIndex === 1 && <div className='text-center text-xs mt-12'>
          <p>{t('app.history.no_create_card')}</p>
        </div>}
        {!isLoading && roles.length === 0 && tabIndex === 2 && <div className='text-center mt-5'>
          <p>{t('app.history.search_empty')}</p>
        </div>}
        {
          <div className={cn((isLoading || !hasMore) && offset != 0 ? 'opacity-100' : 'opacity-0', 'h-6 w-full mt-2')}>
            {isLoading && <p className={'text-center text-xs mb-1'}>{t('app.index.loading')}...</p>}
            {!hasMore && !isLoading && <p className='text-center text-xs mb-1'>{t('app.index.no_card_load')}</p>}
          </div>
        }
        {isLoading && offset == 0 && <LoadingToast msg={t('app.common.loading')} />}
      </div>
    </div></>
}

export default SelectRoles;