import { useTranslation } from 'react-i18next'
import Image from 'next/image'
import useReview from '@share/src/hook/useReview'

const Roles = ({roles, reflesh}: any) => {
    const { t } = useTranslation()
    const review = useReview();
    return <>
        <div className='sm:w-full sm:max-w-sm'>
            <label htmlFor="Roles" className="block text-sm font-medium leading-6 ">
            群聊AI角色
            </label>
            <div className="mt-2 flex gap-x-2">
                    {
                        roles.map((role: any) => {
                            console.log('role', role);
                            return <div key={role.id} className='flex gap-x-2'>
                                <Image className='rounded w-16 dark:bg-gray-800 bg-gray-200 object-cover' src={role.role_avatar || '/dot.png'} width={128} height={192} alt={role.role_name} unoptimized={true} />
                                <div className='w-48 text-sm'>
                                    <div>id:{role.id}</div>
                                    <div className='w-full truncate'>{role.card_name || role.role_name}</div>
                                    <div>{role.author_name}</div>
                                    <button type='button' onClick={async () => {
                                        const res = await review.show({msg: {...role, type: 'edit', isEdit: true}})
                                        if(res) {
                                            reflesh();
                                        }
                                    }} className='bg-purple-500 p-0.5 px-2 rounded mt-2'>编辑</button>
                                </div>
                            </div>
                        })
                    }
            </div>
        </div>
    </>
}

export default Roles