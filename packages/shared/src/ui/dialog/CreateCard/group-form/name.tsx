import { useTranslation } from 'react-i18next'

const Name = ({errors, register, s}: any) => {
    const { t } = useTranslation()
    return <>
        <div className='sm:w-full sm:max-w-sm'>
            <label htmlFor="Name" className="block text-sm font-medium leading-6 ">
            {t('app.history.name')} {errors.name? (
            <span className='text-xs mt-1 text-red-500'>{errors.name.message}</span>
            ) : <span className='text-xs mt-1'>*</span>}
            </label>
            <div className="mt-2">
            <input
                id="name"
                {...register("name", { required: t('app.dialog.require'), maxLength: {
                value: 18,
                message: t('app.dialog.len_18')
                } })}
                type="text"
                required
                autoComplete="name"
                placeholder={t('app.history.name_placeholder')}
                className={`${s.inputStyle} ipt`}
            />
            </div>
        </div>
    </>
}

export default Name