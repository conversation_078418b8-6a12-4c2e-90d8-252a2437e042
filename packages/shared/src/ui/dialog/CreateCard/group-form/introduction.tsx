import { useTranslation } from 'react-i18next'
import AutoTextareaHeight from '../AutoTextareaHeight'
import { useState } from 'react'
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import cn from 'classnames'

const Introduction = ({ errors, register, setValue, tokens, formValues }: any) => {
    const { t } = useTranslation()
    return <>
        <div className="col-span-full">
            <label htmlFor="introduction" className="block text-sm font-medium leading-6 flex justify-between items-center">
                <div>{t('app.history.story_bg')} {errors.introduction ? (
                    <span className='text-xs mt-1 text-red-500'>{errors.introduction.message}</span>
                ) : <span className='text-xs mt-1'>*</span>}</div>
            </label>
            {<div>
                <AutoTextareaHeight className={'dark:bg-gray-800 dark:ring-gray-500'} value={formValues.introduction} placeholder={t('app.history.story_bg_desc')} id="introduction" isRequired={true} attrs={{
                    ...register("introduction", {
                        required: t('app.dialog.require'), validate: {
                            validate: (value: any) => {
                                // console.log('value', value);
                                return !/\{\{user\}\}/.test(value) || t('app.cardEdit.no_user_or_Char')
                            }
                        }
                    })
                }} />
            </div>}
        </div>
    </>
}

export default Introduction