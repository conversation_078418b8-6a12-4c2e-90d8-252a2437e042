import { useTranslation } from 'react-i18next'
import AutoTextareaHeight from '../AutoTextareaHeight'
import { useRef, useState } from 'react'
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import cn from 'classnames'

const quickBtnCls = 'rounded-full dark:bg-gray-800 bg-white drop-shadow py-0.5 px-2.5 mr-1.5 mb-1';
const Scenario = ({ errors, register, setValue, tokens, formValues, selectRoles }: any) => {
    const { t } = useTranslation()
    const inputRef = useRef<any>(null);
    // console.log('selectRoles', inputRef);
    const val = formValues.scenario;
    const handleInsert = (text: string) => {
        const input = inputRef.current;
        const start = input.selectionStart;
        const end = input.selectionEnd;
        const newQuery = val.slice(0, start) + text + val.slice(end);
        setValue('scenario', newQuery);
        // Set cursor position after the inserted text
        input.focus();
        setTimeout(() => {
            input.selectionStart = input.selectionEnd = start + text.length;
        }, 0);
    }
    return <>
        <div className="col-span-full">
            <label htmlFor="scenario" className="block text-sm font-medium leading-6 flex justify-between items-center">
                <div>{t('app.history.group_scenario')}（{tokens.scenario.count} tokens）{errors.scenario ? (
                    <span className='text-xs mt-1 text-red-500'>{errors.scenario.message}</span>
                ) : <span className='text-xs mt-1'>*</span>}</div>
            </label>
            <div>
                <AutoTextareaHeight {...register("scenario", {
                    required: t('app.dialog.require'), validate: {
                        value: () => {
                            // return tokens.scenario.count <= (tokens.scenario.max) || t('app.dialog.desc_err_token');
                            return true;
                        },
                    }
                })}
                    ref={(el) => {
                        register("scenario").ref(el); // 让 RHF 注册字段
                        inputRef.current = el; // 将 DOM 赋值给 useRef
                    }} className={'dark:bg-gray-800 dark:ring-gray-500'} value={formValues.scenario} placeholder={t('app.history.group_scenario_desc')} id="scenario" isRequired={true} />
                <div className='flex flex-wrap text-xs mt-1'>
                    {
                        selectRoles?.map((role: any) => {
                            if (!role.role_name) return null;
                            return <button key={role.id} onClick={() => {
                                handleInsert(`${role.role_name}`)
                            }} className={cn(quickBtnCls)} type='button'>@{role.role_name}</button>
                        })
                    }
                </div>
            </div>
        </div>
    </>
}

export default Scenario