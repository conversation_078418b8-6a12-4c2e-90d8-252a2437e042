import Name from '@share/src/ui/dialog/CreateCard/group-form/name';
import Introduction from '@share/src/ui/dialog/CreateCard/group-form/introduction';
import Scenario from '@share/src/ui/dialog/CreateCard/group-form/scenario';
import SubTags from '@share/src/ui/dialog/CreateCard/form/subTags';
import SimpleIntro from '@share/src/ui/dialog/CreateCard/form/simple_intro';
import Author from '@share/src/ui/dialog/CreateCard/form/author';
import Roles from '@share/src/ui/dialog/CreateCard/group-form/roles'

const GroupForm = ({errors, register,formValues, tokens, setValue, s, msg, roleConfig, reflesh }: any) => {
  // console.log('formValues', formValues);
  // console.log('roleConfig', roleConfig);
    return <div className='max-h-[80vh] overflow-auto'>
    <div className={'pt-5 pb-3 space-y-6'}>
        <Name errors={errors} register={register} s={s} />
        <Introduction errors={errors} register={register} formValues={formValues} tokens={tokens} setValue={setValue} />
        <Scenario errors={errors} register={register} s={s} formValues={formValues} tokens={tokens} />
        <SimpleIntro errors={errors} register={register} s={s} msg={msg} formValues={formValues} />
        <Author errors={errors} register={register} s={s} msg={msg} roleConfig={roleConfig} />
        <SubTags errors={errors} register={register} s={s} msg={msg} roleConfig={roleConfig} />
        <Roles roles={roleConfig.group_roles} reflesh={reflesh} />
    </div>
  </div>
}

export default GroupForm