const setDefault = (res: any, isReadMsg: boolean, msg: any, cacheFormData: any, reset: any) => {
    // 优先使用config的内容
    const role = {...msg, ...res}
    reset({
      name: isReadMsg? role?.name : cacheFormData.name,
      simple_intro: isReadMsg? role?.simple_intro : cacheFormData.simple_intro,
      sub_tags: isReadMsg? role?.sub_tags : cacheFormData.sub_tags,
      author: isReadMsg? role?.author : cacheFormData.author === undefined? true : false,
      introduction: isReadMsg? role?.introduction : cacheFormData.introduction,
      scenario: isReadMsg? role?.scenario : cacheFormData.scenario,
    });
  }

  export default setDefault;