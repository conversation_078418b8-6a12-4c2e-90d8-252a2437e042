"use client";

import React, { forwardRef } from 'react';
import s from './style.module.scss'
import cn from 'classnames';

// noStyle 不开启样式
// minHeight 调整最小高度
// rows 最小行数
// className 容器样式
const AutoTextareaHeight = forwardRef(function AutoTextareaHeight({
  value,
  attrs,
  isRequired,
  id,
  placeholder,
  className,
  rows,
  minHeight,
  noStyle,
  ...rest
}: any, ref: any) {
  // console.log('value', value);
  return <div className={cn(className, "mt-2 relative")}>
    <div className={cn(minHeight || 'min-h-24',noStyle? '' :  s.textareaStyle, 'leading-normal invisible whitespace-pre-wrap break-all overflow-y-auto', className)}>
      {value?.replace(/\n$/, '\n ')}
    </div>
    <textarea
      id={id}
      ref={ref}
      {...attrs}
      {...rest}
      rows={rows || 3}
      value={value}
      {...(isRequired && {required:true})}
      placeholder={placeholder || ''}
      className={cn(noStyle? "bg-transparent outline-none" : s.textareaStyle, `leading-normal absolute left-0 top-0 right-0 bottom-0 resize-none overflow-auto`, className)} 
    />
</div>
});

export default AutoTextareaHeight;