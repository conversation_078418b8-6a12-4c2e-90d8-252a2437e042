'use client'

import { useCallback, useEffect, useRef, useState } from "react";
import { useContext } from "react";
import useRequest from "../../../hook/useRequest";
import _ from 'lodash';
import { AuthContext } from "../../../authContext";


const useCountToken = (formValues: any, prevTokens: any, sumTokenLimit?: number, userInfo?: any) => {
    const auth = useContext(AuthContext);
    const prevFormVal = useRef<any>(null);
    // console.log('useCountToken userInfo', userInfo);
    const [tokens, setTokens] = useState<any>({
        description: {
            count: 0,
            max: 10000
        },
        first_message: {
            count: 0,
            max: 10000
        },
        personality: {
            count: 0,
            max: 10000
        },
        example_dialog: {
            count: 0,
            max: 10000
        },
        muilte_examples: {
            count: 0,
            max: 10000
        },
        scenario: {
            count: 0,
            max: 10000
        },
        muilte_scenes: {
            count: 0,
            max: 10000
        },
        status_block: {
            count: 0,
            max: 10000
        },
        status_block_init: {
            count: 0,
            max: 10000
        },
        status_block_rule: {
            count: 0,
            max: 10000
        },
        sum_book: {
            count: 0,
            max: 10000
        },
        role_book: {
            count: 0,
            max: 5000
        }
    })
    const request = useRequest();
    const abortControllers = useRef<{ [key: string]: AbortController }>({});

    // 统计总tokens
    const sumTokens = () => {
        setTokens((n: any) => {
            let sum = 0;
            Object.keys(n).forEach((key) => {
                if (key !== 'sum_book') {
                    sum += n[key].count;
                }
            });
            n['sum_book'].count = sum;
            return n
        });
    }
    const countToken = async (key: string, str: string) => {
        if (abortControllers.current[key]) {
            abortControllers.current[key].abort('debounce自动取消');
        }
        const controller = new AbortController();
        abortControllers.current[key] = controller;
        try {
            const res = await request('/tokenizers/count', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ content: str }),
                signal: controller.signal
            });

            setTokens((prevTokens: any) => ({
                ...prevTokens,
                [key]: {
                    ...prevTokens[key],
                    count: res.token_count
                }
            }));
            sumTokens();
        } catch (error: any) {
            if (error.name !== 'AbortError') {
                console.log('Error counting tokens:', error);
            }
        }
    }
    const debouncedCountToken = useCallback(
        _.debounce((formValues: any) => {
            Object.keys(tokens).forEach((key) => {
                if(formValues[key] === undefined || prevFormVal.current[key] === undefined) return;
                if (formValues[key] != prevFormVal.current[key]) {
                    // console.log('formValues[key]', formValues[key], key);
                    let val = formValues[key];
                    // console.log('formValues[key]', formValues[key], prevFormVal.current[key]);
                    // muilte_examples是数组需要特殊处理
                    if(key === 'muilte_examples') {
                        const res = formValues[key]?.reduce((acc: any, curr: any) => {
                            acc += '{{user}}: ' + curr.user + '\n{{char}}: ' + curr.char + '\n\n';
                            return acc;
                        }, '');
                        const prevRes = prevFormVal.current[key]?.reduce((acc: any, curr: any) => {
                            acc += '{{user}}: ' + curr.user + '\n{{char}}: ' + curr.char + '\n\n';
                            return acc;
                        }, '');
                        if(res ==  prevRes) {
                            return;
                        } else {
                            val = res;
                        }
                    }
                    // muilte_scenes是数组需要特殊处理
                    if(key === 'muilte_scenes') {
                        const res = formValues[key]?.reduce((acc: any, curr: any) => {
                            acc += 'scenario: ' + curr.scenario + '\nfirst_message: ' + curr.first_message + '\n\n';
                            return acc;
                        }, '');
                        const prevRes = prevFormVal.current[key]?.reduce((acc: any, curr: any) => {
                            acc += 'scenario: ' + curr.scenario + '\nfirst_message: ' + curr.first_message + '\n\n';
                            return acc;
                        }, '');
                        if(res ==  prevRes) {
                            return;
                        } else {
                            val = res;
                        }
                    }
                    // role_book是数组需要特殊处理
                    if(key === 'role_book') {
                        const res = formValues[key]?.reduce((acc: any, curr: any) => {
                            curr.constant && curr.enabled && (acc += curr.content);
                            return acc;
                        }, '');
                        const prevRes = prevFormVal.current[key]?.reduce((acc: any, curr: any) => {
                            curr.constant && curr.enabled && (acc += curr.content);
                            return acc;
                        }, '');
                        if(res == prevRes) {
                            return;
                        } else {
                            val = res;
                        }
                    }
                    // console.log(key, formValues[key], prevFormVal.current[key]);
                    // 替换char后计算
                    let regex = /{{char}}/g;
                    // 替换为 xxx
                    val = val.replace(regex, formValues['name']);
                    // 替换user后计算
                    regex = /{{user}}/g;
                    // 替换为 xxx
                    val = val.replace(regex, formValues['user_name'] || userInfo?.nickname || '{{user}}') || '';
                    // console.log('debouncedCountToken', val);
                    // console.log('debouncedCountToken', val);
                    countToken(key, val);
                }
            });
            prevFormVal.current = JSON.parse(JSON.stringify(formValues));
        }, 1000),
        []
    );

    useEffect(() => {
        // console.log('formValues', formValues);
        if (!prevFormVal.current) {
            prevFormVal.current = JSON.parse(JSON.stringify(formValues));
        } else {
            debouncedCountToken(formValues);
        }
    }, [formValues])
    useEffect(() => {
        // console.log('prevTokens', prevTokens);
        // 初始化线上tokens
        Object.keys(tokens).forEach((key) => {
            if(prevTokens[key]) {
                tokens[key].count = prevTokens[key]
            }
        })
        // 总token限制
        if(sumTokenLimit) {
            tokens['sum_book'].max = sumTokenLimit
        }
        // console.log('tokens', tokens);
        setTokens({...tokens})
        sumTokens();
    }, [prevTokens, sumTokenLimit])
    return {
        tokens,
    }
}

export default useCountToken; 