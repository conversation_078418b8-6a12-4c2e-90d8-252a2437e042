"use client";
// 后台审核和更新
import React, { useEffect, useRef, useState } from 'react';
import type { FC } from 'react'
import { useForm, SubmitHandler } from "react-hook-form"
import Toast from '@share/src/ui/toast';
import useRequest from '@share/src/hook/useRequest';
import type { IProps, FormData } from '@share/src/ui/dialog/CreateCard/type';
import s from '@share/src/ui/dialog/CreateCard/style.module.scss'
import useCountToken from '@share/src/ui/dialog/CreateCard/useCountToken';
import CardName from '@share/src/ui/dialog/CreateCard/form/cardName';
import UserName from '@share/src/ui/dialog/CreateCard/form/user_name';
import NSFW from '@share/src/ui/dialog/CreateCard/form/nsfw';
import SimpleIntro from '@share/src/ui/dialog/CreateCard/form/simple_intro';
import Tags from '@share/src/ui/dialog/CreateCard/form/tags';
import SubTags from '@share/src/ui/dialog/CreateCard/form/subTags';
import Name from '@share/src/ui/dialog/CreateCard/form/name';
import Introduction from '@share/src/ui/dialog/CreateCard/form/introduction';
import Avatar from '@share/src/ui/dialog/CreateCard/form/avatar';
import Description from '@share/src/ui/dialog/CreateCard/form/description';
import FirstMessage from '@share/src/ui/dialog/CreateCard/form/first_message';
import RoleBook from '@share/src/ui/dialog/CreateCard/form/roleBook';
import Speaker from '@share/src/ui/dialog/CreateCard/form/speaker';
import ExampleDialog from '@share/src/ui/dialog/CreateCard/form/example_dialog';
import ChatType from '@share/src/ui/dialog/CreateCard/form/chat_type'
import StatusBlock from '@share/src/ui/dialog/CreateCard/form/statusBlock'
import cn from 'classnames';
import { useTranslation } from 'react-i18next'
import AdaptModel from '@share/src/ui/dialog/CreateCard/form/adaptModel';
import Modal from '@share/src/ui/dialog/Modal';
import ReplayLenRatio from '@share/src/ui/dialog/CreateCard/form/replay_len_ratio';
import MuilteScenes from '@share/src/ui/dialog/CreateCard/form/muilte_scenes';
import setDefault from '@share/src/ui/dialog/CreateCard/setDefault';
import GroupForm from './reviewGroup'
import Author from '@share/src/ui/dialog/CreateCard/form/author';
import NFSWImg from './form/nsfwImg';
import autoReplace from './autoReplace';
import useComfirm from '@share/src/hook/useComfirm';
import RealRole from './form/realRole';
import PlayType from './form/playType';
const runsOnServerSide = typeof window === 'undefined'
// 暂存用户更新的数据
let storeFormData: any = {};
const Review: FC<IProps> = ({ onConfirm, onCancel, msg, userInfo, isOpen }) => {
  const { t } = useTranslation()
  const [sumTokenLimit, setSumTokenLimit] = useState(10000)
  const [prevTokens, setPrevTokens] = useState<any>({});
  const [roleConfig, setRoleConfig] = useState<any>({})
  const request = useRequest()
  const cacheFormData = JSON.parse((!runsOnServerSide && localStorage.getItem('createCardCache')) || '{}');
  // 来源卡片编辑还是卡片上传
  const isReadMsg = msg?.isEdit || msg?.isFromImg;
  const [cropImg, setCropImg] = useState<Blob | null>(null)
  const [hasImg, setHasImg] = useState<boolean>(true)
  const [loading, setLoading] = useState(false)
  const [showRejectReson, setShowRejectReson] = useState(false)
  const [customLevelType, setCustomLevelType] = useState<any>(null)
  const levelType = customLevelType || roleConfig?.role?.level_type || msg?.level_type || 'normal';
  const rejectReasonRef = useRef<any>(null)
  const isGroupType = msg?.modeType === 'group';
  // 普通模式切换到高级模式，不支持切换回普通模式
  const canSwitchMode = levelType === 'normal'? true : false;
  const confirm = useComfirm();
  // console.log('roleConfig, msg', roleConfig, msg);
  const {
    register,
    handleSubmit,
    watch,
    reset,
    setValue,
    control,
    setError,
    clearErrors,
    formState: { errors },
    trigger
  } = useForm<FormData>()

  const onSubmit: SubmitHandler<FormData> = async (submitData) => {
    const hideLoading = Toast.showLoading(t('操作中'))
    storeFormData = submitData;
    storeFormData.level_type = levelType;
    if(storeFormData.sub_tags && typeof storeFormData.sub_tags == 'string') {
      storeFormData.sub_tags = [storeFormData.sub_tags]
    }
    msg?.id && (storeFormData.id = msg?.id);
    msg?.role_avatar && (storeFormData.role_avatar = msg.role_avatar)
    // console.log('roleConfig', roleConfig);

    const roleBook = msg?.role_book || roleConfig?.role?.role_book || {};
    const roleBookEntries = storeFormData.role_book;
    if(roleBookEntries && roleBookEntries.length > 0) {
      // 转换为服务端需要的数组格式
      roleBookEntries.forEach((item: any) => {
        item.keys = item.keys === ''? [] : Array.isArray(item.keys)? item.keys : item.keys.split(/[,，]/)
        // 默认的数组，提交后会以数组形式
        // item.secondary_keys = item.secondary_keys === ''? [] : Array.isArray(item.secondary_keys)? item.secondary_keys : item.secondary_keys.split(/[,，]/)

        // 把probability保存到extensions字段
        item.extensions = {
          probability: (item.probability === undefined || item.probability === '')? 100 : Math.round(item.probability * 100) / 100
        }
      })
      storeFormData.role_book = {
        name: roleBook.name || storeFormData.name,
        book_id: roleBook.book_id,
        entries: roleBookEntries,
        enabled: true,
        extensions: roleBook.extensions || {}
      }
    } else {
      storeFormData.role_book = null;
    }
    try {
      // 全量编辑不审核，走更新逻辑
      if(msg?.type === 'edit') {
        const formData = new FormData()
        formData.append('role_json', JSON.stringify(storeFormData));
        formData.append('avatar_img', cropImg || msg?.imgBlob || '')
        msg?.id && formData.append('mode_target_id', msg?.id)
        msg?.modeType && formData.append('mode_type', msg?.modeType)
        await request(msg?.type === 'edit'? `/roles/update` : `/roles/audit/approved`, {
          method: 'POST',
          body: formData
        });
      } else {
        await request(`/roles/audit/approved`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            mode_target_id: msg?.id,
            tag: submitData?.tags,
            mode_type: msg?.modeType,
            role_json: JSON.stringify(storeFormData)
          })
        });
      }
      Toast.notify({ type: 'success', message: '操作成功' })
      onConfirm()
    } catch (e) {
      console.error('/roles/audit/approved error:', e);
      Toast.notify({ type: 'error', message: '操作失败' })
    }
    hideLoading();
  }
  const formValues = watch();
  // console.log('formValues', formValues);
  const { tokens } = useCountToken(formValues, prevTokens, sumTokenLimit, userInfo)
  const getConfig = async () => {
    Toast.showLoading(t('app.common.loading'));
    try {
      if(isGroupType) {
        const res = await request(`/roles/group/create_config?group_id=${msg?.id}`)
        setRoleConfig(res)
        setDefault({role: res.chat_group_edit}, isReadMsg, msg, cacheFormData, reset, levelType);
        setLoading(true)
      } else {
        const res = await request(`/roles/create_config?role_id=${msg?.id}`)
        setRoleConfig(res)
        res.role_token_count && setPrevTokens(res.role_token_count);
        msg?.role_token_count && setPrevTokens(msg?.role_token_count);
        res.sum_token_limit && setSumTokenLimit(res.sum_token_limit);
        setDefault(res, isReadMsg, msg, cacheFormData, reset, levelType);
        setLoading(true)
      }
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: t('app.common.load_err')
      })
      onCancel()
    } finally {
      Toast.hideLoading();
    }
  }
  useEffect(() => {
    getConfig();
  }, []);

  const rejectCard = async () => {
    const hideLoading = Toast.showLoading('处理中')
    try {
      const data = await request(`/roles/audit/rejected`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode_target_id: msg?.id || 0,
          mode_type: msg?.modeType,
          reason: rejectReasonRef.current?.value || "Does not meet specifications"
        })
      });
      console.log(data);
      Toast.notify({ type: 'success', message: '操作成功' })
      onConfirm()
    } catch (e) {
      console.error('/roles/audit/rejected error:', e);
      Toast.notify({ type: 'error', message: '操作失败' })
      onCancel()
    }
    hideLoading();
  }
  const switchMode = async () => {
    const newType = levelType == "normal"? 'premium' : 'normal';
    // if(newType === 'premium') {
    //   const res = await confirm.show('温馨提醒', '切换高级模式，提交成功后，将不能切回普通模式，是否继续？')
    //   if(!res) return;
    // }
    setCustomLevelType(newType)
  }

  // Watch for changes in image_nsfw
  const imageNsfw = watch("image_nsfw");
  useEffect(() => {
    if (imageNsfw) {
      setValue("nsfw", true);
    }
  }, [imageNsfw, setValue]);

  // 提交前做一次全局检查并弹窗提示
  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSubmit((data) => {
      onSubmit(data);
    }, async (errors) => {
      autoReplace({errors, setValue, formValues, confirm, t, Toast, trigger});
      Toast.notify({
        type: 'warning',
        message: t('app.cardEdit.form_error')
      });
    })(e);
  };

  return (
    <>
      <Modal onClose={() => { onCancel() }} isOpen={loading && isOpen} conWidth='sm:w-[950px]'>
        <div className='min-h-20 mb-4'>
          <div className="flex min-h-full flex-1 flex-col justify-center pt-4">
            <div className="">
              <form className="" action="#" method="POST" onSubmit={handleFormSubmit}>
                {isGroupType ? <div className='max-h-[85vh] pb-1 overflow-auto space-y-6 mb-4 px-1 sm:px-8 pt-4'>
                  <GroupForm errors={errors} register={register} s={s} formValues={formValues} tokens={tokens}  msg={msg}  roleConfig={roleConfig} reflesh={getConfig} />
                </div> :
                  <div className='max-h-[85vh] pb-1 overflow-auto space-y-6 mb-4 px-1 sm:px-8 pt-4'>
                    {canSwitchMode && <button type='button' className='text-xs underline text-blue-500' onClick={switchMode}>切换到{levelType == "normal"? '高级模式' : '普通模式'}</button>}
                    <Avatar errors={errors} register={register} s={s} msg={msg} hasImg={hasImg} setHasImg={setHasImg} setCropImg={setCropImg} />
                    <NFSWImg errors={errors} register={register} s={s} msg={msg} roleConfig={roleConfig} />
                    <NSFW errors={errors} register={register} s={s} />
                    <PlayType errors={errors} register={register} s={s} msg={msg} roleConfig={roleConfig} />
                    <Introduction errors={errors} register={register} s={s} msg={msg} tokens={tokens}  formValues={formValues} />
                    <CardName errors={errors} register={register} s={s} />
                    <SimpleIntro errors={errors} register={register} s={s} msg={msg} formValues={formValues} />
                    <Author errors={errors} register={register} s={s} msg={msg} roleConfig={roleConfig} />
                    <SubTags errors={errors} register={register} s={s} msg={msg} roleConfig={roleConfig} />
                    {/* <Personality errors={errors} register={register} s={s} msg={msg} tokens={tokens} /> */}
                    <Speaker errors={errors} register={register} s={s} tokens={tokens} watch={watch} msg={msg} roleConfig={roleConfig} />
                    <Name errors={errors} register={register} s={s} msg={msg} formValues={formValues} trigger={trigger} />
                    <RealRole errors={errors} register={register} s={s} formValues={formValues} />
                    <UserName errors={errors} register={register} s={s} formValues={formValues} trigger={trigger} />
                    <ChatType register={register} msg={msg} levelType={levelType} />
                    <Description errors={errors} register={register} formValues={formValues} tokens={tokens} setValue={setValue} />
                    <ReplayLenRatio msg={msg} errors={errors} register={register} formValues={formValues} levelType={levelType} roleConfig={roleConfig} />
                    <ExampleDialog errors={errors} register={register} s={s} formValues={formValues} control={control} tokens={tokens} />
                    <MuilteScenes errors={errors} register={register} s={s} formValues={formValues} control={control} tokens={tokens} />
                    <StatusBlock errors={errors} register={register} s={s} msg={msg} formValues={formValues} tokens={tokens} levelType={levelType} />
                    {/* <Scenario errors={errors} register={register} s={s} msg={msg} tokens={tokens} /> */}
                    {/* <FirstMessage errors={errors} register={register} s={s} formValues={formValues} tokens={tokens} /> */}
                    <div className="space-y-4">
                    <h3>{t('app.cardEdit.role_book')}（{tokens.role_book.count} tokens）</h3>
                    <RoleBook key="constant" errors={errors} levelType={levelType} register={register} s={s} control={control} msg={msg} tokens={tokens} formValues={formValues} trigger={trigger} setError={setError} clearErrors={clearErrors} constant={true} />
                    <RoleBook key="non-constant" errors={errors} levelType={levelType} register={register} s={s} control={control} msg={msg} tokens={tokens} formValues={formValues} trigger={trigger} setError={setError} clearErrors={clearErrors} constant={false} />
                  </div>
                    <AdaptModel levelType={levelType} errors={errors} register={register} msg={msg} roleConfig={roleConfig} formValues={formValues} />
                    <Tags errors={errors} register={register} s={s} msg={msg} roleConfig={roleConfig} />
                    
                  </div>
                }
                {
                  msg?.type === 'edit'? <>
                  <div className='max-h-[12vh]'>
                    <div className='px-8 pt-2 mb-0.5 flex flex-row-reverse'>
                      <button key={1} className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3' type="submit">{msg?.isPublic? t('app.cardEdit.public') : msg?.isEdit? t('app.common.update') : t('app.common.submit')}</button>
                      <button className='p-2 px-5 dark:bg-gray-800 bg-gray-300 rounded text-sm' type='button' onClick={() => {onCancel()}}>{t('app.common.cancel')}</button>
                    </div>
                  </div>
                </> : <>
                {!showRejectReson && <div className='px-8 pt-2 mb-0.5 flex flex-row-reverse'>
                  <button className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3' type="submit">通过并上线</button>
                  <button className='p-2 px-5 bg-gray-800 rounded text-sm' type='button' onClick={() => { setShowRejectReson(true) }}>驳回</button>
                </div>}
                {showRejectReson && <div className='px-8 pt-2 pb-12 mb-0.5 flex flex-row-reverse'>
                  <div className="col-span-full bg-gray-900 p-3 w-96">
                    <label htmlFor="introduction" className="block text-sm font-medium leading-6 ">
                      请输入拒绝理由：
                    </label>
                    <div className="mt-2">
                      <textarea
                        ref={rejectReasonRef}
                        id="introduction"
                        required
                        rows={3}
                        className={`${s.textareaStyle} dark:bg-gray-800 dark:ring-gray-500`}
                      />
                    </div>
                    <div className='px-8 pt-2 mb-0.5 flex flex-row-reverse'>
                      <button className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3' type='button' onClick={() => { rejectCard() }}>确定</button>
                      <button className='p-2 px-5 bg-gray-800 rounded text-sm' onClick={() => { setShowRejectReson(false) }} type="button">取消</button>
                    </div>
                  </div>
                </div>}
                </>
                }
                {!isGroupType && <p className={cn('text-right text-sm pr-8 mb-2', tokens.sum_book.count / tokens.sum_book.max > 1 && 'text-red-500')}>{t('app.dialog.sum_cost')}: {tokens.sum_book.count}/{tokens.sum_book.max}tokens</p>}
              </form>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default Review;