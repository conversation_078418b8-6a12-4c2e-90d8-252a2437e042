"use client";
import React, { useEffect, useRef, useState } from 'react';
import type { FC } from 'react'
import cn from 'classnames';
import { useTranslation } from 'react-i18next'
import Modal from '@share/src/ui/dialog/Modal';
import { useForm, SubmitHandler } from "react-hook-form"
import SelectRoles from './SelectRoles';
import Toast from '@share/src/ui/toast';
import s from './style.module.scss'
import Introduction from '@share/src/ui/dialog/CreateCard/group-form/introduction';
import useCountToken from '../../../../../frontend/app/[lang]/(nav)/create/[tab]/useCountToken';
import Scenario from '@share/src/ui/dialog/CreateCard/group-form/scenario';
import useRequest from '@share/src/hook/useRequest';
import setDefault from './setGroupDefault';

type IProps = {
  onCancel: Function,
  onConfirm: Function,
  isOpen?: boolean,
  msg?: Record<string, any>
  userInfo?: any
}
type FormData = {
  // name: string,
  introduction: string,
  scenario: string,
  simple_intro: string,
  sub_tags: string[],
  author: boolean,
}
const confirmBtn = 'p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'
const cancelBtn = 'p-2 px-5 dark:bg-gray-800 bg-gray-300 rounded text-sm'
const runsOnServerSide = typeof window === 'undefined'
const CreateGroup: FC<IProps> = ({ onConfirm, onCancel, isOpen, msg, userInfo }) => {
  const { t } = useTranslation()
  const {
    register,
    handleSubmit,
    watch,
    reset,
    setValue,
    control,
    formState: { errors },
  } = useForm<FormData>({mode: 'onChange'})
  const [sumTokenLimit, setSumTokenLimit] = useState(800)
  const [prevTokens, setPrevTokens] = useState<any>({});
  const request = useRequest();
  const cacheFormData = JSON.parse((!runsOnServerSide && localStorage.getItem('creatGroupCache')) || '{}');
  const cacheSelectRoles = JSON.parse((!runsOnServerSide && localStorage.getItem('creatGroupRoles')) || '[]');
  const userRoles = msg?.create_roles;
  const [step, setStep] = useState(1)
  const [selectRoles, setSelectRoles] = useState<any[]>([])
  // const [fixRoles, setFixRoles] = useState<any[]>([])
  const [roleConfig, setRoleConfig] = useState<any>({})
  const formValues = watch();
  // 来源卡片编辑还是卡片上传
  const isReadMsg = msg?.isEdit || msg?.isFromImg;
  const {tokens} = useCountToken(formValues, prevTokens, sumTokenLimit, userInfo)
  const [show, setShow] = useState(false)
  const onSubmit: SubmitHandler<FormData> = async (data) => {
    console.log('selectRoles', selectRoles);
    console.log('data', data);
    if(tokens.sum_book.count - tokens.sum_book.max > 1) {
      Toast.notify({
        type: 'warning',
        message: t('app.dialog.sum_token_exceed')
      })
      return;
    }
    const hideLoading = Toast.showLoading(t('app.dialog.creating'))
    try{
      const res = await request(msg?.isEdit? `/roles/group/update` : `/roles/group/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: msg?.id || 0,
          // name: data.name,
          introduction: data.introduction,
          scenario: data.scenario,
          role_ids: selectRoles.map(roles => roles.id),
          simple_intro: data?.simple_intro || '',
          sub_tags: data?.sub_tags || [],
          author: data?.author || false,
        })
      });
      hideLoading();
      // 审核中
      if(res.error_code === 1001) {
        Toast.notify({
          type: 'error',
          message: res.message
        })
      } else {
        // console.log(data);
        Toast.notify({type: 'success', message: msg?.isEdit? t('app.mine.update_success') : t('app.dialog.create_success')})
        localStorage.removeItem('creatGroupCache');
        localStorage.removeItem('creatGroupRoles');
      }
      onConfirm()
    } catch(e: any) {
      console.error('create or update Card error:', e);
      hideLoading();
      if(e.status == 403) {
        Toast.notify({type: 'error', message: t('app.cardEdit.auditing_desc')})
      } else {
        Toast.notify({type: 'error', message: t('app.dialog.create_fail')})
      }
    }
  }
  const nextHandler = () => {
    if(selectRoles.length < 2) {
      Toast.notify({
        type: 'warning',
        message: t('app.history.tip')
      })
    } else {
      setStep(step + 1)
    }
  }
  const prevHandler = () => {
    setStep(step - 1)
  }
  const getConfig = async () => {
    Toast.showLoading(t('app.common.loading'));
    try {
      const res = await request('/roles/group/create_config' + (msg?.id? `?group_id=${msg?.id}` : ''))
      setRoleConfig(res)
      res.role_token_count && setPrevTokens(res.role_token_count);
      res.sum_token_limit && setSumTokenLimit(res.sum_token_limit);
      setDefault(res?.chat_group_edit, isReadMsg, msg, cacheFormData, reset);
      res?.group_roles && setSelectRoles(res?.group_roles)
      // setFixRoles(res?.group_roles || [])
      setShow(true)
    } catch(e) {
      Toast.notify({
        type: 'error',
        message: t('app.common.load_err')
      })
      onCancel()
    } finally {
      Toast.hideLoading();
    }
  }
  useEffect(() => {
    // 新创建的群组才缓存
    !msg?.id && localStorage.setItem('creatGroupCache', JSON.stringify(formValues))
  }, [formValues]);
  useEffect(() => {
    // 新创建的群组才缓存
    !msg?.id && localStorage.setItem('creatGroupRoles', JSON.stringify(selectRoles))
    // 之前编辑过的卡片，不能编辑
  }, [selectRoles]);
  useEffect(() => {
    !msg?.id && setSelectRoles(cacheSelectRoles)
    getConfig();
  }, []);
  return (
    <>
      {show && <Modal onClose={() => { onCancel() }} isOpen={isOpen} conWidth='sm:w-[950px]'>
        <div className='min-h-20'>
          <div className="flex min-h-full flex-1 flex-col justify-center">
            <div className="max-h-[100vh]">
              <form className="" action="#" method="POST" onSubmit={handleSubmit(onSubmit)}>
                <div className='max-h-[80vh] overflow-auto'>
                  <div className={cn(step === 1 ? '' : 'hidden')}>
                    <SelectRoles userRoles={userRoles} selectRoles={selectRoles} setSelectRoles={setSelectRoles} />
                  </div>
                  <div className={cn(step === 2 ? 'pt-5 pb-3 space-y-6' : 'hidden')}>
                  {/* <Name errors={errors} register={register} s={s} /> */}
                  <Scenario errors={errors} register={register} s={s} formValues={formValues} tokens={tokens} selectRoles={selectRoles} setValue={setValue} />
                  </div>
                </div>
                <div className='max-h-[12vh] pb-2'>
                  <div className='px-8 pt-3 mb-0.5 flex flex-row-reverse'>
                    {step === 1 ?
                      <button type='button' onClick={nextHandler} className={cn(confirmBtn)}>{t('app.dialog.next')}</button> :
                      <button key={1} className={cn(confirmBtn)} type="submit">{msg?.isPublic ? t('app.cardEdit.public') : msg?.isEdit ? t('app.common.update') : t('app.common.submit')}</button>
                    }
                    {step === 1 ?
                      <button className={cancelBtn} type='button' onClick={() => { onCancel() }}>{t('app.common.cancel')}</button> :
                      <button className={cancelBtn} type='button' onClick={prevHandler}>{t('app.dialog.prev')}</button>
                    }
                  </div>
                  {step === 2 &&
                      <p className={cn('text-right text-sm pr-8', tokens.sum_book.count / tokens.sum_book.max > 1 && 'text-red-500')}>{t('app.dialog.sum_cost')}: {tokens.sum_book.count}/{tokens.sum_book.max}tokens</p>
                    }
                </div>
              </form>
            </div>
          </div>
        </div>
      </Modal>}
    </>
  );
};

export default CreateGroup;