"use client";

import React from 'react';
import type { FC } from 'react'
import Confirm from './Confirm';
import { useParams, useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next'
type IProps = {
  onClose: Function,
  isOpen: boolean
}

const BalanceConfirm: FC<IProps> = ({ onClose, isOpen }) => {
  const { t } = useTranslation()
  const router = useRouter();
  const params = useParams()
  const lang = params.lang as string
  const onConfirm = () => {
    router.push(`/${lang}/pay`);
    onClose();
  }
  const onCancel = () => {
    onClose();
  }
  return (
    <Confirm onClose={() => {onClose()}} isOpen={isOpen} onConfirm={onConfirm} onCancel={() => {onClose()}}>
      <h1 className='text-base font-semibold leading-6'>余额不足</h1>
      <div className='text-sm mt-2'>抱歉，你的余额不足，请充值使用～</div>
    </Confirm>
  );
};

export default BalanceConfirm;
