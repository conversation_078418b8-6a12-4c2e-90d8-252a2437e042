"use client";

import React, { useContext, useState, useEffect } from 'react';
import type { FC, FormEvent } from 'react'
import Modal from './Modal';
import googleSvg from './img/google.svg'
import telegramSvg from './img/telegram.svg'
import loginGuideImg from './img/login_guide.png';
import loginGuideImgLight from './img/login_guide.png';
import useDialogRegist from '../../hook/useDialogRegist';
import Image from 'next/image';
import Toast from '../toast'
import { useTranslation } from 'react-i18next'
import Link from 'next/link';
import useLightBox from '@share/src/hook/useLightBox';
import { Fullscreen } from 'lucide-react';
import facebookSvg from './img/facebook.svg'
import { getAuth, signInWithPopup, GoogleAuthProvider, TwitterAuthProvider, FacebookAuthProvider } from "firebase/auth";
import { firebaseAuth } from '@share/src/module/firebase'
import {web} from '@/app/module/global'

const googleProvider = new GoogleAuthProvider();
const twitterProvider = new TwitterAuthProvider();
const facebookProvider = new FacebookAuthProvider();

type IProps = {
  onClose: Function,
  isOpen: boolean,
  onOpen: Function,
  auth: any
  theme?: string
}
const { notify } = Toast
declare global {
  interface Window {
    TelegramLoginWidget: any;
  }
}

const loginBtn = 'flex justify-center rounded-lg  py-3 border dark:border-gray-600 dark:bg-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800'
const Login: FC<IProps> = ({ onClose, isOpen, onOpen, auth, theme }) => {
  const { t } = useTranslation()
  const [errMsg, setErrMsg] = useState('');
  const dialogRegist = useDialogRegist();
  // google登录错误
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });

  const guideUrl = theme === 'light' ? loginGuideImgLight : loginGuideImg
  const lightBox = useLightBox();
  const handleChange = (e: any) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    setErrMsg('')
  };
  const onLogin = async (e: FormEvent) => {
    e.preventDefault();
    try {
      await auth?.login(formData.email, formData.password);
      onClose()
    } catch (e) {
      notify({ type: 'error', message: '抱歉，登录出现问题，请稍后再试～' })
      setErrMsg(String(e))
      console.error('Error logging in:', e);
    }
  }
  const onGoogleLogin = async () => {
    try {
      const result = await signInWithPopup(firebaseAuth, googleProvider);
      onClose()
      // 获取 ID 令牌
      const idToken = await result.user.getIdToken();
      await auth?.login('', '', idToken);
    } catch (error: any) {
      setError(error.message);
    }
  }

  const onTwitterLogin = async () => {
    try {
      const result = await signInWithPopup(firebaseAuth, twitterProvider);
      onClose()
      // 获取 ID 令牌
      const idToken = await result.user.getIdToken();
      await auth?.login('', '', idToken);
    } catch (error: any) {
      setError(error.message);
    }
  }

  const onFacebookLogin = async () => {
    try {
      const result = await signInWithPopup(firebaseAuth, facebookProvider);
      onClose()
      // 获取 ID 令牌
      const idToken = await result.user.getIdToken();
      await auth?.login('', '', idToken);
    } catch (error: any) {
      setError(error.message);
    }
  }

  return (
    <Modal onClose={() => { onClose() }} isOpen={isOpen}>
      <div className='min-h-20 mb-2'>
        <div className="flex min-h-full flex-1 flex-col justify-center pt-3 px-6">
          <div className="sm:mx-auto sm:w-full sm:max-w-sm">
            <h2 className=" text-center text-2xl font-bold leading-9 tracking-tight">
              {t('app.login.login_title')}
            </h2>
          </div>

          <div className="sm:mx-auto sm:w-full sm:max-w-sm px-6">
            {(process.env.NEXT_PUBLIC_ENV === 'dev' || web) && <form className="space-y-3" action="#" method="POST" onSubmit={onLogin}>
              <div>
                <label htmlFor="email" className="block text-sm font-medium leading-6 ">
                  {t('app.login.email')}
                </label>
                <div className="mt-2">
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="ipt py-2.5"
                  />
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between">
                  <label htmlFor="password" className="block text-sm font-medium leading-6 ">
                    {t('app.login.password')}
                  </label>
                  <div className="text-sm">
                    <a href="#" className="font-semibold text-indigo-400 hover:text-indigo-500">
                      Forgot password?
                    </a>
                  </div>
                </div>
                <div className="mt-2">
                  <input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="password"
                    value={formData.password}
                    onChange={handleChange}
                    required
                    className="ipt py-2.5"
                  />
                </div>
                {errMsg && <p className='text-xs mt-1 text-red-500'>{errMsg}</p>}
              </div>

              <div className='pt-3'>
                <button
                  type="submit"
                  className="flex w-full justify-center rounded-md px-3 py-2.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-purple-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 bg-purple-600 text-white"
                >{t('app.common.login')}</button>
              </div>
            </form>}

            {process.env.NEXT_PUBLIC_ENV !== 'dev' && !web && <><div className='w-full mt-6 mb-5 relative' onClick={() => {
              lightBox.show({
                src: guideUrl,
              })
            }}>
              <Image src={guideUrl} width={951} height={534} alt="guide login" className="w-full " />
              {/* <Fullscreen className='text-white w-4 h-4 absolute bottom-2 right-2 cursor-pointer' /> */}
            </div>
              <div className='flex justify-center flex-col items-center'>
                <Link href={process.env.NEXT_PUBLIC_TG_LOGIN_LINK || ''} target='_blank' className="flex items-center justify-center w-full px-4 py-2.5 mt-3 text-white bg-[#229ED9] rounded-md hover:bg-[#1e8dc2] transition-colors">
                  <Image src={telegramSvg} alt="telegram" className="w-5 h-5 mr-2" />{t('app.login.tg_login')}
                </Link>
              </div></>}

            {web && <>
              <p className='text-center mt-3 mb-2 text-sm leading-6 text-gray-400'>{t('app.login.other_login')}</p>
              <div className='grid grid-cols-3 gap-3 justify-center items-center'>
                <button onClick={onGoogleLogin} className={`${loginBtn}`}><Image src={googleSvg} className='' alt='google' width={20} height={20} /></button>
                <button onClick={onTwitterLogin} className={`${loginBtn}`}><svg width="20" height="20" viewBox="0 0 26 26" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                  <path d="M13.9919 10.7758L21.5178 2H19.8878L13.3619 9.5758L8.14193 2H2.5L10.3619 13.4242L2.5 22.5H4.13L10.9919 14.6242L16.5 22.5H22.1419L13.9919 10.7758ZM11.7178 13.5879L10.9338 12.4879L5.03386 4.06061H7.26593L12.0338 10.9394L12.8178 12.0394L19.0338 20.9394H16.8017L11.7178 13.5879Z" fill="currentColor" />
                </svg>
                </button>
                <button onClick={onFacebookLogin} className={`${loginBtn} !py-2.5`}><Image src={facebookSvg} className='' alt='facebook' width={24} height={24} /></button>
              </div></>}

              {web && <p className="mt-12 text-center text-sm text-gray-500">
              {t('app.login.no_account')}
              <a href="#" onClick={(e) => {
                onClose(e);
                dialogRegist.show()
              }} className="font-semibold leading-6 text-indigo-400 hover:text-indigo-500 ml-1">
                {t('app.login.login_now')}
              </a>
            </p>}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default Login;
