"use client";

import React from 'react';
import type { FC } from 'react'
import Confirm from './Confirm';
import { useTranslation } from 'react-i18next'
type IProps = {
  onClose: React.MouseEventHandler,
  isOpen: boolean
}

const LoginConfirm: FC<IProps> = ({ onClose, isOpen }) => {
  const { t } = useTranslation()
  const onConfirm = () => {
   
  }
  return (
    <Confirm onClose={onClose} isOpen={isOpen} onConfirm={onConfirm}>
      <h1 className='text-base font-semibold leading-6'>请登录</h1>
      <div className='text-sm mt-2'>抱歉，需要登录才能继续～</div>
    </Confirm>
  );
};

export default LoginConfirm;
