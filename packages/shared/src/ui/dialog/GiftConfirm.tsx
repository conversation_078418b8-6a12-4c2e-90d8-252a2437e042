"use client";

import React from 'react';
import type { FC, ReactNode } from 'react'
import Modal from './Modal';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import Image from 'next/image';
import { useTranslation } from 'react-i18next'
type IProps = {
  onClose: React.MouseEventHandler,
  isOpen: boolean,
  onConfirm: React.MouseEventHandler,
  children: React.ReactNode,
  comfirmBtn?: string,
  showCancelBtn?: boolean,
  icon?: ReactNode
}

const Confirm: FC<IProps> = ({ onClose, isOpen, children, onConfirm, comfirmBtn, showCancelBtn = true, icon }) => {
  const { t } = useTranslation()
  return (
    <Modal onClose={onClose} isOpen={isOpen}>
      <div className='absolute left-0 -top-8 w-16 right-0 mx-auto flex justify-center'>{icon}</div>
      <div className='min-h-20 mb-4'>
        <div className='pt-12 p-1'>
          <div className='text-center space-y-3'>
            {children}
          </div>
        </div>
      </div>
      <div className='px-2 py-2 text-center'>
        <button className='p-2 px-5 bg-purple-600 text-white rounded text-sm ml-3' onClick={onConfirm}>{comfirmBtn || '确定'}</button>
      </div>
    </Modal>
  );
};

export default Confirm;
