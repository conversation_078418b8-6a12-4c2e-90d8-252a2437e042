"use client";

import React, { useRef, useState } from 'react';
import type { ChangeEvent, ChangeEventHandler, FC, FormEvent } from 'react'
import Modal from './Modal';
import Toast from '../toast'
import { useForm, SubmitHandler } from "react-hook-form"
import { useTranslation } from 'react-i18next'
import useDialogAlert from '@share/src/hook/useDialogAlert';
type IProps = {
  onClose: Function,
  isOpen: boolean
  code?: string
}
type FormData = {
  email: string
  password: string
  passwordConfirm: string
  verificaitonCode: string
  inviteCode: string
}
const { notify } = Toast
const Regist: FC<IProps> = ({ onClose, isOpen, code }) => {
  const { t } = useTranslation()
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<FormData>()
  const password = watch("password");
  const email = watch("email");
  const dialogAlert = useDialogAlert();
  // console.log(password) // watch input value by passing the name of it

  const onSubmit: SubmitHandler<FormData> = async (data) => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_HOST}/user/register`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: data.email,
          password: data.password,
          captcha: data.verificaitonCode,
          inviteCode: data.inviteCode
        })
      })
      const res = await response.json();
      if(response.ok) {
        notify({ type: 'success', message: t('app.login.regist_success') })
      } else {
        notify({ type: 'error', message: res.detail })
      }
      console.log(res);
    } catch (error) {
      console.error('Error uploading image:', error);
      notify({ type: 'error', message: t('app.login.regist_err') })
    }
    onClose();
  }
  const sendCode = async () => {
    // console.log('email', email);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_HOST}/user/register/send_captcha`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email
        })
      })
      const res = await response.json();
      if(response.ok) {
        dialogAlert.show({
          title: t('app.login.sended'),
          desc: t('app.login.sended_desc')
        });
      } else {
        throw new Error(res.msg)
      }
      // console.log(res);
    } catch (error) {
      console.error('Error uploading image:', error);
      notify({ type: 'error', message: t('app.login.regist_err') })
    }
  }
  return (
    <Modal onClose={() => {onClose()}} isOpen={isOpen}>
      <div className='min-h-20 mb-16'>
        <div className="flex min-h-full flex-1 flex-col justify-center px-6 pt-12 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-sm">
          <h2 className=" text-center text-2xl font-bold leading-9 tracking-tight">
            {t('app.login.regist_title')}
          </h2>
        </div>

        <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-sm">
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <label htmlFor="email" className="block text-sm font-medium leading-6 ">
              {t('app.login.email')} *
              </label>
              <div className="mt-2">
                <input
                  id="email"
                  {...register("email", { required: true })}
                  type="email"
                  autoComplete="email"
                  required
                  className="px-3 py-2.5 ipt"
                />
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between">
                <label htmlFor="password" className="block text-sm font-medium leading-6 ">
                {t('app.login.password')} *
                </label>
                {/* <div className="text-sm">
                  <a href="#" className="font-semibold text-indigo-400 hover:text-indigo-500">
                    Forgot password?
                  </a>
                </div> */}
              </div>
              <div className="mt-2">
                <input
                  id="password"
                  {...register("password", { 
                    required: true, 
                    minLength: {
                      value: 6,
                      message: t('app.login.password_len'),
                    },})}
                  type="password"
                  autoComplete="password"
                  required
                  className="px-3 py-2.5 ipt"
                />
                {errors.password && (
                  <p className='text-xs mt-1 text-red-500'>{errors.password.message}</p>
                )}
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between">
                <label htmlFor="passwordConfirm" className="block text-sm font-medium leading-6 ">
                  {t('app.login.password_confirm')} *
                </label>
                {/* <div className="text-sm">
                  <a href="#" className="font-semibold text-indigo-400 hover:text-indigo-500">
                    Forgot password?
                  </a>
                </div> */}
              </div>
              <div className="mt-2">
                <input
                  id="passwordConfirm"
                  {...register("passwordConfirm", {
                    validate: {
                      checkSamePassword: v => v === password || t('app.login.password_not_match')
                    }
                  })}
                  type="password"
                  autoComplete="passwordConfirm"
                  required
                  className="px-3 py-2.5 ipt"
                />
                {errors.passwordConfirm && <p className='text-xs mt-1 text-red-500'>{errors.passwordConfirm.message}</p>}
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between">
                <label htmlFor="inviteCode" className="block text-sm font-medium leading-6 ">
                  {t('app.login.invite_code')}
                </label>
              </div>
              <div className="mt-2">
                <input
                  id="inviteCode"
                  {...register("inviteCode")}
                  type="text"
                  defaultValue={code || ''}
                  autoComplete="inviteCode"
                  className="px-3 py-2.5 ipt"
                />
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between">
                <label htmlFor="verificaitonCode" className="block text-sm font-medium leading-6 ">
                  {t('app.login.verifyCode')} *
                </label>
              </div>
              <div className="mt-2">
                <input
                  id="verificaitonCode"
                  {...register("verificaitonCode")}
                  type="text"
                  autoComplete="verificaitonCode"
                  className="px-3 py-2.5 ipt !w-32 !inline-block"
                />
                <button className='ml-2 underline' onClick={sendCode} type='button'>{t('app.login.get_code')}</button>
                {errors.verificaitonCode && <p className='text-xs mt-1 text-red-500'>{errors.verificaitonCode.message}</p>}
              </div>
            </div>

            <div className='pt-6'>
              <button
                type="submit"
                className="flex w-full justify-center rounded-md px-3 py-2.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-purple-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 bg-purple-600 text-white"
              >{t('app.common.regist')}</button>
            </div>
          </form>
        </div>
      </div>
      </div>
    </Modal>
  );
};

export default Regist;
