"use client";

import React, { useEffect } from 'react';
import type { FC, ReactNode } from 'react'
import cn from 'classnames';
import { XMarkIcon } from '@heroicons/react/24/outline'
import { isMobileDevice } from '@share/src/module/global';

type IProps = {
  children: ReactNode,
  onClose?: React.MouseEventHandler,
  isOpen?: boolean,
  conWidth?: string,
  className?: string,
  isFull?: boolean
  isCloseIconShow?: boolean
  // 正文css
  mainClassName?: string
}

const Modal: FC<IProps> = ({ children, onClose, isOpen, conWidth="max-w-lg", className, isFull, isCloseIconShow = true, mainClassName = ''}) => {
    // Add event listener for ESC key (desktop only)
    useEffect(() => {
      if (isMobileDevice) return;
      const handleEscKey = (event: KeyboardEvent) => {
        if (event.key === 'Escape' && isOpen && onClose) {
          onClose(event as unknown as React.MouseEvent);
        }
      };
      if (isOpen) {
        document.addEventListener('keydown', handleEscKey);
      }
      // Cleanup
      return () => {
        document.removeEventListener('keydown', handleEscKey);
      };
    }, [isOpen, onClose]);
    
    return (
      <>
        {isOpen && <div className={cn("fixed animate-fadeIn z-10 w-full h-full left-0 top-0 bg-gray-600/[.5] ", isFull? '' : 'border-8 border-transparent')}>
          <div className={cn(conWidth, `${className} animate-scaleIn box-border dark:bg-gray-900 bg-white absolute mx-auto left-0 right-0 top-0 top-2/4 translate-y-[-50%] rounded`)}>
            <div className={cn('px-3 py-3', mainClassName)}>
              {isCloseIconShow && <button className='h-6 w-6 absolute right-4 z-50 text-gray-800 dark:text-white' onClick={onClose}><XMarkIcon className='h-6 w-6 right-4'></XMarkIcon></button>}
              {children}
            </div>
          </div>
        </div>}
      </>
    );
};

export default Modal;
