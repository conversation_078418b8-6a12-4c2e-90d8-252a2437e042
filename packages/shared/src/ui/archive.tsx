import Modal from "./dialog/Modal";
import { useTranslation } from "react-i18next";
import Toast from "./toast";
import useRequest from "../hook/useRequest";
import useDialogAlert from "../hook/useDialogAlert";
const Archive = ({ archiveList, onClose, cardName, id, isGroup, onComfirm, isTG, modeType = 'single' }: any) => {
    const { t } = useTranslation()
    const request = useRequest();
    const dialogAlert = useDialogAlert();
    const exportChat = async (cid: number) => {
        // console.log('cid', `/chat/history/export?mode_type=${modeType}&mode_target_id=${id}&cid=${cid}`);
        try {
            if (isTG) {
                Toast.showLoading('');
                const res = await request(`/chat/history/export?mode_type=${modeType}&mode_target_id=${id}&cid=${cid}`)
                Toast.hideLoading();
                if (!res?.error_code) {
                    dialogAlert.show({
                        title: t('app.chat.export_title'),
                        desc: t('app.chat.export_desc')
                    })
                } else {
                    dialogAlert.show({
                        alertStatus: 0,
                        title: t('app.common.exec_err'),
                        desc: `${res?.message}`
                    })
                }
            } else {
                window.open(process.env.NEXT_PUBLIC_API_HOST + `/chat/history/export?mode_type=${modeType}&mode_target_id=${id}&cid=${cid}`);
            }
        } catch (e) {
            console.log('res', e);
            Toast.notify({
                type: 'error',
                message: t('app.chat.del_failed')
            })
            Toast.hideLoading();
        }
    }
    return (
        <Modal onClose={onClose} isOpen={true}>
            <div className="py-6 px-3 max-h-[80vh] overflow-y-auto">
                <h3 className="text-lg mb-3 text-lg">{cardName}</h3>
                <ul className="space-y-3">
                    {archiveList.map((item: any, index: number) => {
                        return <li key={index} className=''>
                            <div>
                                <div>{item.title}</div>
                                <div className="flex justify-between items-center">
                                    <div>
                                        <div className="h-5 flex items-center mt-1">
                                            <span className="text-xs text-gray-400 mr-1">{t('app.history.first_chat_at')}:</span>
                                            <span className="text-xs text-gray-400 align-middle">{item.first_chat_at}</span>
                                        </div>
                                        <div className="h-5 flex items-center">
                                            <span className="text-xs text-gray-400 mr-1">{t('app.history.latest_chat_at')}:</span>
                                            <span className="text-xs text-gray-400">{item.latest_chat_at}</span>
                                        </div>
                                    </div>
                                    <div className="space-y-1 w-20">
                                        {item.has_permission ?
                                            <>
                                                <button onClick={() => {
                                                    onComfirm({ isGroup, id, cid: item.conversation_id })
                                                }} type="button" className="priBtn py-1 px-2 text-sm w-20">{t('app.history.load_archive')}</button>
                                                <button className='py-1 px-6 w-20 dark:bg-gray-800 bg-gray-300 rounded-lg text-sm ' onClick={() => { exportChat(item.conversation_id) }}>{t('app.recentChat.export_chat')}</button>
                                            </> : <>
                                                <button type="button" disabled className="priBtn_disabled py-1 px-2 text-sm w-20">{item.desc}</button>
                                                <button className='priBtn_disabled py-1 px-2 text-sm w-20'>{t('app.recentChat.export_chat')}</button>
                                            </>
                                        }


                                    </div>
                                </div>
                            </div>
                        </li>
                    })}
                </ul>
            </div>
        </Modal>
    )
}

export default Archive;