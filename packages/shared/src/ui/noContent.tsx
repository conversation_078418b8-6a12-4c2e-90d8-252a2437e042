import { useTranslation } from "react-i18next"
import { DocumentTextIcon } from '@heroicons/react/24/solid'

const NoContent = () => {
    const { t } = useTranslation()
    return (
        <div className="mt-5 col-start-1 sm:col-span-2 lg:col-span-4 xl:col-span-5 flex justify-center">
            <div>
                <DocumentTextIcon className="h-16 w-16 text-gray-400 opacity-60" aria-hidden="true" />
                <p className="text-xs leading-6 text-gray-400">{t('app.mine.no_content')}</p>
            </div>
        </div>
    )
}

export default NoContent