import privacy from './privacy.zh'
import terms from './terms.zh'
import share from './module/share.zh'
import chat from './module/chat.zh'

const translation = {
  approval_for_gift: '过审得🟡权益',
  gift: '奖励',
  chat,
  share,
  meta: {
    title: '幻梦AI伴侣',
    desc: '幻梦AI伴侣，AI聊天，酒馆SillyTavern平替',
    copyright: '',
    privacy_policy: ''
  },
  common: {
    status_setting_title: '状态栏设置说明',
    status_setting_desc: `1.点击按钮可对所有角色卡的状态栏进行隐藏或展示
2.隐藏，代表之后AI回复不再展示状态栏
3.展示，代表之后AI回复文字下展示状态栏

说明：
1.有的角色卡没有状态栏，不受状态栏展示、隐藏设置影响；
2.也可在小程序-我的-设置，进行状态栏展示隐藏设置`,
    status_setting: '状态栏设置',
    save_success: '保存成功',
    update_rule: '更新规则',
    tpl: '模版',
    generate_status_bar: '一键生成状态栏',
    select_template: '选择模板',
    status_bar: '状态栏',
    ai_generate_tips: '本功能为AI智能生成，AI将会结合您当前的聊天记录情节发展，生成聊天整体的状态栏，生成一次消耗600💎',
    ai_generate: 'AI生成中...',
    support_all_lang: '支持所有语言',
    upload: '上传',
    more: '更多',
    less: '收起',
    go_setting: '去设置',
    public_test: '公测中',
    net_err: '网络异常',
    net_err_desc: `请尝试以下方法后重试：
1. 切换vpn（翻墙）节点
2. vpn（翻墙）改为全局模式
如果上述两个方式未能解决，点此[联系客服](https://t.me/ai_x01_bot)`,
    claim:'去领取',
    confirmBtn1: '去充值',
    cancelBtn1: '遗憾退出',
    tips1: '提醒：💎余额不足',
    act_join_success: '报名成功',
    act_join_failed: '报名失败',
    confirm1: '知道了',
    confirm2: '报名',
    message: '消息',
    title: '幻梦',
    back: '返回',
    model: '模式',
    comfirm: '确定',
    logout: '登出',
    regist: '注册',
    login: '登录',
    del: '删除',
    cancel: '取消',
    update: '更新',
    submit: '提交',
    loading: '加载中...',
    load_err: '网络异常，请重试',
    retry: '重试',
    del_seccess: '删除成功',
    exec_err: '执行异常',
    tips: '提示',
    save: '保存',
    back_home: '回到首页',
    pay: '去充值',
    save_back: '保存返回',
    minutes_ago: '分钟前',
    hours_ago: '小时前',
    days_ago: '天前',
    count_msg: '条对话',
    role_info: '角色信息',
    role_introduction: '角色介绍',
    guide: '写卡教程(轻松学会)',
  },
  login: {
    tg_login: '通过Telegram登录',
    login_sucess: '登录成功',
    login_failed: '登录失败，请重试',
    login_title: '登录你的账号',
    email: '邮箱地址',
    password: '密码',
    other_login: '或用以下账户登录',
    no_account: '没有账号？',
    login_now: '立即注册',
    regist_title: '注册你的账号',
    password_confirm: '密码确认',
    invite_code: '邀请码 (选填)',
    verifyCode: '验证码',
    regist: '注册',
    password_len: '密码至少要6位',
    password_not_match: '两次密码不一致',
    regist_success: '注册成功，请登录～',
    regist_err: '抱歉，注册出现问题，请稍后再试～',
    sended: '已发送',
    sended_desc: '验证码已发送到注册的邮箱地址，请登录你的邮箱查看',
    get_code: '获取验证码'
  },
  setting: {
    theme: '主题',
    enable_nsfw: '开启成人内容过滤',
    enable_nsfw_desc: '启用成人内容即表示您确认您已年满 18 岁。',
    setting: '设置',
    system: '跟随系统',
    dark: '黑夜模式',
    light: '白天模式'
  },
  toast: {
    wait_data: '等待数据返回中，请稍后再试～',
    group_not_support: '直聊bot暂不支持群聊卡片',
  },
  nav: {
    checkin_gift: '签到领',
    index: '卡片',
    chat: '聊天',
    mine: '我的',
    pay: '充值',
    create: '创建',
    claim_gift: '领',
    title: '幻梦AI伴侣',
    char: '角色列表',
    tag: '标签管理',
    presets: 'presets',
    reg: '正则',
    operation: '运营',
    setting: '配置',
    createCard: '创建卡片'
  },
  footer: {
    about: '关于',
    contact: '联系我们',
    terms: '服务条款',
    privacy: '隐私政策',
  },
  index: {
    task: '福利任务',
    nsfw: '成人模式',
    more: '显示更多',
    hide: '隐藏',
    loading: '加载中',
    no_card_load: '已加载到底部～',
    search_place_holder: '搜索角色名称 / 关键词 / 标签',
    nsfw_alert_title: '需要登录',
    nsfw_alert_desc: '需要登录后，在设置里面打开nsfw开关才能激活过滤功能',
    regist_title: '注册你的账户',
    author: '作者',
    filter: {
      tag: '标签',
      chat_mode: '模式',
      independent_role: '独立',
      speech_filter: '抢话',
      sort: '排序',
      default: '全部',
      all_model: '全部模式',
      select_tag: '选择标签',
      clear: '清除',
      mother_son: '母子',
      contrast: '反差',
      ntr: 'NTR',
      licentious: '淫乱',
      virgin: '处女',
      incest: '乱伦',
      fanfiction: '同人',
    }
  },
  search: {
    search_res: '搜索结果',
    no_res: '搜索结果为空，试试',
    create_role: '创建你的角色',
    noMsg: '不能为空'
  },
  mine: {
    status_bar: '聊天状态栏展示开关',
    extra_info_name_same: '已自动将玩家名字替换为{{user}}',
    voice_title: '自动生成语音说明',
    voice_desc: `1. 开启，会自动将“”()[]里的文字内容生成语音（推荐，较快生成）
2. 关闭，会将所有文字生成语音（需花时间等待生成）`,
    voice: '语音设置',
    auto_generate_voice: '自动生成语音',
    switch_identity_tip: '切换身份提示',
    switch_identity_desc: `1. 切换身份后，上下文对话中的所有用户名、头像，都会修改
2. 切换身份后，用户设定的补充内容生效`,
    switch_identity: '切换身份',
    cannot_delete_enabled: '身份选中状态不支持删除',
    switch_success: '切换成功',
    select_user: '选择身份',
    vip_limit: '限时体验',
    user_role: '玩家互动身份',
    select: '选择',
    extra_info_desc: `1. 当与AI聊天时，补充您扮演的玩家互动身份角色设定，会影响对话或剧情推进
2. 角色卡中对玩家互动身份的角色已经设定，可能存在冲突不完全生效的可能
3. 玩家互动身份补充设定的信息生效内容token上限150`,
    extra_info_placeholder: `输入玩家设定补充内容，token上限150
例如：{{user}}身高180cm，智商200
特殊说明：这里的玩家名字禁止和昵称相同，只能使用{{user}}`,
    extra_info_title_placeholder: `限制最多15个字`,
    extra_info_limit: '最多生成3条玩家补充信息，同时生效的内容token上限150（支持多选）',
    extra_user_limit: '最多生成3条用户身份',
    play_type: '玩法类型',
    next_receive_at: '下次领取时间',
    bg_transparent: '背景透明度',
    upload_limit_bg: '最多只能上传2张聊天背景',
    chat_bg: '聊天背景',
    switch_language: '切换语言',
    use_common_bg: '使用个人通用聊天背景',
    no_benefit: '暂无赠送权益',
    quota: '聊天额度',
    validity_period: '有效期',
    benifit_consume: '权益消耗',
    benefits: '权益记录',
    benefits_recharge: '充值权益',
    benefits_give: '赠送权益',
    benefits_free: '免费权益',
    expire_amount: '过期钻石数量',
    expire_time: '过期时间',
    no_record: '没有记录',
    history_tips: '提示：只显示25年1月9日及以后的最近3个月消费记录',
    expire_tips: '提示：只显示25年1月9日及以后的最近3个月的过期记录',
    date_format: 'YYYY年MM月DD日',
    no_more_list: '已加载到底部',
    type: '类型',
    card_name: '角色卡名',
    card_id: '角色卡ID',
    chat_mode: '聊天模式',
    consume_diamond: '消耗',
    diamond_consume_time: '消耗时间',
    diamond_expire: '过期记录',
    consume_record: '消费记录',
    diamond_record: '钻石记录',
    show_chat_tips: '聊天小灯泡提醒开关',
    setting: '设置',
    nsfw_alert_desc: '需要在首页开启nsfw开关，才能关闭图片模糊功能',
    blur_img: '模糊图片',
    upload_failed: '上传失败～',
    exceed_limit: '超出限额',
    nopay_card_limit: `非付费用户最多只能创建6张角色卡，付费用户创建卡片数量提升至15张`,
    nopay_card_limit1: `非付费用户最多只能创建6张角色卡，付费用户创建卡片数量提升至15张

【认证作者】可创建更多卡哦❗️`,
    find_customer_service: '找客服认证',
    pay_card_limit: '付费用户最多只能创建15张角色卡，VIP用户创建卡片数量提升至30张。VIP功能即将推出，敬请期待',
    only_three_group_card: '非VIP用户最多只能创建3张群聊卡，VIP功能即将推出，敬请期待',
    vip_title: 'VIP功能',
    vip_desc: '开通VIP后可用，VIP功能即将推出，敬请期待',
    my_role: '我创建的角色卡',
    create: '创建',
    my_group: '我创建的群聊卡',
    upload_tip: '你还没有创建的角色卡片，请点击创建或者上传',
    upload_group_tip: '还没创建群聊，点击“创建群聊“创建',
    fav_role_tip: '没有收藏的角色，在角色详情页可以收藏',
    no_content: '还没有内容',
    recent_chat: '最近聊天',
    recent_chat_tip: '最近没有聊天的角色',
    update_success: '更新成功～',
    public_success: '已提交，审核中～',
    create_failed: '创建失败，请重试～',
    person_info: '个人信息',
    user_id: 'ID',
    nick_name: '昵称',
    input_your_name: '请输入你的角色名字',
    avatar: '头像',
    update: '更新简介',
    beta: '内测',
    upload_card: '导入角色',
    upload_card_title: '导入角色说明',
    upload_card_desc: `本平台兼容其他平台的角色卡，支持[Character Card V1/V2 Specification](https://github.com/malfoyslastname/character-card-spec-v2/blob/main/spec_v2.md)标准卡片中最常见的PNG格式，Agnai、Tavern、SillyTavern、Risu、Chub、PygmalionAI、JanitorAI的角色请在这些平台导出后在此上传。`,
    my_share: '我的分享',
    user_share: 'Ta的分享',
    user_public: 'Ta的发布',
    fav: '我的收藏'
  },
  pay: {
    wechat_btn: `「微信」备用通道`,
    alipay_btn: `「支付宝」备用通道`,
    recharge_explain1: '充值完成后一般5分钟内💎都会到账。<1>联系客服</1>',
    channel_bk: '备用通道',
    pay_wechat_channel_btn1: `微信支付`,
    pay_wechat_channel_btn2: `微信支付`,
    pay_alipay_channel_btn1: `支付宝支付`,
    pay_alipay_channel_btn2: `支付宝支付`,
    pay_channel_desc: '若无法完成充值，可选择【卡密充值】100%成功哦',
    inventory_tips: `1.限时活动，原价💎永久有效，赠送🟡永久有效
2.支付时禁止填写备注，会影响到账
3.卡密购买后需点击“充值-卡密兑换”将卡密兑换成
4.任何充值问题，可联系客服 <1>@ai_x01_bot</1>`,
    pay_methods: '支付方式',
    diamond_intro: '1钻石=1金币',
    exchanging: '兑换中',
    sorry_login_desc: '抱歉，登录出现问题，请稍后再试～',
    card_active: '卡密兑换',
    input_code: '输入迅雷发卡充值后，页面最下方的卡密',
    input_code_placeholder: '这里输入卡密，卡密参考样式：1234abcd-1234-1234-1234-abcd12345678',
    active: '兑换',
    charge_success: '充值成功',
    charge_success_desc: `充值成功，钻石已发放到游戏中~
        如未到账，请联系客服`,
    charge_failed: '充值失败',
    charge_failed_desc: '抱歉，充值出现问题，请重试～',
    charge_err: '充值异常，原因：',
    remain_diamond: '剩余钻石',
    code_exchange: '卡密兑换',
    inventory: '商品列表',
    recharge_history: '充值记录',
    amount: '金额(USDT)',
    diamond: '钻石',
    balance: '余额',
    explain: '说明',
    create_time: '创建时间',
    expire_time: '过期时间',
    recharge_explain: '计费说明',
    diamond_intro_desc: `1. 1💎=1🟡
2. 原则上先消耗🟡再消耗💎，但若💎即将到期则先把这部分💎先消耗完，再消耗🟡

举例：
（a）若💎🟡均为永久有效期，先消耗🟡，🟡消费为0后再消耗💎
（b）若💎🟡均存在有效期，先消耗🟡；若💎即将过期（比🟡先过期），则先消耗即将过期的💎至归0后，再消耗🟡
    `,
    overtime: '支付超时',
    pay_failed: '支付失败',
    copyed_success: '已复制到剪贴板',
    paying: '正在支付',
    usdt_pay_desc: '你可以从任意钱包或交易所转账至以上地址',
    time_remain: '剩余支付时间',
    order_id: '订单号',
    network: '网络',
    charge_amount: '充值钻石数',
    tips: '温馨提示：',
    tips1: '<0>请勿</0>向上述地址充值USDT-ERC20和TRC20资产，否则资产<2>将不可找回</2>',
    order_created: '订单已生成',
    order_created_desc: `支付链接已经推送到“幻梦AI伴侣”Bot中，请点击下方支付按钮，跳转到Bot页面，点击支付链接支付
    （如果点击按钮无法跳转，请关闭本小程序，在Bot找到推送链接继续操作）`,
    pay: '立即跳转',
    order_created_desc1: '请点击下方支付按钮，跳转到支付页面支付',
    pay1: '立即支付',
    charging: '正在充值',
    charging_desc: '充值成功请点击"充值完成"',
    charging_desc1: `1. 选择💎直接到账的充值方式，充值完成后一般5分钟内💎都会到账。
2. 支付受阻，请点击一下“备用通道”支付。
（提示：卡密购买中也有「{{payChannel}}」哦）`,
    finish_charge: '充值完成',
    charge_err1: '充值异常，请重试',
    btn1: '充值遇到问题请点击',
    method: '解决方案',
    method_desc: '支付不成功，可能是浏览器兼容导致。您可以复制下方链接，在其他浏览器（推荐chrome、QQ浏览器、UC浏览器、夸克浏览器等）中打开尝试支付',
    charge_success_desc1: '钻石已经到账~',
    pay_success: '成功支付',
    no_money: '余额不足',
    no_money_desc: '抱歉，你的USDT余额不足，请充值后重试',
    charge: '立即充值',
    charge1: '支付宝充值1',
    charge2: '微信充值',
    usdt_charge: 'USDT充值',
    credit_card: '信用卡充值',
    start_pay: 'star支付',
    ali2: '支付宝充值2',
    bank: '银联支付',
    wechat_alipay: '卡密购买',
    wechat_alipay_subtitle: '(支持微信、支付宝)',
    wechat: '微信支付',
    wechat_subtitle: '(💎直接到账)',
    alipay: '支付宝支付',
    alipay_subtitle: '(💎直接到账)',
    wechat1: '微信扫码支付1',
    Unionpay: '银联扫码支付',
    copy_fail: '复制失败！',
    usdt_warn: '我们到账金额必须 <1>与上述金额一致</1> 否则无法及时到账<br/>交易所钱包请注意 <5>扣除手续费后的金额</5> 与上述一致',
    min: '分',
    sec: '秒',
    copy_success: '已经复制到粘贴板，打开其他浏览器粘贴网址进行支付',
    copy_addr: '复制支付地址',
    purchase_tips: '（没有？<1>点此</1>购买）',
    exchange_guide_title: '激活码使用方法',
    exchange_guide_desc: `
1. 点击按钮「微信/支付寶充值」，在跳转页面购买后，输入手机号或QQ号查询卡密

2. 复制卡密

3. 在本页面，点击「卡密兑换」-- 输入卡密

4. 【重点】购买后找不到卡密，可以联系客服 @xlfkwkf_bot (长按复制)`,
    view_btn: '查看图例教程',
    exchange_btn: '卡密教学',
    recharge_btn: '充值教学',
    recharge_guide_title: '充值說明',
    recharge_guide_desc: `
微信/支付宝充值：
卡密激活码充值，充值成功后在剩余钻石选择「卡密兑换」，输入激活码后钻石到账

微信扫码支付：
微信扫码直充，支付成功后钻石到账

支付宝扫码支付：
支付宝扫码直充，支付成功后钻石到账

USDT充值：
交易所/钱包充值（我们使用Polygon链，手续费很低，不支持其他链），支付成功后钻石到账`,
    guideClose: '关闭去“幻梦AI伴侣支付”',
    permanent_validity: '永久生效'
  },
  operation: {
    confirm: '确认',
    cancel: '取消',
    clear: '清空',
    save: '保存',
    edit: '编辑',
    refresh: '重新开始',
    send: '发送',
    copy: '复制',
    lineBreak: '换行',
  },
  dialog: {
    status_block_placeholder: '可选择状态栏模版后，仍然可做编辑状态栏（总token数上限500）',
    edit_status_block: '自定义状态栏说明',
    edit_status_block_desc: `**创建方式：**
1.可以选择状态栏模版，基于模版创建状态栏；同时，选择状态栏模版后，可以使用AI一键生成状态栏
2.可以自定义状态栏，自行编辑状态栏及更新规则

**收费说明：**
1.选择状态栏模板、自定义状态栏保存时都会扣除600💎
2.选择状态栏模版，AI一键生成状态栏时已扣除600💎（AI生成的内容未改动），此时保存不再扣除600💎；若AI一键生成状态栏后内容被改动，保存时会再扣除600💎。
3.AI一键生成状态栏消耗600💎（每次点击AI一键生成状态栏都会消耗一次600💎）

**自定义状态栏AI审核说明：**
1.AI审核不通过，无法保存并使用
2.AI审核通过，可保存并使用

**状态栏隐藏、展示说明：**
1.点击按钮可对所有角色卡的状态栏进行隐藏或展示（我的-设置）
2.已隐藏，代表之后AI回复不再展示状态栏
3.已展示，代表之后AI回复展示状态栏`,
    cant_submit: '无法提交！',
    cant_submit_desc: `当前{{char}}、{{user}}存在格式错误，是否切换为正确的格式
 
点击是，自动帮您更改为正确格式
点击否，自己手动更改`,
    yes: '是',
    no: '否',
    update_success: '更新成功',
    delComfirm: '是否要删除？',
    play: '播放',
    right_title: '本卡片来源互联网',
    right_title1: '本卡片来源作者创作',
    right_desc: '如有侵权请反馈官方删除',
    right_desc1: '如有侵权请反馈官方，官方联系作者协助处理',
    avatar: '角色头像',
    avatar_err: '未上传图片',
    cardName: '卡名字',
    require: '必填项',
    len_18: '不能超过18个字',
    name_place_holder: '用于卡片标题展示',
    chat_model: '轻聊天',
    role_model: '角色扮演',
    desc_placeholder: '包括AI角色、使用者角色（必须是名字，而非{user}）、关系、性格、外表、喜好、能力、年龄、性别、种族、国籍、民族等，此设定不对其他人展示',
    desc_title: '角色定义',
    example_title: '对话示例',
    desc_err_token: '超出最大tokens数',
    first_title: '第一条消息',
    first_desc: '开场白，角色初始打招呼的第一句话',
    name_len: '长度超出限制（15个字）',
    role_name: 'AI角色名',
    role_placholder: 'AI扮演的角色名字',
    intro_name: '卡片介绍',
    intro_placeholder: '详细介绍角色特点、羁绊冲突、特殊玩法等，仅用于展示，不影响AI扮演发挥',
    personality_title: '性格',
    personality_placeholder: '对角色性格的设定',
    is_public: '是否公开？',
    scenario_title: '初始场景',
    simplt_intro: '营销文案',
    limit_desc: '（{{n}}个字以内）',
    limit: '不能超过{{n}}个字',
    simplt_intro_placeholder: '在角色列表中显示，简介羁绊、冲突、角色魅力等引人之处。仅用于展示，不影响AI扮演发挥',
    voice: '声音',
    status_block: '状态栏',
    status_block_select: '请选择状态栏类型',
    normal: '标准',
    collapse: '折叠',
    hide: '隐藏',
    status_tmp: '状态栏模版',
    status_sample: `示例：
心情：{心情}
性欲值：{性欲值}
地点：{地点}
小穴状态：{小穴状态}
和{{user}}的关系：和{{user}}的关系
外貌服装：{外貌服装}`,
    status_init_desc: `示例：
心情：兴奋、焦虑
性欲值：33
地点：公共场合
小穴状态：有些兴奋湿润
和{{user}}的关系：一起上学的同学,对他有些好感
外貌服装：干净整洁,发梢有些微乱`,
    status_rule_desc: `示例：
性欲值取值范围为0-100，初始值为33
性欲值更新规则：
当{{char}}的性欲被激发时上升、性欲被压抑时下降，单次对话性欲值上下幅度不超过10
当{{char}}到达高潮时性欲值下降20
服装内容应足够详细，包含内衣、袜子和配饰，如果有裸露部位，要详细描述`,
    status_init: '初始状态栏内容',
    status_rule: '状态栏更新规则',
    label: '标签',
    max_tags_err: '标签数量不能超过12个',
    label_err: '非NSFW标签需要勾选至少一个',
    cat: '分类',
    create_success: '创建成功～',
    creating: '正在创建..',
    create_fail: '创建失败，请重试～',
    sum_token_exceed: '总tokens超出限制',
    next: '下一步',
    prev: '上一步',
    sum_cost: '总消耗',
    operate_success: '操作成功～',
    model_select: '模型选择',
    review_reject: '拒绝原因',
    no_show_tip: '不再显示此提示'
  },
  cardEdit: {
    public_reject_title: '提审违规',
    public_reject_desc: `您违反了公开发布角色卡的审核规范，已经被限制提交审核。

【申请恢复审核】请联系客服❗️`,
    public_reject_confirm: '联系客服',
    sys_tool: '系统和工具',
    play_type_title: '玩法类型说明',
    play_type_desc: `对手戏：AI不抢话，AI扮演一个或多个角色
推剧情：AI会写所有剧情，玩家在里面给出关键决策，AI进行补充
系统工具：各种AI助手、生成器、模拟器，包括小说生成、人物生成、故事生成、游戏模拟、特定世界模拟等`,
    opponent_content: '对手戏',
    push_content: '推剧情',
    init_heat: '初始热度值',
    final_heat: '当前热度值',
    deduct_heat_title: '扣除热度值说明',
    deduct_heat: '被扣除热度值',
    deduct_heat_desc: `1. 在被用户点踩后会被扣除热度值
2. 在AI判断角色卡，使用所选模式不能推进剧情时会被扣除热度值
    
改善方法：
1. 审核角色卡时，逻辑复杂的卡不建议勾选极速模式及以下模式`,
    adapt_model_at_least_one: '至少要选择1个适用模型',
    adapt_model_desc1: '慎重选择：AI会根据所选模式推剧情实际情况影响角色卡热度，若所选模式无法推进剧情，则会降低角色卡热度',
    adapt_model_title: '聊天模式说明',
    adapt_model_desc: `极速模式：速度超快，但无法处理复杂逻辑、推剧情会卡住
魅惑模式：满血版网红模型DeepSeek外加深度调教，速度快，智商在线，可驱动部分复杂逻辑
欲望模式：性格风骚，欲望加强，更容易推倒，可以处理复杂逻辑
世界卡模式：高智商的模型和最佳破解调优，支持复杂卡片驱动，文笔炸裂，逻辑清晰
超长记忆模式：群聊最佳模式，3倍超长记忆的高端模型，智商高、文笔好，适合驱动复杂卡片和推动超长剧情
全能模式：集所有模式优点于一身，智商超高，记忆力超强，文笔超级炸裂，复杂角色扮演和宏大世界卡片模拟能力极强，经常出其不意让你觉得她是真人`,
    md: 'Markdown格式支持查看',
    create1: '手动创建',
    tips_title: '说明',
    advanced_mode: '切换高级模式',
    role_noconstant_content_placeholder: '输入内容token上限1000',
    primy_key_placeholder: '多个关键词用逗号隔开，最多20个字',
    len_20: '内容长度不能超过20个字',
    role_content_placeholder: '输入内容无token上限，但算在总消耗的6000token中',
    constant_role_book: '持续生效世界书',
    key_role_book: '关键词世界书',
    comment_placeholder: '限制最多15个字',
    collaspe_err: '需要修复表单错误，才能折叠',
    form_error: '请检查表单填写是否正确',
    ai_name_repeat: '不能和AI角色名重复',
    no_user_name: '需使用{{user}}替代用户角色名: {{userName}}',
    no_char_name: '需使用{{char}}替代AI角色名: {{charName}}',
    no_user_name1: '不能包含用户角色名: {{userName}}',
    token_limit_exceeded: '不能超过{{limit}}tokens',
    adapModel: '适用模式',
    AIName_placeholder: 'AI扮演的角色名字',
    user_role_name: '用户角色名',
    user_role_name_placeholder: '不会在聊天中显示,但可帮AI更好生成对话内容',
    ai_formate: '回复模式',
    example_placeholder: '对话示例对于AI回复具有极强的指导性。AI会模仿这里语言风格、内容结构，语言体现出的性格、喜好等个性\n需要符合卡模式，注意轻聊模式请使用直接文本和括号()',
    step1: '基础设定',
    step2: '公开卡设定',
    public: '公开发布',
    example_dialog_user_placeholder: `“张阿姨，你自己逛公园呀？“（示例，角色扮演，「语言文字或对话」放“”里`,
    example_dialog_ai_placeholder: `“小明，要不咱们一起逛？”（示例，角色扮潢，「语言文字或对话」放“里`,
    example_dialog_user_placeholder_chat: `张阿姨，你自己逛公园呀`,
    example_dialog_ai_placeholder_chat: `小明，要不咱们一起逛？（示例，轻聊天「非对话」例如场景动作心理等放（）里，「语言文字或对话」无修饰直接输出`,
    replay_len: 'AI回复长度',
    replay_len_title: '说明',
    replay_len_desc: `对话示例对于AI回复具有极强的指导性。AI会模仿这里语言风格、内容结构，语言体现出的性格、喜好等个性。 

需要符合卡模式：

1. 轻聊天，「非对话」例如场景动作心理等放（）里，「语言文字或对话」无修饰直接输出；

2. 角色扮演，「语言文字或对话」放“”里；

这2种模式，都是强制给AI绑定一个固定角色，例如：黑寡妇、加勒比海盗NPC集合（集合代表一个角色）、一个系统、一个小说家。`,
    muilte_scenes_title: '对话场景',
    muilte_scenes_illustrate_title: '轻聊天说明',
    muilte_scenes_illustrate: '轻聊天，「非对话」例如场景动作心理等放（）里，「语言文字或对话」无修饰直接输出',
    muilte_scenes_init: '初始场景',
    muilte_scenes_init_desc: '碰巧在公园遇到小明（示例，小明是用户名）',
    muilte_scenes_first_message: '第一条消息',
    muilte_scenes_first_message_desc: `（看到小明很开心）小明你在这里做什么？（示例，轻聊天「非对话」例如场景动作心理等放（）里，「语言文字或对话」无修饰直接输出`,
    muilte_scenes_first_message_role_desc: `看到小明很开心，“小明你在这里做什么？”（示例，角色扮演，「语言文字或对话」放“”里）`,
    lang_title: '设定支持语言',
    auditing_desc: '审核中，审核完成才能再次发布哦',
    edit: '编辑',
    editing: '编辑中',
    auditing: '审核中',
    approve: '审核通过',
    reject: '审核拒绝',
    select_model: '请选择创建方式',
    new_model: '新手模式',
    advanced_model: '高级模式',
    enable_beginer_tpl: '开启新手模版',
    user: '用户',
    char: '角色',
    add_sample: '增加示例',
    add_dialog_context: '增加对话场景',
    normal_model: '全知视角',
    gender: '性别',
    gender_desc: '女（示例）',
    age: '年龄',
    age_desc: '18（示例，最小18岁）',
    personality: '性格',
    personality_desc: '好色，容易嫉妒（示例，可多个）',
    appearance: '外貌',
    appearance_desc: '身高165cm，长相甜美，高中校服JK款（示例）外貌（身形，长相，穿着）',
    sexual: '性癖',
    sexual_desc: '异味癖，对拒绝敏感（示例，可多个）',
    add: '增加',
    role_book: '世界书',
    order: '顺序：',
    enable: '开启：',
    primy_key: '主要关键字：',
    sec_key: '可选过滤器：',
    trigger: '触发概率(0-100)：',
    content: '内容：',
    status: '状态：',
    constant: '常量',
    normal: '非常量',
    status_title: '状态栏说明',
    status_desc: `1. 不支持标签，请不要输入任何标签
        2. 系统默认增加\`\`\`的代码块支持，不需要单独输入
        
        示例：
        #{character name}
👚服饰状态: {character此刻详细的服饰}
💭内心想法: {character此刻的内心想法}
        `,
    reply_title: '回复模式说明',
    reply_desc: `1. 轻聊天：AI角色第一人称视角，输出{{char}}简单对话，「非对话」例如场景动作心理等放（）里，「语言文字或对话」无修饰直接输出，对话中用“你”提及{{user}}
2. 角色扮演：AI 角色用第三人视角，输出{{char}}的对话，「语言文字或对话」放“”，用“你”来提及{{user}}
3. 全知视角：无视角约定，无文字格式约定，无对话输出要求，需自行约定。

1和2都是强制给AI绑定一个固定角色，例如：小张、黑寡妇、加勒比海盗们（NPC集合，多人共同代表一个角色）、一个系统、一个小说家。

3适用于自行详细定义的角色、系统、模拟器、小说家、叙述者、摄像机、监控器等。`,
  role_book_constant_title: '持续生效世界书',
  role_book_constant_desc: `1. 不限制增加的条目个数
  2. 每一个持续生效世界书，条目内容输入不限制token长度，但算在总消耗的6000token中`,
  role_book_key_title: '关键词世界书',
  role_book_key_desc: `1. 每一个条目内容输入token上限1000
2. 不限制增加条目的个数，但是每次触发时，内容token上限1000
3. 设置多个关键词（逗号隔开）触发同一个条目，当聊天中最新的两轮对话（用户两条消息、AI两条消息）匹配其中一个关键词便触发该条目
4. 当最新的两轮对话（用户两条消息、AI两条消息）同一个关键词同时触发多个条目，或者多关键词触发多个条目，触发内容token上限都为1000
·顺序高的优先触发
·顺序相同时，若多个条目内容token上限不足1000，则全部触发；若多个条目内容token上限超过1000，超过的部分会被截断`,
  author: '作者名',
  showAuthor: '是否展示',
  author_desc: `不勾选展示，角色卡展示的作者名：匿名
  勾选展示，角色卡展示的作者名：您当前的用户名`,
  data: '数据',
  cardName: '卡名字',
  uv: '用户数',
  chatRound: '角色卡热度',
  likeCount: '点赞数',
  auditng: '正在审核',
  audit_desc: '卡片正在审核中，审核结束才能操作',
  low_img: '上传失败',
  low_img_desc: '上传图片宽度不能低于400像素，高度不能低于600像素，请调整图片质量或分辨率后重试～',
  tips: `1. 审核通过时，有可能修改卡的内容(图片\状态栏\角色设定等)
2. 私有卡，仅自己可见
3. 连续累计被拒绝审核6次，会被限制公开发布`,
  del_failed1: '审核中的卡不支持删除',
  has_private_card_title: '无法公开发布',
  has_private_card_desc: `群聊中存在私有卡无法发布
建议：
1、私有卡先公开发布成功
2、公开发布成功的卡，可以通过“创建群聊-搜索”选择加入群聊，角色卡全部为公开卡可发布群聊。`,
  no_user_or_Char: '不能包含{{user}}，请删除',
  max_9: '世界书条目最多不超过9个',
  max_constant_entries: '持续生效世界书最多不能超过3条',
  max_non_constant_entries: '非常量最多不能超过6条',
  quick_type: '快捷输入'
},
  history: {
    no_archive: '没有聊天记录',
    group_card: '群聊卡',
    vip_feature: '发私照',
    load_archive: '读取存档',
    del_history: '删除记录',
    first_chat_at: '首次聊天',
    latest_chat_at: '最后聊天',
    tip: '请至少要选择2个角色～',
    my_role: '创建私有卡',
    my_group: '创建私有群',
    group_title: '群聊创建&扣费规则',
    group_desc: `1. 可选2-4个AI角色卡，包含：幻梦平台上架的AI角色卡，自己创建的AI角色卡

2. 群聊体验好的选卡建议：
  （1）相同故事背景，且彼此相互了解：众所周知的故事（西游记里的人物，海贼王里的角色），或者每张角色卡都设置相同的故事背景，或者在单一角色卡设定里介绍其他角色卡的角色设定和关系
  （2）相同语言模式，相同回复格式，相似状态栏

3. 群聊时，用户手动点击AI角色头像，被点击的AI角色卡会发起回复，若点击骰子，则随机1个AI角色卡主动发起回复

4. 扣费说明：群聊时，用户选择某个聊天模式，每次AI角色卡发起回复，则扣除匹配的钻石（举例：群聊时，极速模式，用户点击某1个AI角色卡头像，该AI角色发起回复，则扣除100钻石）`,
    create_group: '创建群聊',
    group_scenario: '故事线开端',
    group_scenario_desc: '交代清楚角色间关系、所处场景、即将发生的事件等，便于后续开启角色间的互动',
    rec_card: '推荐角色卡',
    my_card: '我的角色卡',
    select_role: '选择群聊角色',
    select_role_desc: '只可选择{{char}}为独立角色的卡片',
    search_placeholder: '搜索角色卡',
    tip1: '群聊不能超过4个角色哦',
    no_create_card: '还没创建卡片，请到"聊天"-"我的卡片"创建',
    search_empty: '搜索结果为空',
    name: '群聊名',
    name_placeholder: '在角色列表中显示，名字起得好会有更多人玩',
    story_bg: '群聊介绍',
    story_bg_desc: '详细介绍角色特点、羁绊冲突、特殊玩法等，仅用于展示，不影响AI扮演发挥',
    scenario: '初始场景',
    scenario_desc: '输入初始场景描述，故事开始的场景，展示在群聊详情页'
  },
  errorMessage: {
  },
  lang: {
    zh: '简体中文',
    en: 'English',
    'zh-TW': '繁体中文'
  },
  lan_short: {
    zh: '简体',
    en: 'En',
    'zh-TW': '繁体'
  },
  privacy,
  terms,
  recentChat: {
    del_chat: '删除记录',
    export_chat: '导出',
    export_card: '导出卡片'
  },
  sort: {
    approved: '审核通过',
    reject: '审核拒绝',
    no_publish: '未发布',
    default: '默认'
  }
}

export default translation
