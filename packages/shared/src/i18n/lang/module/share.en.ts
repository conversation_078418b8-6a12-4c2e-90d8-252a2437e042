
const share = {
    share_btn: 'Invite friends to earn 1000🟡',
    join_group_desc: 'Join the Huanmeng AI group first before you can share',
    joined_group: 'Already joined the group',
    join_group: 'Go to join the group',
    not_in_group: 'Currently unable to share, need to join the Huanmeng AI group before you can share',
    title: 'Sharing Conversation',
    title1: 'Title',
    desc: 'Description',
    desc_len: 'Length exceeds the limit',
    share_time: 'Time',
    generate: 'AI generates descriptions',
    sharer: 'Sharer',
    title_placeholder: 'Share title, up to 15 characters',
    desc_placeholder: 'Share description, up to 100 characters',
    tip_desc: 'The wonderful moments of the chat can be "shared" to the TG group, passing on your happiness to others and chatting out different wonderful plots',
    generate_desc_tip: 'This function is generated by AI. AI will combine the development of your current chat history to generate an overall description of this chat. It costs 50💎 to generate it once.',
    new_chat: 'Start a new conversation with this character',
    comfirm: 'Go to share',
    comfirm1: 'Go to share ({{n}}/10 times)',
    loading1: 'Generating...',
    generate_success: 'Generated successfully! ',
    share_success_desc: `You have completed sharing, and AI will review the content you shared

    1. If the content is compliant, the content shared within 30 seconds will be sent to the user group "Speak freely"

    2. If the content is not compliant, it will not be sent to the user group, and the bot robot will push the violation reason to you (you need to use compliant content before you can share again)`,
    share_limit: 'The share limit for today has been reached. Please come back tomorrow to share again.'
}
export default share