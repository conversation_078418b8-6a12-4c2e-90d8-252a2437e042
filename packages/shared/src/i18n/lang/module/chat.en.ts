
const chat = {
    model_not_exist: 'The currently selected model is offline and has been switched to the recommended model: {{name}}',
    same_model: 'The current mode is the same as the target mode',
    vip_model_desc: 'Exclusive [Fast]: Chatting consumes 💎 coins, and will prioritize the stability of the model and efficient text output',
    share_model_desc: 'Shared [Slow]: Chatting consumes free chat mode times, the computing power used is shared, poor stability, slow model, and text output requires queuing',
    new_user_recharge_tips: 'Congratulations🎉 for getting the new user recharge gift package',
    new_user_recharge_tips_desc: 'The more you recharge, the more you get. Only one chance for the first recharge! Smart friends have already chosen the package with a higher gift ratio',
    switch_model_tip: 'Switch mode: The more advanced mode AI will be smarter, more emotionally intelligent, and more real',
    benefit_title: 'A meeting gift',
    benefit_desc: `🎉Congratulations! You got the lucky gift package!The platform gives you 60 chat rights for free! (You can receive rewards every day after completing the task)`,
    benefit_btn: 'Click to claim now',
    benefit_claim_status: 'Get it successfully',
    tip_change_mode_auto_free: 'Automatically switch to model: {{to_model_name}}',
    tip_change_mode_auto1: 'Current mode: {{from_model_name}} has insufficient free quota, and has been switched to: {{to_model_name}}',
    free_benifit: 'Share',
    paid_benifit: 'Exclusive',
    no_free_benefit1: 'This chat mode has insufficient remaining times, please switch to other modes',
    remain: 'Remaining',
    net_slow: 'Slow',
    net_fast: 'Fast',
    net_off: 'None',
    switch_share: 'Share',
    switch_pay: 'Privilege',
    tab_share: '【Share】Slow',
    tab_pay: '【Privilege】Fast',
    current: 'Current',
    switch_consume_comfirm: `Exclusive
    (Use 💎 coins)`,
    switch_consume_cancel: `Share
    (Use free times)`,
    switch_consume: 'Select the deduction method',
    switch_consume_desc: `Please select the deduction method for chatting with AI
1. Shared, chat consumes the number of free chat mode rewards
2. Exclusive, chat consumes 💎 gold coins`,
    switch_failed1: 'Please wait for the AI ​​to reply before switching.',
    tip_change_mode_man: 'Manually switch to: {{to_model_name}}',
    tip_change_mode_auto: 'This card does not support {{from_model_name}}, and has automatically switched to: {{to_model_name}}',
    tip_change_mode_entry: 'Current mode: {{to_model_name}}',
    no_banlance_msg: 'Balance is insufficient, recharge to continue chatting',
    go_charge: 'Go to recharge',
    dice_tooltip: "Click the dice for a random AI reply",
    avatar_tooltip: "Click an AI avatar to get a reply from that AI",
    play_type: 'Play Type',
    tokens: 'Number of tokens',
    switch_top_free_model: 'Switch {{model_name}}',
    btn_pay: 'Experience advanced mode with a minimum of 9.9',
    switch_model: 'Choose {{model}}',
    continue_model: 'Continue using {{model}}',
    switch_desc: `❗️ Modes that failed the test: {{not_pass_model}}

In the above modes, the AI ​​chat experience will be affected, such as character settings not taking effect, garbled replies, difficulty in plot advancement, etc.

✅ Recommended chat mode: {{supported_model}}
The above modes have been tested by the author and AI can function normally.

Note: In more advanced modes, AI will be smarter, more emotionally intelligent, more realistic, and have more exciting writing and plots.`,
    switch_desc1: `This card does not support {{model_name}}, and has automatically switched to {{target_model_name}} mode`,
    switch_desc2: `❗️ Mode that failed the test: {{not_pass_model}}

    In the above modes, the AI ​​chat experience will be affected, such as character settings not taking effect, garbled replies, difficulty in advancing the plot, etc.

    ✅ Recommended chat mode: {{supported_model}}
    The above modes have been tested by the author, and AI can function normally.

    Note: In more advanced modes, AI will be smarter, more emotionally intelligent, more realistic, and more exciting.`,        
    like_success: 'Success',
    dislike_success: 'Dislike Success',
    copy_success: 'Copy successful',
    copy_fail: 'Copy failed',
    msg_too_long: 'The upper limit of the content is {{limit}} tokens. Please adjust it before sending if the limit is exceeded.',
    net_err1: 'Message exception',
    net_err_desc: `The message was not sent successfully due to network reasons. The next time you enter the conversation or refresh the page, the system will automatically clear invalid messages`,
    no_share: 'No user has shared chat content yet',
    square: 'Wonderful Square',
    private: 'Private Card',
    newChat: 'New Chat',
    init_scenario: 'Initial Scenario:',
    empty: 'Not Filled',
    private_card: 'Private',
    content_err: 'Content error, status code: {status}, description: {errMsg}, please try again～',
    net_err: 'Network error, this conversation is free of charge, please try again～',
    recall: 'Recall',
    copy: 'Copy',
    retry: 'Retry',
    tips: 'Warm Tips:',
    comfirm: 'Got it',
    tips_desc: `AI can help you generate dialogues and continue to advance the plot, but every time you click on the light bulb, the 💎 consumed by the current chat mode will be deducted

Are you sure that the additional 💎 will be consumed by AI to help you generate dialogues?`,
    wating_desc: 'Waiting for data to return, please try again later~',
    warn: 'Reminder',
    warn_desc: `Start a new chat, support selecting the last 3 chat records, and read the archives at the following locations:
Location 1: Mini Program - Chat - Role Card Information
Location 2: Mini Program - Role Card Chat Window Settings in the upper right corner
Do you want to continue?`,
    try_ai_answer: 'Try an AI answer',
    cancel_reply: 'Cancel reply',
    person_card: 'Personal Card',
    person_limit: 'Personal Use only',
    price: 'Diamond / Round ',
    switch_success: 'Switch successful',
    switch_failed: 'Switch Failed',
    msg_not_empty: 'Message cannot be empty',
    show_all_chats: 'Default: {{n}} rounds',
    view_all: 'To view all records, please <1>click here</1>',
    export_all: 'To export all history records, please <1>click here</1>',
    history: 'Chat History',
    export_title: 'Exported',
    export_desc: 'The chat records have been sent via bot. Please return to the bot page to download them.',
    del_failed: 'Error, Please try again',
    img: 'Photos',
    img_title: '【Limited Time Offer】Sexy Private Photo, Just for You!',
    img_title1: '【Limited time offer】Sexy private photos, not satisfied? ! ',
    img_desc: 'Looking for a more intimate interaction? Originally priced at 2000💎, now only 500💎 during the promotion! Your exclusive AI character will send you a sexy, private photo, fully customized just for you. Don’t miss this special surprise made only for you!',
    img_desc1: 'You can regenerate it. The retry price is 400💎, the original price is 2000💎, 20% off! 20% off! 20% off! The original generated private photos will be retained and a new sexy private photo will be generated! ',
    img_confirm_btn: 'View Photo',
    img_cancel_btn: 'Hold Back',
    one_img_tips: 'Only one image can be generated per reply~',
    load_fail: 'Image loading failure',
    retry_tips_title: 'Reminder',
    retry_tips_desc: `Hi, want AI's response to better match your expectations? 😍 Each retry will regenerate the message and deduct a chat fee. If you clicked 'Photos' before, the photo will disappear, and sending it again will require extra diamonds. 💎

Don't worry, AI will offer an even better reply! ✨ For a more perfect interaction, just give it another click. Retry this chat?`,
    retry_tips_cancel_btn: 'Cancel',
    switch: 'Switch Now',
    keep: 'Cancel',
    start_chat: 'Start a private chat',
    fav: 'Collect',
    faved: 'Collected',
    fav_success: 'Collected successfully, can be viewed in My-My Collection',
    del_fav_success: 'The collection has been cancelled. You can view it in My-My Collection',
    continue: 'Continue',
    continue_title: 'Continue reply instructions',
    continue_title_desc: `1. "Continue" button function: When the AI ​​character's reply content exceeds the token limit, click "Continue" to let the AI ​​continue to write the unfinished message

2. Each time you "continue"
(1) When 💎 is consumed, 50% of the 💎 required for a single chat in the current mode will be deducted (for example: 600 💎 for a single chat in the current mode, 300 💎 will be deducted when you "continue")
(2) When the number of rights is consumed, the number of rights for 1 chat in the current mode will be deducted (the minimum unit of the number of rights is 1, there is no 0.5 times, so 1 time will be deducted)

3. ‼ ️The system cannot accurately determine whether the "text" has been generated. Clicking will result in a fee deduction. Please use with caution‼ ️`,
    continue_tips: `Sorry, the current model ({{model}}) does not support the continue reply function, please switch to a more advanced model and try again~`
  }
export default chat