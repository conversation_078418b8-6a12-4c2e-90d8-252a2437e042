
const chat = {
    model_not_exist: '目前選擇的模型已下線，已切換到建議模型:{{name}}',
    same_model: '目前模式和目標模式相同',
    vip_model_desc:'可享[快速]：聊天消耗💎金幣，會優先保障模型的穩定性及高效的文字輸出',
    share_model_desc:'共享[慢速]：聊天消耗免費聊天模式次數，使用的算力是共享的，穩定性差、模型慢、文字輸出需要排隊',
    new_user_recharge_tips: '恭喜🎉取得新使用者儲值大禮包',
    new_user_recharge_tips_desc: '充多送多，僅首充一次機會喔！聰明的朋友已經選擇贈送比例多的套餐啦',
    switch_model_tip: '切換模式：更進階的模式AI會更聰明、更高情緒智商、更真人',
    benefit_title: '一份見面禮',
    benefit_desc: `🎉恭喜你！被幸運大禮包砸中！平台免費贈送你60次聊天權益！（完成任務可每天領取獎勵）`,
    benefit_btn: '立刻點擊領取',
    benefit_claim_status: '領取成功',
    tip_change_mode_auto_free: '自動切換到模型：{{to_model_name}}',
    tip_change_mode_auto1: '目前模式：{{from_model_name}}免費額度不足，已切換至：{{to_model_name}}',
    free_benifit: '共享',
    paid_benifit: '尊享',
    no_free_benefit1: '該聊天模式，剩餘次數不足，請切換其他模式',
    remain: '剩餘',
    net_slow: '慢速',
    net_fast: '快速',
    net_off: '無',
    switch_share: '共享',
    switch_pay: '尊享',
    tab_share: '【共享AI算力】',
    tab_pay: '【尊享AI算力】',
    current: '當前',
    switch_consume_comfirm: `尊享
    （使用💎金幣）`,
    switch_consume_cancel: `共享
    （使用免費次數）`,
    switch_consume: '選擇扣費方式',
    switch_consume_desc: `請選擇和AI聊天的扣費方式
1. 共享，聊天消耗獎勵的免費聊天模式次數
2. 尊享，聊天消耗💎金幣`,
    switch_failed1: '請等AI回覆完成再進行切換操作',
    tip_change_mode_man: '手動切換到：{{to_model_name}}',
    tip_change_mode_auto: '此卡不支援{{from_model_name}}，已經自動切換到：{{to_model_name}}',
    tip_change_mode_entry: '目前模式：{{to_model_name}}',
    no_banlance_msg: '餘額不足，儲值後繼續暢聊',
    go_charge: '去儲值',
    dice_tooltip: "點擊骰子，隨機一個AI回復",
    avatar_tooltip: "點哪個AI頭像，哪個AI回復",
    tokens: 'token數',
    switch_top_free_model: '切換{{model_name}}',
    btn_pay: '最低9.9體驗高階模式',
    switch_model: '選擇{{model}}',
    continue_model: '繼續用{{model}}',
    switch_desc: `❗️ 測試不通過的模式：{{not_pass_model}}

    上述這些模式，AI聊天體驗會受到影響哦，例如角色設定不生效、回覆出現亂碼、劇情推進困難等

    ✅ 建議的聊天模式：{{supported_model}}
    上述模式經過作者測試，AI可以正常發揮。

    注意：更高級的模式AI會更聰明、更高情緒智商、更真人、文筆劇情更精彩哦`,
    switch_desc1: `此卡不支援{{model_name}}，已經自動切換到{{target_model_name}}模式`,
    switch_desc2: `❗️ 測試不通過的模式：{{not_pass_model}}

    上述這些模式，AI聊天體驗會受到影響哦，例如角色設定不生效、回覆出現亂碼、劇情推進困難等

    ✅ 建議的聊天模式：{{supported_model}}
    上述模式經過作者測試，AI可以正常發揮。

    注意：更進階的模式AI會更聰明、更高情緒智商、更真人、文筆劇情更精彩哦`,
    like_success: '點讚成功',
    dislike_success: '踩成功',
    copy_success: '複製成功',
 copy_fail: '複製失敗',
    msg_too_long: '內容上限{{limit}}token，已超出限制請調整後發送',
    net_err1: '訊息異常',
    net_err_desc: `該訊息由於網路等原因，沒有發送成功。下次進入該對話或刷新頁面，系統會自動清理無效訊息`,
    no_share: '還沒有使用者分享過聊天內容',
    square: '精彩廣場',
    private: '個人創建私有卡',
    card_intro: '卡片介紹',
    newChat: '新對話',
    init_scenario: '初始場景：',
    empty: '未填寫',
    private_card: '私有卡',
    content_err: '內容異常，狀態碼: {{status}}，說明: {{errMsg}}，請重試～',
    net_err: '網絡異常，本次對話不扣費，請重試～',
    recall: '撤回',
    copy: '複製',
    retry: '重試',
    tips: '溫馨提示：',
    comfirm: '知道了',
    tips_desc: `AI可以幫助您產生對話，繼續推動劇情，但每點擊一次小燈泡，會扣除當前聊天模式所需消耗的💎

你確認，額外消耗💎透過AI幫你生成對話嗎？`,
    wating_desc: '等待數據返回中，請稍後再試～',
    warn: '提醒',
    warn_desc: `開始新聊天，支援選擇最近的3次聊天記錄，可在以下位置讀取存檔：
位置一：小程式-聊天-角色卡訊息
位置二：小程式-角色卡聊天視窗右上角設定中
是否繼續`,
    try_ai_answer: '試一試 AI回答',
    cancel_reply: '取消回覆',
    person_card: '個人上傳卡片',
    person_limit: '僅限個人使用',
    price: '鑽石/條',
    switch_success: '切換成功',
    switch_failed: '切換失敗',
    msg_not_empty: '消息不能爲空',
    show_all_chats: '默認展示{{n}}輪對話',
    view_all: '查看全部記錄請<1>點擊</1>',
    export_all: '導出歷史記錄請<1>點擊</1>',
    history: '聊天曆史',
    export_title: 'Exported',
    export_desc: '聊天記錄已通過bot推送，請回到bot頁面下載',
    del_failed: '執行異常，請重試',
    img: '發私照',
    img_title: '【限時特惠】性感私照，專屬給你！',
    img_title1: '【限時特惠】性感私照，不滿意？ ！ ',
    img_desc: '想要更親密的互動嗎？原價2000💎，特惠期間僅需500💎，你的專屬AI角色將為你發送一張性感私房照，絕對私人訂製，獨享親密時刻！千萬別錯過這份為你準備的獨家驚喜！',
    img_desc1: '可以重新生成哦，重試價格400💎，原價2000💎，2折扣！ 2折！ 2折！原有生成的私照都會保留，再產生一張新的性感私房照！ ',
    img_confirm_btn: '查看私照',
    img_cancel_btn: '忍住不看',
    one_img_tips: '同一個回覆只能生成一張圖片哦～',
    load_fail: '圖片加載失敗',
    retry_tips_title: '溫馨提示',
    retry_tips_desc: `嗨，想讓Ta的回應更符合你的心意嗎？😍 每次重試都會讓AI重新生成這句話，並扣除一次聊天費用哦。如果你之前點擊了「發私照」，重試後照片也會消失，重新發私照需要單獨消耗鑽石～💎
    
    不過別擔心，Ta會為你送上更加讓你滿意的回覆！✨ 感受更完美的互動，只需再輕輕一點～你要重試這次聊天嗎？`,
    retry_tips_cancel_btn: '再想想，先不重試',
    switch: '立即切換',
    keep: '保持',
    start_chat: '開始私密聊天',
    fav: '收藏',
    faved: '已收藏',
    fav_success: '收藏成功，可在 我的-我的收藏 查看',
    del_fav_success: '已取消收藏，可在 我的-我的收藏 查看',
    continue: '繼續',
    continue_title: '繼續回覆說明',
    continue_title_desc: `1.「繼續」按鈕作用：AI角色回覆內容超過token上限時，點選「繼續」AI續寫未回覆的訊息

2. 每次「繼續」
（1）消耗💎時，會扣除當前聊天模式單次所需💎的50%（例如：當前模式下單次聊天600💎，「繼續」則扣除300💎）
（2）消耗權益次數時，會扣除目前聊天模式1次的權益次數（權益次數最小單位1次，沒有0.5次，故扣1次）

3. ‼ ️系統無法準確判斷「文字」是否生成完畢，點選會扣費，請謹慎使用哦‼ ️`,
    continue_tips: `抱歉，目前模型（{{model}}）不支援繼續回覆功能，請切換到更進階模型再嘗試~`
  }
export default chat