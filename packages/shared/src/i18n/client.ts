'use client'
import i18next from 'i18next'
import { initReactI18next } from 'react-i18next'
import Cookies from 'js-cookie'
import LanguageDetector from 'i18next-browser-languagedetector'
import appEn from './lang/app.en'
import appZh from './lang/app.zh'
import appZhTW from './lang/app.zh-TW'
import type { Locale } from './settings'
import { LOCALE_COOKIE_NAME, getOptions } from './settings'

const resources = {
  en: {
    translation: {
      app: appEn
    },
  },
  zh: {
    translation: {
      app: appZh
    },
  },
  'zh-TW': {
    translation: {
      app: appZhTW
    },
  },
}

i18next
  .use(initReactI18next)
  .use(LanguageDetector)
  .init({
    ...getOptions(),
    lng: undefined, // let detect the language on client side
    detection: {
      order: ['path', 'htmlTag', 'cookie', 'navigator'],
      lookupFromPathIndex: 0, // Adjust based on the position of the language code in your URL
    },
    resources,
  })

export const setLocaleOnClient = (locale: Locale) => {
  Cookies.set(LOCALE_COOKIE_NAME, locale)
  i18next.changeLanguage(locale)
}
