import {web, tgWeb} from '@/app/module/global'
export const fallbackLng = tgWeb? 'en' : web? 'zh-TW' : 'zh'
// export const languages = web? [fallbackLng] : [fallbackLng, 'zh-TW']
export const switchLanguages = tgWeb? [fallbackLng] : web? [fallbackLng, 'en'] : [fallbackLng, 'zh-TW', 'en']
export const defaultNS = 'translation'
export const LOCALE_COOKIE_NAME = 'i18next'

export type Locale = typeof switchLanguages[number]

export function getOptions(lng = fallbackLng, ns = defaultNS) {
  return {
    // debug: true,
    supportedLngs: switchLanguages,
    // preload: languages,
    fallbackLng,
    lng,
    fallbackNS: defaultNS,
    defaultNS,
    ns,
  }
}
