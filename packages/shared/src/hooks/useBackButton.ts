import { useCallback, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import sdk from '@share/src/module/sdk';
import backFn from '@share/src/module/back';
import { isTG } from '@share/src/module/global';

/**
 * 返回按钮处理函数类型
 */
type BackHandlerType = () => void;

/**
 * 自定义Hook，用于处理返回按钮功能
 * 封装了SDK的BackButton相关操作，避免在多个组件中重复相同的代码
 * 支持三种返回模式：
 * 1. 默认返回行为（使用浏览器历史）
 * 2. 使用back函数（支持语言参数和from参数）
 * 3. 完全自定义的返回处理函数
 * 
 * @param options 配置选项
 * @param options.customBackHandler 自定义返回处理函数
 * @param options.useBackFn 是否使用back函数（支持语言参数）
 * @returns 包含backHandler函数的对象
 */
export const useBackButton = (options?: {
  customBackHandler?: BackHandlerType;
  useBackFn?: boolean;
}) => {
  const router = useRouter();
  const params = useParams();
  const lang = params.lang as string;
  
  /**
   * 默认返回处理函数，使用浏览器历史
   * 如果历史记录存在，则返回上一页
   * 否则导航到首页
   */
  const defaultBackHandler = useCallback(() => {
    if (window.history.length >= 2) {
      router.back();
    } else {
      router.push('/');
    }
  }, [router]);

  /**
   * 使用back函数的处理函数，支持语言参数和from参数
   */
  const backFnHandler = useCallback(() => {
    backFn(router, lang);
  }, [router, lang]);

  // 根据选项确定使用哪个处理函数
  const backHandler = options?.customBackHandler || 
                     (options?.useBackFn ? backFnHandler : defaultBackHandler);

  // 设置SDK的BackButton相关操作，只在Telegram环境中执行
  useEffect(() => {
    // 只在Telegram环境中处理返回按钮
    if (isTG) {
      // 显示返回按钮
      sdk.BackButton.show();
      // 设置点击事件处理函数
      sdk.BackButton.onClick(backHandler);
      
      // 清理函数，组件卸载时执行
      return () => {
        // 移除点击事件处理函数
        sdk.BackButton.offClick(backHandler);
        // 隐藏返回按钮
        sdk.BackButton.hide();
      };
    }
  }, [backHandler]);

  return { backHandler };
};
