'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import type { GlobalConfig } from './module/globalConfig'
import { getCookie } from 'cookies-next';
import { query } from '@share/src/module/urlTool'
import useRequest from './hook/useRequest';

interface ConfigContextType {
  config: GlobalConfig
}

export const ConfigContext = createContext<ConfigContextType | null>(null)
export const ConfigProvider = ({ children, _config }: { children: React.ReactNode, _config: GlobalConfig }) => {
  const [config, setConfig] = useState<GlobalConfig>(_config || {})
  const request = useRequest();
  // console.log('config', config)

  const updateConfig = (config: GlobalConfig) => {
    let sfwTmaList: string[] = config?.sfw_tma || ['tma_10086', 'tma_ai16z' ];
    const botId = query.get("botid") || getCookie('botid');
    // 只对指定小程序和后台（botid undefined）的图片进行模糊处理
    let sfwApp = sfwTmaList.includes(String(botId));
    setConfig({ ...config, sfwApp })
    // window.config = config
  }
  const getGlobalConfig = async () => {
    const res: any = await request('/config')
    updateConfig(res)
  }
 
  useEffect(() => {
    // 如果config为空，本地请求服务端config
    if(Object.keys(config).length === 0) {
      getGlobalConfig()
    } else {
      updateConfig(config)
    }
  }, [])

  return (
    <ConfigContext.Provider value={{ config }}>
      {children}
    </ConfigContext.Provider>
  )
}

// 通过hook const config = useConfig() 调用
export const useConfig = () => {
  const context = useContext(ConfigContext)
  if (!context) {
    throw new Error('useConfig must be used within a ConfigProvider')
  }
  return context.config
}
