import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "../shared/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    typography: require('./typography'),
    extend: {
      fontFamily: {
        'crimson-text': ['var(--font-crimson-text)', 'serif'],
        'delius-swash-caps': ['var(--font-delius-swash-caps)', 'cursive'],
      },
      keyframes: {
        fadeIn: {
          '0%': { 'background-color': 'rgba(0, 0, 0, 0)' },
          '100%': { 'background-color': 'rgb(75 85 99 / 50%)' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95) translate(0, -50%)', opacity: '0' },
          '100%': { transform: 'scale(1) translate(0, -50%)', opacity: '1' },
        }
      },
      animation: {
        fadeIn: 'fadeIn 0.5s ease',
        scaleIn: 'scaleIn 0.3s ease',
      },
      dropShadow: {
        'u': '0 0 .4rem #dbdbdb',
      }
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('tailwindcss-animated')
  ],
  darkMode: 'class'
};
export default config;
