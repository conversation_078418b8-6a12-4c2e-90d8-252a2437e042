'use client'
import React, { useCallback, useContext, useEffect, useState, Suspense } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation'
import { AuthContext } from '@little-tavern/shared/src/authContext'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import Image from 'next/image'
import { Markdown } from '@/app/[lang]/components/markdown'
import MyNav from '../components/MyNav'
import Loader, { LoadingToast, LoadingToastFailed } from '@little-tavern/shared/src/ui/Loading'
import { Heart, Star, Fullscreen, ThumbsDown } from 'lucide-react';
import Toast from '@share/src/ui/toast'
import useLightBox from '@share/src/hook/useLightBox'
import useSWRImmutable from 'swr';
import { imgBlur } from '@share/src/module/urlTool';
import { RoleType } from '@share/src/ui/card'
import s from '../globals.module.css'
import useLinkToChat from '@/app/hook/useLinkToChat'
// import ShareList from '../components/share-list/shareList'
import Chatlist from '../components/share-list/chatList'
import Tags from '@share/src/ui/card/tags'
import Link from 'next/link'
import Illustrate from '@share/src/ui/tooltip/illustrate'
import {LoginStatus} from '@share/src/authContext'
import { tgWeb } from '@/app/module/global'

const Main = () => {
  const { t } = useTranslation()
  const params = useParams()
  const searchParams = useSearchParams()
  const lang = params.lang as string
  const roleId = searchParams.get('id')
  const type = searchParams.get('type') || ''
  const isGroup = type === 'group'
  // 必须传参数是模糊，才模糊
  const isBlurImg = searchParams.get('blur') === '1'
  const request = useRequest();
  const [fav, setFav] = useState(false)
  const lightBox = useLightBox();
  const auth = useContext(AuthContext);
  const user = auth?.user;
  const linkToChat = useLinkToChat();
  const [likeCount, setLikeCount] = useState(0)
  const [disLikeCount, setDisLikeCount] = useState(0)
  const [like, setLike] = useState(false)
  const [disLike, setDisLike] = useState(false)
  const [productIds, setProductIds] = useState<any[]>([])

  const url = roleId ? (isGroup ? `/roles/group/detail?group_id=${roleId}` : `/roles/detail?role_id=${roleId}`) : null;
  const { data, isLoading, error, mutate } = useSWRImmutable(url, request, {
    revalidateOnFocus: false
  });
  // console.log('data', data, isLoading);
  const card = isGroup ? data?.group : data;
  useEffect(() => {
    if (!data) return;
    // console.log('fetchUserInfo', card);
    setFav(card?.favorite)
    setLike(card?.like)
    setLikeCount(card?.like_count)
    setDisLike(card?.dislike)
    setDisLikeCount(card?.dislike_count)
    const ids = user?.chat_products?.filter((item: any) => card?.support_product_ids?.includes(item.mid)) || []
    setProductIds(ids)
  }, [data])

  useEffect(() => {
    linkToChat.prefetch({ isGroup, id: roleId })
  }, [])

  const [tab, setTab] = useState('detail');
  const tabs = [
    {
      id: 1,
      name: 'detail',
      title: t('app.chat.card_intro')
    },
  ]
  // 非私有卡和群主卡才能支持精彩广场
  const isSingleCard = !(card?.private_card || isGroup)
  isSingleCard && tabs.push({
    id: 2,
    name: 'share',
    title: t('app.chat.square')
  })

  // const groupAvatar = card?.roles?.map((role: any) => role.role_avatar)
  const onStartChat = (id: number) => {
    console.log('onSlectRole', id);
    linkToChat.push({ isGroup, id })
  }

  const retry = () => {
    mutate()
  }

  const favHandler = async () => {
    Toast.showLoading('')
    const res = await request(fav ? `/user/favorite/delete?mode_type=${type}&mode_target_id=${roleId}` : `/user/favorite/add?mode_type=${type}&mode_target_id=${roleId}`, { method: 'POST' })
    Toast.hideLoading();
    if (res.error_code === 0) {
      Toast.notify({
        type: 'success',
        message: fav ? t('app.chat.del_fav_success') : t('app.chat.fav_success')
      })
      setFav(!fav)
    } else {
      Toast.notify({
        type: 'warning',
        message: res.message
      })
    }
  }
  const likeHandler = async () => {
    if (like) {
      Toast.notify({
        type: 'success',
        message: t('app.chat.repeat_like')
      })
      return;
    }
    Toast.showLoading('')
    const res = await request(fav ? `/user/like?mode_type=${type}&mode_target_id=${roleId}` : `/user/like?mode_type=${type}&mode_target_id=${roleId}`, { method: 'POST' })
    Toast.hideLoading();
    if (res.errorCode !== 0) {
      Toast.notify({
        type: 'success',
        message: t('app.chat.like_success')
      })
      setLikeCount((data?.like_count || 0) + 1)
      setLike(true)
    } else {
      Toast.notify({
        type: 'warning',
        message: res.message
      })
    }
  }
  const disLikeHandler = async () => {
    if (disLike) {
      Toast.notify({
        type: 'success',
        message: t('app.chat.repeat_dislike')
      })
      return;
    }
    Toast.showLoading('')
    const res = await request(fav ? `/user/dislike?mode_type=${type}&mode_target_id=${roleId}` : `/user/dislike?mode_type=${type}&mode_target_id=${roleId}`, { method: 'POST' })
    Toast.hideLoading();
    if (res.error_code === 0) {
      Toast.notify({
        type: 'success',
        message: t('app.chat.dislike_success')
      })
      setDisLikeCount((data?.dislike_count || 0) + 1)
      setDisLike(true)
    } else {
      Toast.notify({
        type: 'warning',
        message: res.message
      })
    }
  }
  const tokens = card?.sum_token
  // 如果登录失败或者登录成功，都展示图像，登录中不展示
  const isShowBlurImg = auth?.loginStatus === LoginStatus.failed || auth?.loginStatus === LoginStatus.success
  // 登录失败展示模糊图片
  const canBlurImg = auth?.loginStatus === LoginStatus.failed? true : !user?.show_nsfw_image;
  return (
    <>
      <MyNav>{card?.card_name || card?.name}</MyNav>
      <main className="mt-[44px] h-[calc(100%_-_44px_-_env(safe-area-inset-bottom))] overflow-auto">
        {isLoading ? <Loader msg={t('app.common.loading')} /> : <>
          {
            card && <><div className={cn('max-w-[1280px] px-3 mx-auto md:flex md:gap-5 pt-2 md:pt-5 pb-20')}>
                <div className='relative z-0 md:w-[min(300px,30vw)] mb-2'>
                  {
                    isGroup ? <div className="md:w-auto md:h-auto overflow-hidden rounded dark:bg-gray-800 bg-gray-300 mx-2 !mx-auto mt-1 mb-1 relative grid grid-cols-2 gap-1">
                      {

                        card?.roles?.map((role: RoleType, index: number) => {
                          return <Image key={index} className='inline-block rounded max-w-none w-full aspect-square' style={{ objectFit: "cover" }} src={isShowBlurImg ? imgBlur({ url: role.role_avatar || '/dot.png', isBlurImg: role.image_nsfw && isBlurImg, canBlur: canBlurImg }) : '/dot.png'} width={150} height={225} alt={role?.name || role?.card_name} />
                        })
                      }
                      {card?.private_card && <>
                        <div className='opacity-75 absolute bottom-0 left-0 rounded-bl rounded-tr px-1  bg-red-500 text-xs text-white'>{t('app.chat.private')}</div>
                      </>}
                    </div> :
                    <>
                      <div className='md:aspect-square md:h-auto !mx-auto relative z-0 mb-3'>
                        <Image className='rounded dark:bg-gray-800 bg-gray-200 object-cover w-full h-[min(360px,50vh)] md:aspect-2/3 md:h-auto' src={isShowBlurImg ? imgBlur({ url: card.role_avatar || '/dot.png', isBlurImg: card.image_nsfw && isBlurImg, canBlur: canBlurImg }) : '/dot.png'} width={600} height={800} quality={90} alt={card.card_name || card.role_name} onClick={() => {
                          lightBox.show({ src: card.role_avatar });
                        }} />
                        <Fullscreen className='bg-purple-600 px-0.5 text-white rounded-sm opacity-85 w-5 h-5 absolute bottom-1 right-1 cursor-pointer' onClick={() => {
                          lightBox.show({ src: card.role_avatar });
                        }} />
                        {card?.private_card && <>
                          <div className='opacity-75 absolute bottom-0 left-0 rounded-bl rounded-tr px-1  bg-red-500 text-xs text-white'>{t('app.chat.private')}</div>
                        </>}
                      </div>
                      <div className='w-full flex justify-between mb-1'>
                      {!card?.private_card && <div className='flex'>
                        {!isGroup && <><button onClick={likeHandler} className={cn('rounded-full px-3 py-1.5 bg-white dark:bg-gray-800 text-xs flex justify-center items-center mr-2 dark:text-white text-gray-600', like && '!text-pink-500')}>
                          <Heart className={cn('w-4 h-4 mr-0.5')} />
                          {likeCount}
                        </button>
                          <button onClick={disLikeHandler} className={cn('rounded-full px-3 py-1.5 bg-white dark:bg-gray-800 text-xs flex justify-center items-center mr-2 dark:text-white text-gray-600', disLike && '!text-amber-500')}>
                            <ThumbsDown className='w-4 h-4 mr-0.5' />
                            {disLikeCount}
                          </button>
                        </>
                        }
                        <button onClick={favHandler} className={cn('rounded-full px-3 py-1.5 bg-white dark:bg-gray-800 text-xs flex justify-center items-center mr-2 dark:text-white text-gray-600', fav && '!text-purple-500')}>
                          <Star className='w-4 h-4 mr-0.5' />
                          {fav ? t('app.chat.faved') : t('app.chat.fav')}
                        </button>
                      </div>}
                    </div>
                    <Tags className='mt-3' tags={card?.sub_tags} />
                    </>
                  }
                </div>
                <div className='flex-1'>
                  {
                    <div className={cn('text-[15px]')}>
                      <h3 className='text-sm mb-1 text-gray-400'>{t('app.common.role_info')}</h3>
                      {/* - 当token数<2000时，【展示】token数：<2000
                      - 当token数>2000时，【展示】token数：展示具体的数字 */}
                      <div className='px-3 py-2 bg-white dark:bg-gray-900 rounded'>
                        {!isGroup && <div className='text-gray-400 mb-1.5'>
                          <span className='mr-1'>Tokens:</span> <span className='text-gray-600 dark:text-gray-100'>{tokens < 2000 ? '<2000' : tokens}</span>
                        </div>}
                        {!card?.private_card && !tgWeb && <div className='text-gray-400 mb-1.5'>
                          <span className='mr-1'>{t('app.index.author')}: </span> {card?.author_id === 0 ? <span className='text-gray-600 dark:text-gray-100'>{card?.author_name}</span> : <Link href={`/user/${card?.author_id}/card`} className='inline-block text-blue-500 underline hover:underline'>
                          {card?.author_name}
                        </Link>}</div>}
                        {!isGroup && <div className='text-gray-400 mb-1.5'>
                          <span className='mr-1'>{t('app.mine.play_type')}: </span>
                          <span className='text-gray-600 dark:text-gray-100'>
                            {card?.play_type === 'RIVALRY' && t('app.cardEdit.opponent_content')}
                            {card?.play_type === 'PLOT_INFERENCE' && t('app.cardEdit.push_content')}
                            {card?.play_type === 'SYSTEM' && t('app.cardEdit.sys_tool')}
                          </span>
                          <Illustrate className='ml-1' title={t('app.cardEdit.play_type_title')} desc={t('app.cardEdit.play_type_desc')} isMd={true}></Illustrate>
                        </div>}
                        {!isGroup && <div className='text-gray-400 flex'>
                          <span className='mr-1'>{t('app.mine.chat_mode')}: </span>
                          <div className='flex-1'>
                          {productIds.map((item: any, index: number) => {
                            return <span className='text-gray-600 dark:text-gray-100' key={item.mid}>{item.model_name}{index < productIds.length - 1 ? '、' : ''}</span>
                          })}
                          </div>
                        </div>}
                      </div>
                      <button type='button' className={cn(s.priBtn, '!hidden md:!block !rounded-full bg-purple-500 !mx-auto py-2  text-white w-full mt-3')} onClick={() => { onStartChat(card.id) }}>{t('app.chat.start_chat')}</button>
                      <h3 className='text-sm mt-3 my-1 text-gray-400'>{t('app.common.role_introduction')}</h3>
                      <div className='px-3 py-2 bg-white dark:bg-gray-900 rounded'>
                        <div className='prose dark:prose-invert'><Markdown content={card?.introduction || ''} /></div>
                      </div>
                    </div>
                  }
                  {!isGroup && !card?.private_card && !tgWeb && <>
                    <h3 className='text-sm mt-3 my-1 text-gray-400'>{t('app.chat.square')}</h3>
                    <Chatlist url={`/share/list/role?role_id=${roleId}`} />
                  </>}
                </div>
              </div>
              <div className='md:hidden fixed bottom-0 left-0 right-0 w-full flex justify-between py-2 bg-white dark:bg-[var(--background)] dark:border-t dark:border-[#2c2e2d] pb-[calc(env(safe-area-inset-bottom)_+_8px)]'>
                  <button type='button' className={cn(s.priBtn, '!rounded-full block bg-purple-500 !mx-auto py-2 w-[calc(100%_-_24px)] xl:w-[1280px] mx-auto text-white')} onClick={() => { onStartChat(card.id) }}>{t('app.chat.start_chat')}</button>
                </div>
              </>
          }
        </>
        }
        {error && !isLoading && <LoadingToastFailed retry={retry} error={error} />}
      </main>
    </>
  )
}

const RolePage = () => {
  return (
    <Suspense fallback={<Loader />}>
      <Main />
    </Suspense>
  )
}

export default RolePage
