import cn from 'classnames'
import { Co<PERSON>, Heart, ThumbsDown } from 'lucide-react';
import { GiftIcon } from '@heroicons/react/24/solid'
import LoadImage from '@little-tavern/shared/src/ui/LoadImage'
import { ArrowPathIcon } from '@heroicons/react/24/solid'
import useComfirm from '@share/src/hook/useComfirm';
import useRequest from '@share/src/hook/useRequest';
import Toast from '@share/src/ui/toast';
import { useState } from 'react';

const Photo = ({ chat, isChatSampleStyle, isBg, bgColor, resolvedTheme, t, s, setChatList, chatList, isImgRetry, chatInfo, setChatInfo }: any) => {

  const comfirm = useComfirm();
  const request = useRequest();
  const imgs = [{ photo_id: chat?.photo_id, photo_url: chat.photo_url, loadingImg: chat.loadingImg }, ...(chat?.retry_photos || [])]
  const like_status = chatInfo.photos_likes_map?.[imgs[imgs.length - 1].photo_id] || 0;
  // console.log('imgs', imgs, like_status, chat.photo_id, chatInfo)
  const [retrying, setRetrying] = useState(false)

  const retryImg = async (img: any) => {
    // 避免重复点击扣费
    if(retrying) {
      Toast.notify({
        type: 'info',
        message: t('app.chat.wating_desc')
      })
      return;
    }
    const showChatImgTip = localStorage.getItem('retryChatImgTip');
    if (showChatImgTip != '1') {
      const res = await comfirm.show({
        title: t('app.chat.img_title1'),
        desc: t('app.chat.img_desc1'),
        comfirmBtn: t('app.chat.img_confirm_btn'),
        showCancelBtn: true,
        icon: <GiftIcon className="h-6 w-6 text-red-400" />,
        cancelBtn: t('app.chat.img_cancel_btn'),
        isShowIgnore: true
      })
      if (res?.ignore) {
        localStorage.setItem('retryChatImgTip', '1')
      }
      if (!res?.confirm) {
        return;
      }
    }
    const idx = chatList.length - 1;
    // console.log('retryImg', idx);
    setChatList((draft: any) => {
      const img = {
        loadingImg: true
      };
      if(draft[idx].retry_photos) {
        draft[idx].retry_photos.push(img)
      } else {
        draft[idx].retry_photos = [img]
      }
      // 触发ai组件刷新
      draft[idx].loadingImg = undefined;
    })
    setRetrying(true)
    try {
      const res = await request(`/take_photo/retry`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message_id: chat.message_id,
          version: chat.version,
          photo_id: img.photo_id
        })
      })
      setChatList((draft: any) => {
        const retryImgsLen = draft[idx].retry_photos.length;
        draft[idx].retry_photos[retryImgsLen - 1] = {
          photo_id: res.photo_id,
          photo_url: res.photo_url,
          loadingImg: false
        };
        // 刷新组件
        draft[idx].loadingImg = draft[idx].loadingImg === false? undefined : false;
      })
    } catch (e: any) {
      // 其他网络情况处理
      if (e.status != 402) {
        Toast.notify({
          type: 'error',
          message: t('app.chat.content_err', { status: e.status || e.name, errMsg: e.statusText || e.message })
        })
      }
      setChatList((draft: any) => {
        draft[idx].retry_photos.pop()
        draft[idx].loadingImg = false;
      })
    } finally {
      setRetrying(false)
    }
  }

  const likeHandler = async (img: any, like: boolean) => {
    Toast.showLoading('');
    try {
      const res = await request(`/photo/like_status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          role_id: chatInfo.role_id,
          conversation_id: chatInfo.conversation_id,
          message_id: chat.message_id,
          version: chat.version,
          photo_id: img.photo_id,
          like_status: like ? 1 : 2
        })
      })
      const idx = chatList.length - 1;
      setChatInfo((draft: any) => {
        draft.photos_likes_map = {
          ...draft.photos_likes_map,
          [img.photo_id]: like ? 1 : 2
        }
      })
      setChatList((draft: any) => {
        // 刷新组件
        draft[idx].loadingImg = draft[idx].loadingImg === false? undefined : false;
      })
    } catch (e: any) {
      // 其他网络情况处理
      if (e.status != 402) {
        Toast.notify({
          type: 'error',
          message: t('app.chat.content_err', { status: e.status || e.name, errMsg: e.statusText || e.message })
        })
      }
    } finally {
      Toast.hideLoading()
    }
  }

  return <>
    {imgs.map((img: any, index: number) => {
      return <>{(img?.photo_url || img?.loadingImg === true) && !isChatSampleStyle && <div className={cn('relative my-4 max-w-[95%] sm:max-w-[86%] items-start pl-16', isChatSampleStyle && '!mt-0.5')}>
        <div className={cn(s.shadow, 'w-full sm:mr-5 dark:bg-gray-900 bg-white rounded-lg ml-1', !isChatSampleStyle && 'p-0.5')} style={isBg ? {
          backgroundColor: `${bgColor}`
        } : {}}>
          <div className={cn(img?.loadingImg && s.loader, resolvedTheme === 'dark' && s.loaderDark)}>
            {img.photo_url && !img?.loadingImg && <LoadImage className='rounded w-full object-cover' src={img.photo_url} width={400} height={600} alt={''} unoptimized={true} />}
          </div>
        </div>
        {/* photo_id 是这次新增加的字段。 所以数据库里已有的私照是没有该字段，前端需要 根据 历史聊天记录返回值的图片是否有 photo_id 字段，来判断该图片是否展示 赞踩功能： 如果图片没有带photo_id ，就不要展示赞踩功能 */}
        {img.photo_url && index === imgs.length - 1 && isImgRetry && <div className='flex items-center mt-1 justify-end space-x-2'>
          {img.photo_id && <><button onClick={() => {likeHandler(img, true)}} className={cn('rounded-full bg-gray-800 py-1.5 px-2.5 mr-1 flex', like_status === 1 && 'text-red-400')}>
            <Heart className={cn('w-4 h-4 mr-0.5')} />
          </button>
          <button onClick={() => {likeHandler(img, false)}} className={cn('rounded-full bg-gray-800 py-1.5 px-2.5 mr-1 flex', like_status === 2 && 'text-red-400')}>
            <ThumbsDown className={cn('w-4 h-4 mr-0.5')} />
          </button></>}
          <button onClick={() => retryImg(img)} className='rounded-lg bg-gray-800 py-1 px-2.5 mr-1 flex items-center'>
            <ArrowPathIcon className="h-4 w-4 text-blue-400 mr-0.5" />
            {t('app.chat.retry')}
          </button>
        </div>}
      </div>}</>
    })}
  </>
}

export default Photo