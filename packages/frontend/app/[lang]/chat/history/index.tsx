'use client'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import s from '../style.module.css'
import cn from 'classnames'
import Image from 'next/image'
import AudioPlay, {stopAudio} from '@little-tavern/shared/src/ui/AudioPlay';
import Loader from '@little-tavern/shared/src/ui/Loading';
import { useTranslation } from 'react-i18next'
import { useParams } from 'next/navigation'
import Toast from '@little-tavern/shared/src/ui/toast'
import format from '@/app/module/formate-date';
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import { Markdown } from '../../components/markdown';
import { AuthContext } from '@little-tavern/shared/src/authContext';
import ExpandableContent from '@little-tavern/shared/src/ExpandableContent'
import Link from 'next/link';
import '../style.scss'
import { useTheme } from 'next-themes'
import {ArrowUturnLeftIcon, CheckCircleIcon} from '@heroicons/react/24/solid'
import { useRouter } from 'next/navigation';
import UserChat from '../userChat';
import AIChat from '../AIChat';

export type IChat = {
  id?: string,
  type: 'human' | 'ai' | 'img',
  avatar?: string,
  message_id?: string,
  version?: string,
  // 跟message_id一样，缓存判断是否重复请求
  imgId?: string,
  voice_url?: string,
  content: string,
  timestamp: number | null,
  isLoading?: boolean
}
let controller: any;
const Chat = () => {
  const { t } = useTranslation()
  const params = useParams()
  const lang = params.lang as string
  const { theme } = useTheme()
  const [loadHistory, setLoadHistory] = useState(true)
  const [conversationId, setConversationId] = useState('')
  const elementRef = useRef<HTMLDivElement>(null)
  const [chatList, setChatList] = useState<IChat[]>([]);
  const request = useRequest();
  // 默认聊第一个角色
  const [avatar, setAvatar] = useState('');
  const [roleName, setRoleName] = useState('');
  const [intro, setIntro] = useState('');
  const [privateCard, setPrivateCard] = useState(false);
  const [scenario, setScenario] = useState('');
  const router = useRouter();
  const [groupId, setGroupId] = useState<number>(0);
  const auth = useContext(AuthContext);
  const isLogin = auth?.isLogin;
  const uId = auth?.user?.id || '';
  const user = auth?.user
  const [chatInfo, setChatInfo] = useState<any>({})

  const fetchHistory = async (roleId = -1, conversationId='', newStart=1, groupId=0, modeType='single') => {
    setLoadHistory(true)
    const data = await request(`/chat/history?role_id=${roleId}&conversation_id=${conversationId}&new_start=${newStart}&group_id=${groupId}&mode_type=${modeType}`);
    setAvatar(data.role_avatar);
    setRoleName(data.role_name);
    setIntro(data.introduction);
    setScenario(data.scenario);
    setChatList(data.chat_list)
    setPrivateCard(data.private_card)
    setLoadHistory(false)
    setChatInfo(data)
    // 记录用户最新聊天的roleid
    const currentChatInfo = JSON.parse(localStorage.getItem('currentChatInfo') || '{}')
    if(!currentChatInfo[uId]) {
      currentChatInfo[uId] = {};
    }
    currentChatInfo[uId][roleId] = {
      conversationId: data.conversation_id
    }
    currentChatInfo[uId].curId = roleId;
    localStorage.setItem('currentChatInfo', JSON.stringify(currentChatInfo));
  };
  useEffect(() => {
    return () => {
      stopAudio();
    }
  }, [])
  useEffect(() => {
    if(!uId) return;
    const searchParam = new URLSearchParams(location.search);
    const queryRoleId = JSON.parse(searchParam.get('roleid') || '0');
    const currentChatInfo = JSON.parse(localStorage.getItem('currentChatInfo') || '{}')
    const queryGroupId = parseInt(searchParam.get('groupid') || '0');
    const conversationId = searchParam.get('conversation_id') || ''
    const modeType = searchParam.get('mode_type') || ''
    let chatInfo;
    if(currentChatInfo[uId]) {
      chatInfo = currentChatInfo[uId];
    }
    fetchHistory(queryRoleId, conversationId, 0, queryGroupId, modeType);
  }, [isLogin, uId])
  const back = () => {
    router.back();
  }
  return (
    <div className='mt-[44px] h-[calc(100%_-_44px_-_env(safe-area-inset-bottom))] overflow-auto'>
      <nav className='fixed dark:bg-[var(--background)] bg-white drop-shadow-u dark:drop-shadow-none z-10 w-full left-0 top-0 py-2'>
        <button className='absolute p-2 left-1 top-0.5 flex text-base items-center' onClick={back}>
            <ArrowUturnLeftIcon className='w-5 h-5 mr-1 text-purple-500' />
            {t('app.common.back')}
        </button>
        <div className='w-fit mx-auto text-sm h-7 flex items-center justify-center'>
            <div className='flex items-center flex-shrink-0 text-gray-500'>
              <div className="flex items-center text-sm">
                  {t('app.chat.history')}
              </div>
            </div>
        </div>
    </nav>
      {loadHistory? <Loader /> : <main className={cn(auth?.isAndroidTg? s.tgAndroidLayout : s.gridLayout, 'relative z-0')}><div className='pb-3 pt-2 overflow-auto text-sm leading-6' ref={elementRef}>
        <div className='mx-auto'>
          <div className='my-4 mt-1 flex items-start max-w-[95%] sm:max-w-[87%] mx-auto'>
            <ExpandableContent maxHeight={200}>
              <div className={cn(s.shadow, 'text-sm dark:bg-gray-900 bg-white p-3 pb-1 rounded-lg')}>
                  {/* <h2 className='dark:text-gray-200 text-gray-700'>{t('app.chat.role_bg')}</h2>
                  <div className={cn('prose prose-invert text-gray-500')}><Markdown content={intro} /></div> */}
                  <h2 className='dark:text-gray-200 text-gray-700'>{t('app.chat.init_scenario')}</h2>
                  <div className={cn('prose prose-invert text-gray-500')}><Markdown content={scenario || t('app.chat.empty')} /></div>
                  {privateCard && <div data-before={t('app.chat.person_card')} data-after={t('app.chat.person_limit')} className={cn(s.watermarked, `before:!content-[attr(data-before)] after:!content-[attr(data-after)]`, "w-full h-full absolute top-0 left-0")}></div>}
                </div>
              </ExpandableContent>
          </div>
         {
            chatList.map((chat, index) => {
              if (chat.type == 'human') {
                return <UserChat key={chat.message_id || index} user={user} chat={chat} isShowRevocation={false}  />
              } else {
                return <AIChat key={chat.message_id || index} chat={chat} user={user} chatInfo={chatInfo} chatList={chatList} index={index}  />
              }
            })
          }
        </div>
      </div>
    </main>}
    </div>
  )
}

export default Chat
