import type { FC } from 'react'
import ExpandableContent from '@little-tavern/shared/src/ExpandableContent'
import cn from 'classnames'
import s from './style.module.css'
import Markdown from '../components/markdown';
import { useTranslation } from 'react-i18next';
import {memo} from 'react'

interface IIntro {
    intro: string
    scenario: string
    privateCard: boolean
    isBg: boolean
}
const Intro: FC<IIntro> = ({intro, scenario, privateCard, isBg}) => {
    const { t } = useTranslation()
    return <div className='my-4 mt-1 flex items-start max-w-[95%] sm:max-w-[87%] mx-auto'>
    <ExpandableContent maxHeight={200}>
      <div className={cn(s.shadow, 'text-sm dark:bg-gray-900 bg-white p-3 pb-1 rounded-lg', isBg && 'dark:!bg-gray-900/90 !bg-white/90')}>
        <h2 className='dark:text-gray-100 text-gray-700'>{t('app.chat.init_scenario')}</h2>
        <div className={cn('prose prose-invert dark:text-gray-400 text-gray-500')}><Markdown content={scenario || t('app.chat.empty')} /></div>
        {privateCard && <div data-before={t('app.chat.person_card')} data-after={t('app.chat.person_limit')} className={cn(s.watermarked, `before:!content-[attr(data-before)] after:!content-[attr(data-after)]`, "w-full h-full absolute top-0 left-0")}></div>}
      </div>
    </ExpandableContent>
  </div>
}

export default memo(Intro, (prevProps, nextProps) => {
  return prevProps.intro === nextProps.intro && prevProps.isBg === nextProps.isBg
})