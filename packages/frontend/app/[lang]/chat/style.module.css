
.loader {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
.loader:after {
  content:"";
  display: inline-block;
  width: 20px;
  margin-top: 5px;
  margin-bottom: 4px;
  aspect-ratio: 4;
  --_g: no-repeat radial-gradient(circle closest-side,#6f6f6f 90%,#ffffff00);
  background: 
    var(--_g) 0%   50%,
    var(--_g) 50%  50%,
    var(--_g) 100% 50%;
  background-size: calc(100%/3) 100%;
  animation: b2 1s infinite linear;
}
.loaderDark:after {
  --_g: no-repeat radial-gradient(circle closest-side,#fff 90%,#0000);
}
@keyframes b2 {
  33%{background-size:calc(100%/3) 0%  ,calc(100%/3) 100%,calc(100%/3) 100%}
  50%{background-size:calc(100%/3) 100%,calc(100%/3) 0%  ,calc(100%/3) 100%}
  66%{background-size:calc(100%/3) 100%,calc(100%/3) 100%,calc(100%/3) 0%  }
}

.gridLayout {
  display: grid;
  grid-template-rows: auto min-content;
  height: calc(100vh - 44px);
  height: calc(100svh - 44px);
}
.tgAndroidLayout {
  padding-bottom: 60px;
}
/* .choices :where(q):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: rgb(156 163 175) !important;
} */
.choices :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0 !important;
}

/* .mask-transparent{
  mask-image: linear-gradient(0deg,transparent,rgba(0,0,0,.013) .81%,rgba(0,0,0,.049) 1.55%,rgba(0,0,0,.104) 2.25%,rgba(0,0,0,.175) 2.9%,rgba(0,0,0,.259) 3.53%,rgba(0,0,0,.352) 4.12%,rgba(0,0,0,.45) 4.71%,rgba(0,0,0,.55) 5.29%,rgba(0,0,0,.648) 5.88%,rgba(0,0,0,.741) 6.47%,rgba(0,0,0,.825) 7.1%,rgba(0,0,0,.896) 7.75%,rgba(0,0,0,.951) 8.45%,rgba(0,0,0,.987) 9.25%,#000 10%)
} */


.watermarked::before {
  content: "";
  font-size: 1.2rem;
  color: rgba(128, 128, 128, 0.25);
  position: absolute;
  top: 20%;
  left: 35%;
  width: 140px;
  height: 28px;
  overflow: hidden;
  display: block;
  white-space: nowrap;
  z-index: 0;
  transform: rotate(-45deg);
  pointer-events: none;
}
.watermarked::after {
  content: "";
  font-size: 1.2rem;
  color: rgba(128, 128, 128, 0.25);
  position: absolute;
  top: 35%;
  left: 40%;
  width: 140px;
  height: 28px;
  overflow: hidden;
  display: block;
  white-space: nowrap;
  z-index: 0;
  transform: rotate(-45deg);
  pointer-events: none;
}
.lineClamp3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.lineClamp2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
