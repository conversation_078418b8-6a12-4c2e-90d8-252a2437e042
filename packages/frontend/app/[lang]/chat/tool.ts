
// 文本内抽取推荐回复
// 模版
// {recAnswer.length !== 0 && <div className='ml-16 pl-1 text-gray-500 max-w-[80%]'>
//             <h2 className='text-gray-400 text-xs'>试试这样回复：</h2>
//             <ul className='mt-2 text-sm'>
//               {recAnswer.map((item: any) => {
//                 return <li key={item}>
//                   <button onClick={() => {handleSend(item)}} className={cn(s.choices, 'text-left cursor-pointer text-gray-400 border border-gray-500 bg-gray-900 rounded-lg inline-block p-1 px-3 mb-2 prose prose-invert')}><Markdown content={item} /></button>
//                 </li>
//               })}
//             </ul>

import { IChat } from "./type";

//           </div>}
const extractChoices = (text: string, character: string, setRecAnswer: any, t: any) => {
  const regex = /<ca>([\s\S]*?)<\/ca>|<ca>([\s\S]*)/g;
  const choicesText = text.match(regex)?.[0] || '';
  // todo: ${character}存在风险，如果变量带有括号之类，会报错
  const choicesRegex = new RegExp(`\\d+\\.\\s*${character}[:：]\\s*(.+)`, 'g');
  const choices = [];
  let match;
  while ((match = choicesRegex.exec(choicesText)) !== null) {
    const val = match[1].trim();
    process.env.NEXT_PUBLIC_RM_CONSOLE != "true" && console.log('val-推荐回复，移除用户名：\n', val);
    choices.push(val);
  }
  let res = text.replace(regex, '');
  process.env.NEXT_PUBLIC_RM_CONSOLE != "true" && console.log('res-删除推荐回复后的内容', res);
  // 只有空白符（包括换行、回车等）的情况
  const rg = /^\s*$/;
  if (rg.test(res)) {
    res = t('app.chat.content_err');
    setRecAnswer([]);
  } else {
    setRecAnswer(choices);
  }

  return res
};

// 兼容android和ios自动滚动到底部
const autoScrollBottom = (auth: any, elementRef: any, autoScrollRef: any) => {
  const container = auth?.isAndroidTg ? document.documentElement : elementRef.current;
  // const container = elementRef.current;
  if (autoScrollRef.current && container && container.scrollHeight > container.clientHeight) {
    container.scrollTop = container.scrollHeight;
  }
}

export enum LoadStatus {
  Loading = 'loading',
  Success = 'success',
  Error = 'error'
}
const initUserChatTmp = (message: string) => {
  return {
    type: 'human',
    message_id: '',
    content: message,
    loadStatus: LoadStatus.Loading,
    timestamp: Date.now() / 1000
  }
}

const initAIChatTmp = (aiRoleId?: number): IChat => {
  return {
    id: '',
    type: 'ai',
    avatar: '',
    message_id: '',
    content: '',
    timestamp: null,
    isLoading: true,
    role_id: aiRoleId,
    isRecieving: true
  }
}

const updateChatRes = (obj: IChat) => {
  obj.timestamp = Date.now() / 1000;
  obj.isRecieving = false;
  obj.isLoading = false;
  obj.isContinueLoading = false;
  return obj
}

export {
  extractChoices,
  autoScrollBottom,
  initUserChatTmp,
  initAIChatTmp,
  updateChatRes
}