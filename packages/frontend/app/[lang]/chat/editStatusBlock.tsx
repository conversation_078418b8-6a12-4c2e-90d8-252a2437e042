"use client";

import React, { useEffect, useState } from 'react';
import type { FC } from 'react'
import Modal from '@share/src/ui/dialog/Modal';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { CheckIcon } from '@heroicons/react/24/outline'
import cn from 'classnames';
import ReactMarkdown from 'react-markdown'
import Illustrate from '@share/src/ui/tooltip/illustrate'
import { useTranslation } from 'react-i18next'
import AutoTextareaHeight from '@share/src/ui/dialog/CreateCard/AutoTextareaHeight'
import useRequest from '@share/src/hook/useRequest';
import useComfirm from '@share/src/hook/useComfirm';
import { LightBulbIcon } from '@heroicons/react/24/solid'
import useTokenCounter from '@share/src/hook/useTokenCounter'
import Toast from '@share/src/ui/toast';
import useDialogAlert from '@share/src/hook/useDialogAlert';

type IProps = {
    onClose: () => void,
    isOpen: boolean,
    statusInfo?: any,
    chatInfo?: any,
    chat?: any
    setChatList: (chatList: any) => void
}
const EditStatusBlock: FC<IProps> = ({ onClose, isOpen, statusInfo, chatInfo, chat, setChatList }) => {
    const { t } = useTranslation()
    const [tplVal, setTplVal] = useState('')
    const defaultTpl = statusInfo.user_sbt_detail || {}
    const [tpl, setTpl] = useState(defaultTpl || {})
    const [isShowContent, setIsShowContent] = useState(defaultTpl?.content ? true : false)
    const request = useRequest();
    const comfirm = useComfirm();
    const [contentTokens, setContentTokens] = useState<number>(0);
    const [ruleTokens, setRuleTokens] = useState<number>(0);
    const tpls = statusInfo?.templates || []
    const [showAIGenerate, setShowAIGenerate] = useState(false)
    const [isAIGenerate, setIsAIGenerate] = useState(false)
    const dialogAlert = useDialogAlert();
    // console.log('statusInfo', tpl);
    const handlerSwitchTpl = (e: any) => {
        const title = e.target.value;
        setTplVal(title)
        const tip = tpls.filter((tpl: any) => {
            return tpl.title === title
        });
        if(tip.length > 0) {
            setTpl(tip?.[0])
            setShowAIGenerate(true)
            setIsShowContent(false)
        }
    }
    const aiGenerate = async (e: any) => {
        const isShowAIGenerate = localStorage.getItem('isShowAIGenerate')
        if (!isShowAIGenerate) {
            const res = await comfirm.show({
                title: t('app.common.tips'),
                desc: t('app.common.ai_generate_tips'),
                icon: <LightBulbIcon className='w-6 h-6 text-yellow-600' />,
                isShowIgnore: true
            })
            if (res?.ignore) {
                localStorage.setItem('isShowAIGenerate', '1')
            }
            if (!res?.confirm) {
                return;
            }
        }
        Toast.showLoading(t('app.common.ai_generate'), true)
        const generateRes = await request(`/user/chat/status_block/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "sbt_id": tpl?.id,
                "conversation_id": chatInfo?.conversation_id,
                "message_id": chat?.message_id,
                "version": chat?.version
            })
        })
        if (generateRes?.error_code === 0) {
            setTpl({
                ...tpl,
                content: generateRes?.data.content
            })
            setIsShowContent(true)
            setIsAIGenerate(true)
        } else {
            Toast.notify({
                type: 'error',
                message: generateRes?.message
            })
        }
        Toast.hideLoading()
    }
    
    const { debouncedCountTokens } = useTokenCounter();
    const onChange = (e: any) => {
        setIsShowContent(true)
        setIsAIGenerate(false)
        setTpl({
            ...tpl,
            content: e.target.value
        })
    }
    const onChangeRule = (e: any) => {
        setIsAIGenerate(false)
        setTpl({
            ...tpl,
            rule: e.target.value
        })
    }
    useEffect(() => {
        debouncedCountTokens(tpl?.content, (tokenCount: number) => {
            setContentTokens(tokenCount)
        });
    }, [tpl?.content, debouncedCountTokens]);
    // useEffect(() => {
    //     debouncedCountTokens(tpl?.rule, (tokenCount: number) => {
    //         setRuleTokens(tokenCount)
    //     });
    // }, [tpl?.rule, debouncedCountTokens]);
    const onSave = async (e: any) => {
        e.preventDefault()
        Toast.showLoading(t(''))
        try {
            const res = await request(`/user/chat/status_block/save`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    // 如果选择模版，用模版id，否则传0
                    "sbt_id": showAIGenerate? tpl?.id : 0,
                    "conversation_id": chatInfo?.conversation_id,
                    "message_id": chat?.message_id,
                    "version": chat?.version,
                    "content": tpl?.content,
                    "rule": tpl?.rule,
                    "ai_generate": isAIGenerate
                })
            })
            if(res?.error_code === 0) {
                Toast.notify({
                    type: 'success',
                    message: t('app.common.save_success')
                })
                setChatList((draft: any) => {
                    draft[draft.length - 1] = res.data.chat_message
                })
                onClose()
            } else {
                dialogAlert.show({
                    title: t('app.common.tips'),
                    desc: res?.message,
                    alertStatus: 2,
                    comfirmBtn: t('app.common.confirm1')
                })
            }
        } catch (error) {
            console.log('error', error);
        } finally {
            Toast.hideLoading()
        }
    }
    return <Modal onClose={() => {onClose()}} isOpen={isOpen} mainClassName='!px-1'>
        <div className='min-h-20 mb-2'>
            <form className='p-1' action="#" method="POST" onSubmit={onSave}>
                <div className='max-h-[calc(100vh-12rem)] overflow-y-scroll'>
                    <h1 className='text-base font-semibold leading-6'>{t('app.common.status_bar')} <Illustrate title={t('app.dialog.edit_status_block')} desc={t('app.dialog.edit_status_block_desc')} isMd={true}></Illustrate></h1>
                    <div className='text-sm mt-2 flex justify-between'>
                        <select
                            className='dark:bg-white bg-gray-100 text-black p-0.5'
                            value={tplVal}
                            onChange={handlerSwitchTpl}
                        >
                            <option value={''}>{t('app.common.select_template')}</option>
                            {tpls.map((tpl: any, index: number) => {
                                return <option key={index} value={tpl.statusSample}>{tpl.title}</option>
                            })}
                        </select>
                        {showAIGenerate && <button className='text-blue-500' type='button' onClick={aiGenerate}>{t('app.common.generate_status_bar')}</button>}
                    </div>
                    <div className='mb-2'>
                        <div className='mt-2 flex items-center'>
                            <h2>{t('app.common.tpl')}</h2>
                            {contentTokens > 0 && <span className='ml-1'>tokens {contentTokens}</span>}
                        </div>
                        <AutoTextareaHeight required oninvalid="this.setCustomValidity('请输入用户名')"
         oninput="this.setCustomValidity('')" className={'dark:bg-gray-800 dark:ring-gray-500 !mt-0'} placeholder={t('app.dialog.status_block_placeholder')} value={isShowContent ? tpl?.content : tpl?.content_placeholder} onChange={onChange} onFocus={() => setIsShowContent(true)} />
                        <div className='mt-2 flex items-center'>
                            <h2>{t('app.common.update_rule')}</h2>
                        </div>
                        <AutoTextareaHeight required className={'dark:bg-gray-800 dark:ring-gray-500 !mt-0'} placeholder={t('app.dialog.status_block_placeholder')} value={tpl?.rule} onChange={onChangeRule} />
                    </div>
                </div>
                <div className='px-2 py-2 flex flex-row-reverse'>
                    <button className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3' type='submit'>{t('app.common.save')}</button>
                    {<button className='p-2 px-5 dark:bg-gray-800 bg-gray-300 rounded text-sm ' onClick={onClose}>{t('app.common.cancel')}</button>}
                </div>
            </form>
        </div>
    </Modal>
}

export default EditStatusBlock;