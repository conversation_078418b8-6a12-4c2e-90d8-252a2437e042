import type { FC } from 'react'
import cn from 'classnames'
import { useTranslation } from 'react-i18next';
import { User } from '@little-tavern/shared/src/authContext'
import Image from 'next/image'
import { IChat } from './type'
import { memo } from 'react'
import { LoaderCircle, CircleAlert, Copy } from 'lucide-react';
import { LoadStatus } from './tool';
import useDialogAlert from '@share/src/hook/useDialogAlert';
import s from './style.module.css'
import { ArrowPathIcon, ArrowUturnDownIcon } from '@heroicons/react/24/solid'
import Toast from '@share/src/ui/toast';
import ModeChangeHistory from '../components/chat-input/modeChangeHistory';
interface IUserChat {
  chat: IChat
  user: User | null | undefined
  isShowRevocation?: boolean
  revocation?: Function
  // 分享聊天样式
  isChatSampleStyle?: boolean
  retry?: Function
  isBg?: boolean
  opacity?: number
  modelEventMap?: any
  modeType?: string
}
const UserChat: FC<IUserChat> = ({ chat, user, isShowRevocation, revocation, isChatSampleStyle, retry, isBg, opacity, modelEventMap, modeType }) => {
  const { t } = useTranslation()
  const dialogAlert = useDialogAlert();
  const netErrHandler = async () => {
    const res = await dialogAlert.show({
      title: t('app.chat.net_err1'),
      desc: chat?.errMsg || t('app.chat.net_err_desc'),
      alertStatus: '2',
    })
  }
  const copy = async (content: string) => {
    navigator.clipboard.writeText(content).then(
      function () {
        Toast.notify({
          type: 'success',
          message: t('app.chat.copy_success')
        })
      },
      function () {
        Toast.notify({
          type: 'error',
          message: t('app.chat.copy_fail')
        })
      },
    );
  }
  const bgColor = `rgb(118 106 200 / ${opacity ? opacity : 90}%)`;
  // console.log('bgColor', bgColor);
  return <><div className={cn('my-4 flex items-start flex-row-reverse', isChatSampleStyle && '!my-3')}>
    <div className={cn('w-16 text-center', isChatSampleStyle && '!w-14')}>
      {user?.avatar ? <Image className={cn('rounded-full h-12 w-12 ml-2 mr-3 object-cover', isChatSampleStyle && '!rounded-full !h-10 !w-10')} src={user?.avatar} width={48} height={48} alt={user?.nickname} unoptimized={true} /> : <div className={cn('rounded-full inline-flex justify-center items-center h-12 w-12 object-cover border border-gray-300 dark:border-gray-200 text-center text-gray-500 dark:text-gray-100', isChatSampleStyle && '!rounded-full !h-10 !w-10 text-xs', isBg && 'bg-white/85 dark:bg-transparent')}>User</div>}
      <h2 className='w-full px-1 text-center text-xs mt-1 dark:text-gray-300 text-gray-500 truncate'>{user?.nickname}</h2>
    </div>

    <div className={cn('relative p-3 py-2 rounded-lg inline-block bg-[#766ac8] sm:max-w-[65%] max-w-[75%] text-white', isChatSampleStyle && 'sm:!max-w-[82%] !max-w-[82%] dark:!bg-[#2b2d4f]')} style={isBg ? {
        backgroundColor: `${bgColor}`
      } : {}}>
      {chat.loadStatus === LoadStatus.Loading && <button type='button' className='absolute -left-5 top-0'>
        <LoaderCircle className='animate-spin w-4 h-4 dark:text-gray-200 text-purple-400' />
      </button>}
      {chat.loadStatus === LoadStatus.Error && <button type='button' onClick={netErrHandler} className='absolute -left-5 -top-0.5 p-0.5'>
        <CircleAlert className='w-4 h-4 text-red-500' />
      </button>}
      <p className={cn('whitespace-pre-wrap break-all text-[15px]', isChatSampleStyle && '!text-sm !text-gray-200', isChatSampleStyle && s.lineClamp2)}>{chat.content}</p>

      {!isChatSampleStyle && <div className='flex mt-3 justify-end text-xs text-white'>
        <button onClick={() => {
          copy(chat.content)
        }} className='hover:text-white flex items-center'>
          <Copy className="h-3.5 w-3.5 mr-1" />
          {t('app.chat.copy')}
        </button>
        {isShowRevocation && revocation && chat.loadStatus !== LoadStatus.Loading && <>
          <button onClick={() => {
            revocation(chat.message_id)
          }} className='ml-2 hover:text-white flex items-center'>
            <ArrowUturnDownIcon className="h-3.5 w-3.5 mr-1" />
            {t('app.chat.recall')}
          </button>
          {retry && modeType !== 'group' && <button onClick={() => { retry({ message: chat.content, msgId: chat.message_id }) }} className='ml-2 dark:hover:text-white flex'>
            <ArrowPathIcon className="h-3.5 w-3.5 text-white mr-0.5" />
            {t('app.chat.retry')}
          </button>}
        </>}
      </div>}
    </div>
  </div>
  <ModeChangeHistory msgId={chat.message_id} modelEventMap={modelEventMap} />
  </>
}

export default memo(UserChat, (prevProps, nextProps) => {
  return prevProps.isShowRevocation === nextProps.isShowRevocation && !prevProps.isShowRevocation && prevProps.isBg === nextProps.isBg && prevProps.opacity === nextProps.opacity && prevProps.user?.avatar === nextProps.user?.avatar && prevProps.user?.nickname === nextProps.user?.nickname
})