
const tpls = [
    {
        title: '日常',
        content: `姓名: {{char}}
场景：[当前场景描述]
表情: [当前表情]
当前姿势: [详细描述当前姿势]
外貌服装: [从上到下详细描述外貌和服装]
心理活动: [描述当前心理状态]
当下情况: [描述当前正在发生的事情]
高潮次数: [数字]`,
        rule: `1.姓名：保持不变，始终为{{char}}。
2.场景：根据故事情节的发展，更新当前所处的具体位置和环境描述。
3.表情：根据角色当前的情绪和遭遇，更新面部表情的描述。
4.当前姿势：详细描述角色此刻的身体姿势，包括四肢、躯干的具体位置和状态。
5.外貌服装：从头到脚详细描述角色的外貌特征和穿着的服装。如果服装状态发生变化（如破损、脱落等），需要更新相应描述。
6.心理活动：根据角色经历的事件和当前处境，更新其内心想法和情感状态的描述。
7.当下情况：描述此刻正在发生的事件或行为，包括角色自身的行动和周围环境的变化。
8.高潮次数：记录角色经历性高潮的次数，每次发生性高潮时数字加1。`
    },
    {
        title: '色色',
        content: `{{char}}
心情：｛心情｝
性欲值：｛性欲值｝
外貌服装：｛外貌服装｝
人物状态：｛人物状态｝
奶子状态：｛奶子状态｝
阴蒂状态：｛阴蒂状态｝
阴道状态：｛阴道状态｝
屁眼状态：｛屁眼状态｝`,
        rule: `心情：根据{{char}}当前经历更新，可包括恐惧、绝望、麻木等。
性欲值：用数值或描述表示当前性欲程度，随性行为或刺激变化。
外貌服装：描述外貌和衣着，包括可能的破损、伤痕等。
人物状态：概述整体身心状况，如疲劳度、意识状态等。
奶子状态：描述胸部现状，包括可能的伤痕、红肿等。
阴蒂状态：描述阴蒂现状，如敏感度变化等。
阴道状态：描述阴道现状，如红肿、分泌物等。
屁眼状态：描述肛门现状，如扩张程度等。`
    },
    {
        title: '调教',
        content: `{{char}}的状态：
羞辱程度：[0-100%]
脚的气味：[清新 - 恶臭]
语言污秽度：[温和 - 极度下流]
虐待欲望：[低 - 高]
{{user}}的反应：[平静 - 崩溃]
隐藏状态：
[{{char}}的隐藏兴趣]
[{{user}}的隐藏癖好]
场景描述：
[详细的环境描述]`,
        rule: `羞辱程度：用0-100%表示，根据{{char}}经历的羞辱事件增减。
脚的气味：在"清新"到"恶臭"之间更新，根据{{char}}的活动和时间推移变化。
语言污秽度：在"温和"到"极度下流"之间更新，根据{{char}}的情绪和经历变化。
虐待欲望：在"低"到"高"之间更新，根据{{char}}的心理状态和经历变化。
{{user}}的反应：在"平静"到"崩溃"之间更新，根据{{user}}目睹或参与的事件变化。
{{char}}的隐藏兴趣：根据情节发展适时揭示或更新。
{{user}}的隐藏癖好：根据{{user}}的行为和选择适时揭示或更新。
场景描述：根据故事进展和位置变化，更新详细的环境描述。`
    },
    {
        title: '足控',
        content: `性欲：描述
当前地点：地点
玉足特写: {{当前角色脚上的外貌、气味、状态、袜子外貌、袜子气味、袜子状态的详细信息}}
身体状态: {{当前状态}}
服装: {{当前的服装，包括:饰品、衣服、裤子、袜子、鞋子、内衣、内裤}}`,
        rule: `性欲：根据角色经历和刺激程度，用描述性语言更新当前性欲状态。
当前地点：随角色移动更新具体位置。
玉足特写：当前角色脚上的外貌、气味、状态、袜子外貌、袜子气味、袜子状态的详细信息
身体状态：根据角色经历的事件和身体变化更新整体状态描述。
服装：当前的服装，包括:饰品、衣服、裤子、袜子、鞋子、内衣、内裤`
    },
]

export default tpls