'use client'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import cn from 'classnames'
import Image from 'next/image'
import Loader from '@little-tavern/shared/src/ui/Loading';
import { useTranslation } from 'react-i18next'
import { useParams } from 'next/navigation'
import Toast from '@little-tavern/shared/src/ui/toast'
import format from '@/app/module/formate-date';
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import { Markdown } from '../../../components/markdown';
import { AuthContext } from '@little-tavern/shared/src/authContext';
import '../../style.scss'
import s from '../../style.module.css'
import { useTheme } from 'next-themes'
import { useRouter } from 'next/navigation';
import UserChat from '../../userChat';
import AIChat from '../../AIChat';
import useLightBox from '@share/src/hook/useLightBox'
import useLinkToChat from '@/app/hook/useLinkToChat';
import Link from 'next/link';
import NavBottom from '@/app/[lang]/components/header/nav-bottom';
import MyNav from '@/app/[lang]/components/MyNav';

export type IChat = {
  id?: string,
  type: 'human' | 'ai' | 'img',
  avatar?: string,
  message_id?: string,
  version?: string,
  // 跟message_id一样，缓存判断是否重复请求
  imgId?: string,
  voice_url?: string,
  content: string,
  timestamp: number | null,
  isLoading?: boolean
}
const Chat = () => {
  const { t } = useTranslation()
  const params = useParams()
  const lang = params.lang as string
  const { theme } = useTheme()
  const shareId = params.id
  console.log('shareId', shareId);
  const [loading, setLoading] = useState(true)
  const elementRef = useRef<HTMLDivElement>(null)
  const request = useRequest();
  const router = useRouter();
  const auth = useContext(AuthContext);
  const [shareInfo, setShareInfo] = useState<any>({})
  const [shareUser, setShareUser] = useState<any>({})
  const lightBox = useLightBox();
  const linkToChat = useLinkToChat();
  const fetchHistory = async () => {
    setLoading(true)
    try {
      const res = await request(`/share?share_id=${shareId}`);
      if (res.error_code === 0) {
        setShareInfo(res?.data)
        setShareUser({
          avatar: res?.data?.share_avator,
          nickname: res?.data?.sharer_name
        })
      } else {
        Toast.notify({
          type: 'error',
          message: res.message
        })
      }
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: t('app.common.load_err')
      })
      Toast.notify({ type: 'error', message: t('app.common.load_err') });
    } finally {
      setLoading(false)
    }
  };
  useEffect(() => {
    fetchHistory();
  }, [])
  const startChat = () => {
    linkToChat.push({ isGroup: false, id: shareInfo?.role_id })
  }
  return (
    <>
      <MyNav showSwitchLang={false}>{shareInfo?.role_name}</MyNav>
      <main className={cn('relative z-0 mx-auto main-height')}>
        <div className='max-w-[1280px] my-3 px-1 md:px-3 mx-auto'>
          {loading ? <Loader /> : <>
            <div className='md:flex md:gap-3 md:mt-2'>
              <div className='md:w-[min(300px,30vw)]'>
                <div className='flex items-center px-2'>
                  <Image className='rounded dark:bg-gray-800 bg-gray-200 object-cover !mx-auto mb-2 w-[min(640px,100%)] h-[min(360px,50vh)] md:aspect-square md:h-auto' src={shareInfo?.role_avatar || '/dot.png'} width={600} height={800} quality={90} alt={shareInfo?.role_name || ''} onClick={() => {
                    lightBox.show({ src: shareInfo?.role_avatar });
                  }} />
                </div>
                <div className="mx-2 pb-2 pt-1 flex flex-wrap gap-6 justify-center sm:justify-normal">
                  <div className="rounded-md w-full p-3 dark:bg-gray-900 bg-white">
                    <div className='space-y-2'>
                      <h3 className=''>{shareInfo?.share_title}</h3>
                      {/* <div className={'text-gray-500'}>{shareInfo?.share_description}</div> */}
                    </div>
                  </div>
                </div>
                <div className={cn(s.shadow, 'mx-2 mb-2 text-sm dark:bg-gray-900 bg-white p-3 rounded-lg')}>
                  <div className={cn('text-gray-500')}>{shareInfo?.share_description}</div>
                  {/* <div className={cn('prose prose-invert text-gray-500')}><Markdown content={shareInfo?.share_description} /></div> */}
                </div>
                <div className="mx-2 pb-2 flex flex-wrap gap-6 justify-center sm:justify-normal">
                  <div className="rounded-md w-full p-3 dark:bg-gray-900 bg-white">
                    <div className='space-y-4 pb-1'>
                      <div className='flex items-center space-x-2'>
                        <div className='text-sm text-gray-500 dark:text-gray-400'>{t('app.share.sharer')}</div>
                        <Image className='rounded-full w-6 h-6 dark:bg-gray-800 bg-gray-200 object-cover mr-2' src={shareInfo?.sharer_avator || '/dot.png'} unoptimized={true} width={20} height={20} quality={90} alt={shareInfo?.sharer_name || ''} />
                        <h3><Link href={`/user/${shareInfo.sharer_uid}/share`} className='hover:underline'>{shareInfo?.sharer_name}</Link></h3>
                      </div>
                      <div className='flex items-center space-x-2'>
                        <div className='text-sm text-gray-500 dark:text-gray-400'>{t('app.index.author')}</div>
                        <Image className='rounded-full w-6 h-6 dark:bg-gray-800 bg-gray-200 object-cover mr-2' src={shareInfo?.author_avator || '/dot.png'} unoptimized={true} width={20} height={20} quality={90} alt={shareInfo?.sharer_name || ''} />
                        <h3>{shareInfo?.author_id === 0 ? shareInfo?.author_name : <Link href={`/user/${shareInfo.author_id}/card`} className='hover:underline'>{shareInfo?.author_name}</Link>}</h3>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className='md:flex-1'>
                
                <div className='pb-3 pt-2 text-sm leading-6' ref={elementRef}>
                  <div className='mx-auto'>
                    {
                      shareInfo?.chat_list?.map((chat: any, index: number) => {
                        if (chat.type == 'human') {
                          return <UserChat key={chat.message_id || index} user={shareUser} chat={chat} isShowRevocation={false} />
                        } else {
                          return <AIChat key={chat.message_id || index} chat={chat} user={null} chatInfo={shareInfo} chatList={shareInfo?.chat_list} index={index} />
                        }
                      })
                    }
                  </div>
                  <div className='text-center mt-4 pb-4'>
                    <button type='button' onClick={startChat} className='priBtn px-12 py-2'>
                      {t('app.chat.start_chat')}
                    </button>
                    <p className='text-xs text-gray-400 mt-1'>{t('app.share.new_chat')}</p>
                  </div>
                </div>
              </div>
            </div>
          </>}
        </div>
      </main>
      <NavBottom />
    </>
  )
}

export default Chat
