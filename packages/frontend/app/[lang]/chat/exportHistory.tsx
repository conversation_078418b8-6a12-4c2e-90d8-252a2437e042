import useDialogAlert from '@little-tavern/shared/src/hook/useDialogAlert';
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import Link from 'next/link';
import { useState, useContext } from 'react';
import { Trans, useTranslation } from 'react-i18next'
import { AuthContext } from '@little-tavern/shared/src/authContext';
import Toast from '@little-tavern/shared/src/ui/toast';
import {memo} from 'react'
import cn from 'classnames'
import { useTheme } from 'next-themes';

const ExportHistory = ({showExoprt, roleId, conversationId, groudId, modeType, isBg, opacity}: {showExoprt: string, roleId: number, conversationId: string, groudId: number, modeType: string, isBg: boolean, opacity?: number}) => {
    const request = useRequest();
    const { t } = useTranslation()
    const dialogAlert = useDialogAlert();
    const auth = useContext(AuthContext);
    const isTG = auth?.isTG;
    const exportHistory = async () => {
      Toast.showLoading('');
      try {
        const res = await request(`/history/export_tg?role_id=${roleId}&conversation_id=${conversationId}&groupid=${groudId}&mode_type=${modeType}`)
        Toast.hideLoading();
        if(res) {
          dialogAlert.show({
            title: t('app.chat.export_title'),
            desc: t('app.chat.export_desc')
          })
        }
      } catch(e) {
        console.log('res', e);
        Toast.notify({
          type: 'error',
          message: t('app.chat.del_failed')
        })
        Toast.hideLoading();
      }
    }
    const { resolvedTheme } = useTheme()
    const bgColor = resolvedTheme === 'dark' ? `rgb(17 24 39 / ${opacity ? opacity : 95}%)` : `rgba(255 255 255 / ${opacity ? opacity : 95}%)`;
    return <>{showExoprt? <div className={cn('bg-gray-300 dark:bg-gray-900 rounded py-1 max-w-[95%] sm:max-w-[87%] mx-auto text-center text-sm dark:text-gray-300 text-gray-700')} style={isBg? {
      backgroundColor: `${bgColor}`
    }: {}}>
        {showExoprt}
        <br />
        <Trans i18nKey="app.chat.view_all" t={t}>
        查看全部记录请<Link href={`/chat/history?roleid=${roleId}&groupid=${groudId}&conversation_id=${conversationId}&mode_type=${modeType}`} className='underline text-blue-500'>点击</Link>
        </Trans><br />
        {true?
        <Trans i18nKey="app.chat.export_all" t={t}>导出历史记录请<button onClick={exportHistory} className='underline text-blue-500'>点击</button></Trans> : 
        <Trans i18nKey="app.chat.export_all" t={t}>导出历史记录请<Link href={`${process.env.NEXT_PUBLIC_API_HOST}/history/export?role_id=${roleId}&conversation_id=${conversationId}&groupid=${groudId}&mode_type=${modeType}`} target='_blank' className='underline text-blue-500'>点击</Link></Trans>
        }
        </div> : null}</>
}


export default memo(ExportHistory, (prevProps, nextProps) => {
  return prevProps.showExoprt === nextProps.showExoprt && prevProps.isBg === nextProps.isBg && prevProps.opacity === nextProps.opacity
})

