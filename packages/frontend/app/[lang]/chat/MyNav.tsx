'use client'
import React, { Fragment, useContext, memo, useEffect, useState, useCallback } from 'react'
import { ArrowUturnLeftIcon, CheckCircleIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import Setting from './setting';
import { useBackButton } from '@share/src/hooks/useBackButton';
import IconClean from './icons/clean'
import useComfirm from '@share/src/hook/useComfirm';

const btnClass = 'group flex w-full items-center gap-2 rounded-lg py-3 px-2 dark:data-[focus]:bg-white/10 dark:hover:bg-white/10 hover:bg-violet-500 hover:text-white'
const iconClass = 'size-4 text-purple-500 dark:text-gray-300 group-hover:text-white'
const MyNav = ({ modeType, groupId, chatInfo, cardName, startNewChat }: any) => {
    const { t } = useTranslation()
    const comfirm = useComfirm();
    const { backHandler } = useBackButton({ useBackFn: true });
    return <nav className='fixed dark:bg-[var(--background)] bg-white drop-shadow-u dark:drop-shadow-none z-10 w-full left-0 top-0 py-2 h-11 dark:border-b dark:border-[#2c2e2d]'>
        <button className='absolute p-2 py-2.5 left-1 top-0.5 flex text-sm items-center hover:bg-gray-200 dark:hover:bg-gray-800 rounded' onClick={backHandler}>
            <ArrowUturnLeftIcon className='w-4 h-4 mr-0.5 text-purple-500' />
            {t('app.common.save_back')}
        </button>
        <div className='w-fit mx-auto text-sm h-7 flex items-center justify-center'>
            <div className='flex items-center flex-shrink-0 text-gray-600 dark:text-gray-200'>
                <div className="text-center text-sm w-[55vw] truncate">
                    {cardName}
                </div>
            </div>
        </div>
        <div className='!absolute right-0 top-0 flex items-center'>
            <button className={btnClass} onClick={async () => {
                const res = await comfirm.show({
                    title: t('app.chat.warn'),
                    desc: t('app.chat.warn_desc')
                })
                if (res?.confirm) {
                    startNewChat()
                }
            }}>
                <div className={iconClass}><IconClean /></div>
            </button>
            <Setting className='!py-3 !px-2 !mr-1' chatInfo={chatInfo} modeType={modeType} groupId={groupId} />
        </div>
    </nav>
}


export default memo(MyNav)
