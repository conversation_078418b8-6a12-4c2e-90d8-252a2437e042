import { LoadStatus } from "./tool"

// imgId: 跟message_id一样，缓存判断是否重复请求
export type IChat = {
    id?: string
    type?: 'human' | 'ai' | 'img'
    avatar?: string
    message_id?: string
    version?: string
    imgId?: string  
    voice_url?: string
    content: string
    timestamp?: number | null
    isLoading?: boolean
    role_id?: number
    isRecieving?: boolean
    photo_url?: string
    loadingImg?: boolean
    can_continue_replay?: boolean
    isContinueLoading?: boolean
    loadStatus?: LoadStatus
    errMsg?: string // content异常，状态码: {{status}}, 说明: {{errMsg}}
    chatStatus?: boolean // 是否成功输出所有内容
    retry_photos?: any[] // 重试图片
}