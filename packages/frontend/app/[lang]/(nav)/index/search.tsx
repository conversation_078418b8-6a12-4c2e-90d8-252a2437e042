import { MagnifyingGlassIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import { useRouter, useParams } from 'next/navigation'
import Toast from '@little-tavern/shared/src/ui/toast'
import { useRef } from 'react'

const Search = ({nsfw}: {nsfw: boolean}) => {
    const router = useRouter();
    const { t } = useTranslation()
    const params = useParams()
    const lang = params.lang as string
    const seachIptRef = useRef<any>(null)
    
    const searhHandle = async () => {
        const query = seachIptRef.current.value;
        console.log('seachIptRef.current.value', query);
        if (!valid(query)) return
        router.push(`/${lang}/search?keyword=${query}&nsfw=${nsfw}`)
    }
    const valid = (query: string) => {
        if ((!query || query.trim() === '')) {
            Toast.notify({
                type: 'warning',
                message: t('app.search.noMsg')
            })
          return false
        }
        return true
      }
    const handleKeyUp = (e: any) => {
        if (e.code === 'Enter' || e.key === 'Enter') {
            e.preventDefault()
            searhHandle()
        }
    }

    return <div className='flex-1  sm:w-96 sm:flex-none'>
        <div className='flex items-center'>
            <div className='relative flex-1 mr-2'>
                <input
                ref={seachIptRef}
                type="text"
                onKeyUp={handleKeyUp}
                enterKeyHint={'search'}
                placeholder={t('app.index.search_place_holder')}
                className='pl-3.5 rounded-full border-0 py-2 sm:py-2.5 shadow-sm ring-1 ring-inset dark:ring-gray-500 ring-purple-500 focus:ring-inset focus:outline-0 text-sm sm:leading-6 dark:bg-gray-800 px-3 w-full outline-none' />
                <button className=' absolute rounded-full dark:bg-gray-800 bg-white text-gray-500 right-1 top-[2px] p-1.5 sm:p-2.5' type='button' onClick={searhHandle}>
                    <MagnifyingGlassIcon className='w-5 h-5 text-purple-500 dark:text-gray-300 dark:hover:text-gray-100' />
                </button>
            </div>
        </div>
    </div>
}

export default Search