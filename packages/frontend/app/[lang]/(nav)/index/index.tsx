import React from 'react'
import TabSwitcher from './TabSwitcher'
import Footer from "@/app/[lang]/components/footer"
import Header from "../../components/header"
import TabSwitcherServer from './TabSwitcherServer'

interface TextGenerationProps {
  params: {
    lang: string;
  };
}

const TextGeneration = async ({ params }: TextGenerationProps) => {
  const { nsfwData, normalData } = await TabSwitcherServer({ params })

  return (
    <>
      <TabSwitcher nsfwData={nsfwData} normalData={normalData} />
    </>
  )
}

export default TextGeneration