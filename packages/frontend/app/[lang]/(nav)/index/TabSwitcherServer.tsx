import { limit } from './common';
interface TabSwitcherServerProps {
  params: {
    lang: string;
  };
}

// 缓存上一次成功获取的数据
let lastSuccessfulResponse: any = null;
const defaultData = {
  card_list: [],
  count: 0,
};
async function fetchData(enabledNSFW: boolean, lang: string) {
  if(process.env.NEXT_PUBLIC_ENV === 'dev') {
    return defaultData
  }
    const url = `${process.env.NEXT_PUBLIC_API_HOST}/roles/filter_list_v2?nsfw=${enabledNSFW}&offset=0&limit=${limit}`;
    
    try {
      const response = await fetch(url, {
        next: { 
          revalidate: 90 // 每90秒重新验证
        },
        headers: {
          'current-language': lang
        },
      });
      if (!response.ok) {
        console.log('fetchData failed', url);
        throw new Error('Failed to fetch data');
      }
      
      const data = await response.json();
      lastSuccessfulResponse = data; // 保存成功获取的数据
      console.log('fetchData filter_list_v2 done', url);
      return data;
    } catch (error: any) {
      console.error('Error fetching initial data:', url, error.name, error.message);
      // 如果有缓存数据，返回缓存数据
      if (lastSuccessfulResponse) {
        console.log('Using cached data');
        return lastSuccessfulResponse;
      }
      
      // 没有缓存时返回默认空数据
      return defaultData;
  }
}

export default async function TabSwitcherServer({ params }: TabSwitcherServerProps) {
  const lang = params.lang
  // console.log('lang', lang);
  // 为每种语言获取数据
  const [nsfwData, normalData] = await Promise.all([
    fetchData(true, lang),
    fetchData(false, lang)
  ])
  return {
    nsfwData: nsfwData,
    normalData: normalData
  }
}
