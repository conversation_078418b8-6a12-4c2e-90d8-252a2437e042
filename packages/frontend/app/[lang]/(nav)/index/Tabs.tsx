
const VIST_NEW_TAB_TIME = 'index_visit_new_tab_time'
const Tabs = ({pageData, currentTag, updatePageData, reset, setVisitNewTime, visitNewTime}: any) => {
    return (
        <div className="pt-3 px-2 flex justify-between mb-3">
            <div className='space-x-1.5 flex flex-1 text-sm'>
                {pageData?.tags.map((item: any, index: number) => {
                    return <button
                        key={index}
                        className={`relative dark:border-0 px-[13px] py-1 rounded-lg ${item.key === currentTag || (currentTag === '' && item.key === 'CHOSEN') ? 'bg-purple-500 text-white border border-purple-500' : 'dark:bg-gray-700 dark:text-white bg-white text-purple-500 border-solid border border-purple-500 dark:hover:bg-gray-500 hover:bg-gray-100'}`}
                        onClick={() => {
                            if (item.key === currentTag) return;
                            updatePageData((draft: any) => {
                                draft.currentTag = item.key
                                // 如果是榜单，选中
                                if (item.key === 'MONTHLY' || item.key === 'WEEKLY' || item.key === 'DAILY') {
                                    draft.sort_type = item.key
                                }
                            })

                            if (item.key === 'NEW') {
                                const visitNewTabTime = Date.now() / 1000;
                                localStorage.setItem(VIST_NEW_TAB_TIME, String(visitNewTabTime))
                                setVisitNewTime(visitNewTabTime)
                            }
                            reset();
                        }}
                    >
                        {item.name}
                        {(item.key === 'NEW' && visitNewTime < pageData.newTabCreateTime) && <span className='absolute right-2 top-1 w-1.5 h-1.5 rounded-full bg-red-500'></span>}
                    </button>
                })}
            </div>
        </div>
    )
}

export default Tabs