'use client'

import { useCallback, useEffect, useRef, useState } from "react";
import useRequest from "@little-tavern/shared/src/hook/useRequest";
import _ from 'lodash';

let prevFormVal: any;
const useCountToken = (formValues: any, prevTokens: any, sumTokenLimit?: number, userInfo?: any) => {
    // console.log('useCountToken userInfo', userInfo);
    const [tokens, setTokens] = useState<any>({
        scenario: {
            count: 0,
            max: 1000
        },
        sum_book: {
            count: 0,
            max: 800
        },
    })
    const request = useRequest();
    const abortControllers = useRef<{ [key: string]: AbortController }>({});

    // 统计总tokens
    const sumTokens = () => {
        setTokens((n: any) => {
            let sum = 0;
            Object.keys(n).forEach((key) => {
                if (key !== 'sum_book') {
                    sum += n[key].count;
                }
            });
            n['sum_book'].count = sum;
            return n
        });
    }
    const countToken = async (key: string, str: string) => {
        if (abortControllers.current[key]) {
            abortControllers.current[key].abort('debounce自动取消');
        }
        const controller = new AbortController();
        abortControllers.current[key] = controller;
        try {
            const res = await request('/tokenizers/count', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ content: str }),
                signal: controller.signal
            });

            setTokens((prevTokens: any) => ({
                ...prevTokens,
                [key]: {
                    ...prevTokens[key],
                    count: res.token_count
                }
            }));
            sumTokens();
        } catch (error: any) {
            if (error.name !== 'AbortError') {
                console.log('Error counting tokens:', error);
            }
        }
    }
    const debouncedCountToken = useCallback(
        _.debounce((formValues: any) => {
            Object.keys(tokens).forEach((key) => {
                if(formValues[key] === undefined || prevFormVal[key] === undefined) return;
                if (formValues[key] != prevFormVal[key]) {
                    // console.log('formValues[key]', formValues[key], key);
                    let val = formValues[key];
                    // 替换char后计算
                    let regex = /{{user}}/g;
                    // 替换为 xxx
                    val = val.replace(regex, formValues['user_name'] || userInfo?.nickname) || '';
                    countToken(key, val);
                }
            });
            prevFormVal = JSON.parse(JSON.stringify(formValues));
        }, 1000),
        []
    );

    useEffect(() => {
        // console.log('formValues', formValues);
        if (!prevFormVal) {
            prevFormVal = JSON.parse(JSON.stringify(formValues));
        } else {
            debouncedCountToken(formValues);
        }
    }, [formValues])
    useEffect(() => {
        console.log('prevTokens', prevTokens);
        // 初始化线上tokens
        Object.keys(tokens).forEach((key) => {
            if(prevTokens[key]) {
                tokens[key].count = prevTokens[key]
            }
        })
        // 总token限制
        if(sumTokenLimit) {
            tokens['sum_book'].max = sumTokenLimit
        }
        // console.log('tokens', tokens);
        setTokens({...tokens})
        sumTokens();
    }, [prevTokens, sumTokenLimit])
    return {
        tokens,
    }
}

export default useCountToken; 