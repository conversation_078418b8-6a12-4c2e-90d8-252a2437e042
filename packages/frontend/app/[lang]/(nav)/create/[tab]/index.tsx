'use client'
import React, { use, useCallback, useContext, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useParams, useRouter } from 'next/navigation'
import Card from '@little-tavern/shared/src/ui/card'
import s from '@/app/[lang]/globals.module.css'
import Footer from '../../../components/footer'
import { AuthContext } from '@little-tavern/shared/src/authContext'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import Loader from '@little-tavern/shared/src/ui/Loading';
import useSWRImmutable, { mutate } from 'swr';
import Toast from '@little-tavern/shared/src/ui/toast'
import useDialogAlert from '@little-tavern/shared/src/hook/useDialogAlert'
import useCreateCard from '@little-tavern/shared/src/hook/useCreateCard'
import useCreateGroup from '@little-tavern/shared/src/hook/useCreateGroup'
import usePagesState from '@little-tavern/shared/src/hook/usePageState'
import { LoginStatus } from '@little-tavern/shared/src/authContext';
import useDialogLogin from '@little-tavern/shared/src/hook/useDialogLogin'
import MyRole from './myrole'

const btn = cn(s.primBtn, 'mx-2 text-sm')
const url = `/user/chat/roles`;
const Main = ({params: {lang, tab}}: any) => {
  const { t } = useTranslation()
  const auth = useContext(AuthContext);
  const request = useRequest();
  const userInfo = auth?.user;
  const { data, isLoading } = useSWRImmutable(userInfo?.id? url : null, request, {
      revalidateOnFocus: false
  });
  const dialogAlert = useDialogAlert();
  const createGroup = useCreateGroup();
  const loginStatus = auth?.loginStatus;
  const dialogLogin = useDialogLogin();
  const reouter = useRouter();

  // console.log('tabName', tabName);

  const tabs = [
    {
      id: 1,
      name: 'myroles',
      title: t('app.history.my_role'),
    },
    {
      id:2,
      name: 'mygroup',
      title: t('app.history.my_group'),
    }
  ]

  const alertQuota = (desc: string, comfirmBtn?: string, cb?: any) => {
    dialogAlert.show({
      title: t('app.mine.exceed_limit'),
      desc: desc,
      alertStatus: '-1',
      comfirmBtn: comfirmBtn
    }).then(() => {
      cb && cb()
    })
  }

  const fetchUserCard = useCallback(async () => {
    mutate(url);
  }, [])
  useEffect(() => {
    if (loginStatus === LoginStatus.failed) {
      dialogLogin.show();
      return
    }
  }, [loginStatus])
  useEffect(() => {
    reouter.prefetch(`/${lang}/create/mygroup`);
  }, [])
  return (
    <>
      <main className="main-height">
        {userInfo?.id == undefined || isLoading? <Loader msg={t('app.common.loading')}/> : <div className='con-width'>
        <div className='py-1 flex ml-2 space-x-3.5'>
          {tabs.map((elem: any) => {
            return <div key={elem.id}>
              <button onClick={() => {
                reouter.push(`/${lang}/create/${elem.name}`);
              }} className={cn('my-1.5 text-sm py-0.5', tab === elem.name ? 'dark:text-white text-purple-500 border-b-2 border-purple-500' : 'hover:bg-gray-200 dark:hover:bg-gray-800 text-gray-500 dark:text-gray-400')}>{elem.title}</button>
            </div>
          })}
        </div>
        {
          tab === tabs[0].name && <MyRole fetchUserCard={fetchUserCard} alertQuota={alertQuota} data={data} />
        }
        {
          tab === tabs[1].name && <div className='mt-1'>
            <div className='px-2'>
              <button className={cn(btn, '!px-4 ml-0')} onClick={async () => {
                if(data?.create_groups?.length >= 3) {
                  alertQuota(t('app.mine.only_three_group_card'));
                } else {
                  const res = await createGroup.show({msg: {create_roles: data?.create_roles || []}})
                  res && fetchUserCard()
                }
              }}>{t('app.history.create_group')}</button>
              <span className='text-xs text-red-500'>{t('app.common.public_test')}</span>
              <button onClick={() => {
                dialogAlert.show({
                  title: t('app.history.group_title'),
                  desc: t('app.history.group_desc'),
                  alertStatus: '2'
                })
              }} type='button' className='mx-1 text-center text-gray-500 dark:hover:text-white border-gray-500 dark:hover:border-white rounded-full border w-4 h-4 text-xs '>?</button>
            </div>
            <div className="px-2 pt-2 pb-6 grid grid-cols-1 sm:grid-cols-2 gap-2 lg:gap-3 lg:grid-cols-4 xl:grid-cols-5">
            {
              data?.create_groups.length > 0?
              data.create_groups.map((role: any) => {
                return <Card key={role.id} modeType={'group'} reflesh={fetchUserCard} role={{isEdit: true, ...role, isShowDel: true, isShowEdit: true, isShowPublic: true }}></Card>
              }) :
              <p className="mt-1 text-xs leading-6 text-gray-400 text-center">{t('app.mine.upload_group_tip')}</p>
            }
          </div></div>
        }
        </div>}
      </main>
    </>
  )
}

export default Main
