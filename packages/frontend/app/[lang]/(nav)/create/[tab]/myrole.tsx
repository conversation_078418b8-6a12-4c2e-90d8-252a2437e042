'use client'
import React, { use, useCallback, useContext, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useParams, useRouter } from 'next/navigation'
import Card from '@little-tavern/shared/src/ui/card'
import s from '@/app/[lang]/globals.module.css'
import { AuthContext } from '@little-tavern/shared/src/authContext'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import Toast from '@little-tavern/shared/src/ui/toast'
import useCreateCard from '@little-tavern/shared/src/hook/useCreateCard'
import Modal from '@little-tavern/shared/src/ui/dialog/Modal'
import Illustrate from '@share/src/ui/tooltip/illustrate'
import { useConfig } from '@share/src/configContext';
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import {web} from '@/app/module/global'

const btn = cn(s.primBtn, 'mx-2 text-sm')
const modelBtn = 'w-5/6 py-4 block my-5 rounded-lg bg-purple-500 text-lg text-white hover:bg-purple-600 mx-auto';
// 展示过任务图标，隐藏后不再展示，下次启动小程序再展示
let showPublicTaskIcon = true
const btnClass = 'flex relative items-center rounded-full border border-purple-400 dark:border-gray-300 px-3 mr-1.5 py-1 text-xs dark:text-white';

const MyRole = ({ fetchUserCard, alertQuota, data }: any) => {
    const { t } = useTranslation()
    const params = useParams()
    const lang = params.lang as string
    const auth = useContext(AuthContext);
    const request = useRequest();
    const userInfo = auth?.user;
    const max_role_count = userInfo?.max_role_count || 6;
    // console.log('max_role_count', max_role_count);
    const createCard = useCreateCard();
    const speakList = userInfo?.speaker_list || [];
    const [openEditorModel, setOpenEditorModel] = useState(false)
    const _config = useConfig();
    const [config, setConfig] = useState(_config)
    const [showPublicTask, setShowPublicTask] = useState(userInfo?.publish_task_switch)
    const sortOptions = [
        {key: 'default', name: t('app.sort.default')},
        {key: 'approved', name: t('app.sort.approved')},
        {key: 'reject', name: t('app.sort.reject')},
        {key: 'no_publish', name: t('app.sort.no_publish')},
    ]
    const openEditor = async (level_type: string) => {
        setOpenEditorModel(false)
        const isSuccess = await createCard.show({ msg: { isEdit: false, speakList: speakList, level_type: level_type } })
        if (isSuccess) {
            fetchUserCard()
        }
    }
    const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        if (!checkQuota()) {
            return;
        }
        if (e.target.files && e.target.files.length > 0) {
            const formData = new FormData();
            const file = e.target.files[0];
            formData.append('image', file);
            formData.append('file_type', file.type);
            const hideLoading = Toast.showLoading('');
            try {
                const res = await request('/role_card/analysis/img', {
                    method: 'POST',
                    body: formData,
                });
                const data = res.role_config;
                data.role_book = res.role_book;
                data.role_token_count = res.role_token_count;
                hideLoading();
                const isSuccess = await createCard.show({ msg: { isEdit: false, ...data, speakList: speakList, isFromImg: true, level_type: 'premium' } })
                if(isSuccess) {
                    setOpenEditorModel(false)
                    isSuccess && fetchUserCard()
                }
            } catch (error) {
                Toast.notify({ type: 'success', message: t('app.mine.upload_failed') })
                hideLoading();
                console.error('Error uploading image:', error);
            }
        }
        e.target.value = ''
    }
    const checkQuota = () => {
        if (data?.create_roles?.length >= max_role_count ) {
            if(data?.create_roles?.every((item: any) => item?.audit_status === 'approved')) {
                alertQuota(t('app.mine.nopay_card_limit1'), t('app.mine.find_customer_service'), () => {
                    if(web) {
                        window.open(`mailto:${process.env.NEXT_PUBLIC_SUPPORT_MAIL}`, '_blank');
                    } else {
                        window.open(`https://t.me/ai_x01_bot`, '_blank');
                    }
                });
            } else {
                alertQuota(t('app.mine.nopay_card_limit'));
            }
            
            return false
        } else {
            return true
        }
    }
    const [selectedSort, setSelectedSort] = useState('')
    const handlerSwitchSort = (sort: any) => {
        setSelectedSort(sort)
    }
    let list = [...(data?.create_roles || [])];
    if(selectedSort) {
        if(selectedSort === 'default') {
            list = [...(data?.create_roles || [])];
        } else if(selectedSort === 'approved') {
            // 审核通过：审核通过的卡、以及审核通过后正在编辑、正在二次审核的卡，排在上方，其他保持默认的逻辑排在下方
            list.sort((a: any, b: any) => (b.audit_status === 'auditing' && b.public_role_id != 0 ? 1 : 0) - (a.audit_status === 'auditing' && b.public_role_id != 0 ? 1 : 0))
            list.sort((a: any, b: any) => (b.audit_status === 'editing' && b.public_role_id != 0 ? 1 : 0) - (a.audit_status === 'editing' && b.public_role_id != 0 ? 1 : 0))
            list.sort((a: any, b: any) => (b.audit_status === 'approved' ? 1 : 0) - (a.audit_status === 'approved' ? 1 : 0))
        } else if(selectedSort === 'reject') {
            // 审核拒绝：被拒审的卡排在上方，其他保持默认的逻辑排在下方
            list.sort((a: any, b: any) => (b.audit_status === 'rejected' ? 1 : 0) - (a.audit_status === 'rejected' ? 1 : 0))
        } else if(selectedSort === 'no_publish') {
            // 未发布：私有卡未提交公开发布的卡排在上方，其他保持默认的逻辑排在下方
            list.sort((a: any, b: any) => (b.audit_status === '' ? 1 : 0) - (a.audit_status === '' ? 1 : 0))
        }
    }
    return <div className='mt-1'>
        <div className='px-2 flex items-center justify-between'>
            <button className={cn(btn, '!px-4 ml-0 relative')} onClick={async () => {
                if(checkQuota()) {
                    setOpenEditorModel(true)
                }
                setShowPublicTask(false)
                showPublicTaskIcon = false
            }}>
                {showPublicTask && showPublicTaskIcon && <div className='absolute -top-1 -right-4 text-xs px-1 bg-red-500 rounded-full'>{t('app.gift')}</div>}
                {t('app.mine.create')}
            </button>
            <div className={`${btnClass} ${selectedSort && '!border-purple-400'}`}>
                {selectedSort ? <div className='truncate w-min'>{sortOptions.find((item: any) => item.key === selectedSort)?.name}</div> : t('app.sort.default')}
                <ChevronDownIcon className={cn("-mr-1 h-3.5 w-3.5 text-violet-500  dark:text-zinc-100")} />
                <select
                    className='text-center bg-white dark:bg-black dark:text-white w-full h-full absolute opacity-0 left-0 top-0'
                    value={selectedSort || ''}
                    onChange={(e) => handlerSwitchSort(e.target.value)}
                >
                    {
                        sortOptions.map((item: any) => {
                            return <option key={item.key} value={item.key}>{item.name}</option>
                        })
                    }
                </select>
            </div>
        </div>
        <div className="px-2 pt-2 pb-6 grid grid-cols-1 sm:grid-cols-2 gap-2 lg:gap-3 lg:grid-cols-4 xl:grid-cols-5">
            {
                list.length > 0 ?
                list.map((role: any) => {
                        return <Card key={role.id} reflesh={fetchUserCard} role={{ isEdit: true, ...role, speakList, isShowDel: true, isShowEdit: true, isShowPublic: true }} config={config}></Card>
                    }) :
                    <p className="mt-1 text-xs leading-6 text-gray-400 text-center">{t('app.mine.upload_tip')}</p>
            }
        </div>

        <Modal isOpen={openEditorModel} onClose={() => { setOpenEditorModel(false) }}>
            <div className='min-h-72 text-center'>
                <h3 className='pt-6 pb-5 text-xl flex items-center justify-center'>
                    <span className='mr-1'>{t('app.cardEdit.select_model')}</span> <Illustrate title={t('app.mine.upload_card_title')} desc={t('app.mine.upload_card_desc')} isMd={true}></Illustrate>
                </h3>
                <div className='flex items-center justify-center gap-2 text-xs whitespace-pre-wrap'>{data?.publish_card_tips}</div>
                <button className={cn(modelBtn)} onClick={() => { openEditor('normal') }}>{t('app.cardEdit.create1')}</button>
                <label className={cn(modelBtn, 'cursor-pointer')} htmlFor="cardInput">
                {t('app.mine.upload_card')}
                <input id="cardInput" name='cardInput' type="file" hidden accept="image/png" onChange={handleImageChange} />
            </label>
            </div>
        </Modal>
    </div>

}

export default MyRole