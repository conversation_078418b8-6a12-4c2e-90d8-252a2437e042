'use client'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useParams, useRouter } from 'next/navigation'
import Footer from "@/app/[lang]/components/footer";
import { Markdown } from '../../components/markdown';
import {ArrowUturnLeftIcon, CheckCircleIcon} from '@heroicons/react/24/solid'
import Back from '@little-tavern/shared/src/ui/Back'
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import Link from 'next/link'
interface FAQItem {
  id: string
  question: string
  answer: any
}
const Pay = () => {
  const { t } = useTranslation()
  const [content, setContent] = useState('');
  const params = useParams()
  const lang = params.lang as string
  const [activeQuestion, setActiveQuestion] = useState('');
  useEffect(() => {
    setContent(t('app.about.con'));
  }, [])
  useEffect(() => {
    const hash = window.location.hash;
    // 并且hash存在并且符合hash格式，非key=value
    if (hash && hash.startsWith('#') && !hash.includes('=')) {
      setActiveQuestion(hash.slice(1));
      const element = document.querySelector(hash);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }, [content]);
  const faqData: FAQItem[] = [
    {
      id: 'q1',
      question: t('app.faq.q1_title'),
      answer: t('app.faq.q1_desc', {mail: process.env.NEXT_PUBLIC_SUPPORT_MAIL}),
    },
    {
      id: 'q2',
      question: t('app.faq.q2_title'),
      answer: t('app.faq.q2_desc'),
    },
    {
      id: 'q3',
      question: t('app.faq.q3_title'),
      answer: t('app.faq.q3_desc'),
    },
    {
      id: 'q4',
      question: t('app.faq.q4_title'),
      answer: t('app.faq.q4_desc'),
    },
    {
      id: 'q5',
      question: t('app.faq.q5_title'),
      answer: t('app.faq.q5_desc'),
    },
    {
      id: 'q6',
      question: t('app.faq.q6_title'),
      answer: t('app.faq.q6_desc'),
    },
  ];
  const toggleAnswer = (id: string) => {
    setActiveQuestion(activeQuestion === id ? '' : id);
  };
  return (
    <>
      {content && <><main className="main-height w-full con-width px-3 text-base">
        <Back />
        <div className={cn('prose dark:prose-invert w-full sm:w-[768px] mx-auto pt-1 text-base mb-3')}><Markdown content={t('app.about.con', {mail: process.env.NEXT_PUBLIC_SUPPORT_MAIL})} isPage={true} />
        <div id="faq" className='h-8'></div>
        <h1 className='text-xl py-4 pb-1'><Link className='text-blue-500 underline decoration-1 decoration-blue-500' href={`/${lang}/about#faq`}>{t('app.faq.title')}</Link></h1>
          {faqData.map((item, index) => (
            <div key={item.id} id={item.id} className=''>
              <h2
                className='flex items-center my-3 cursor-pointer'
                onClick={() => toggleAnswer(item.id)}
              >
                {++index}. {item.question}
                <ChevronDownIcon
                    className={cn("-mr-1 h-5 w-5 text-purple-500 hover:text-violet-100 transition", activeQuestion !== item.id && "rotate-180")}
                    aria-hidden="true"
                  />
              </h2>
              {activeQuestion !== item.id && (
                <div className='mt-2 rounded-md p-3 border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900'>
                  {
                    typeof item.answer == 'string'? <div className='prose text-base dark:prose-invert'><Markdown content={item.answer} isPage={true} /></div> : item.answer
                  }
                </div>
              )}
            </div>
          ))}
          </div>
      {content && <Footer cl='justify-center ml-0 !m-auto left-0 right-0 w-full'/>}
      </main>
      </>}
    </>
  )
}

export default React.memo(Pay)

