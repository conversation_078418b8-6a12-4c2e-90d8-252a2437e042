import React from 'react'
import Main from './index'
// import serverFetch from '@share/src/module/serverFetch';

const App = ({params}: any) => {
  const userId = params.id;
  // console.log('userId', userId);
  // const res = await serverFetch({
  //   url: `/user/other?other_user_id=${userId}`
  // })
  // console.log('res', res);
  return (
    <Main params={params} userId={userId} />
  )
}
export default React.memo(App)
