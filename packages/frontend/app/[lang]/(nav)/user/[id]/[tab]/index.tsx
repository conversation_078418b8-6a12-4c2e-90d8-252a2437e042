'use client'
import React, { useCallback, useContext, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams, useRouter } from 'next/navigation'
import Footer from "@/app/[lang]/components/footer";
import { AuthContext } from '@little-tavern/shared/src/authContext'
import PersonSetting from './PersonSetting'
import Loader, { LoadingToast, LoadingToastFailed } from '@little-tavern/shared/src/ui/Loading'
import PublicCard from './publicCard'
import ShareList from '@/app/[lang]/components/share-list/shareList';
// import ChatList from '@/app/[lang]/components/share-list/chatList';
import Tabs from '@/app/[lang]/components/tabs';
import s from './style.module.scss'
import cn from 'classnames'
import Back from '@share/src/ui/Back'

const Main = ({params}: any) => {
  const { t } = useTranslation()
  const auth = useContext(AuthContext);
  const userId = params.id;
  const lang = params.lang;
  const tab = params.tab;
  
  // console.log('data', data);
  const reouter = useRouter();
  const tabs = [
    {
      id: 1,
      name: 'card',
      title: t('app.mine.user_public'),
      link: `/${lang}/user/${userId}/card`
    },
    {
      id: 2,
      name: 'share',
      title: t('app.mine.user_share'),
      link: `/${lang}/user/${userId}/share`
    }
  ]
  
  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
    reouter.prefetch(`/${lang}/user/${userId}/share`);
  }, []);
  return (
    <>
      <main className={cn(s.layout, "main-height con-width")}>
        <Back />
        <PersonSetting userId={userId}></PersonSetting>
        <Tabs tabs={tabs}></Tabs>
        {!mounted ? <Loader msg={t('app.common.loading')} /> : (
          <>
            {
              tab === tabs[0].name && <PublicCard userId={userId}></PublicCard>
            }
            {
              tab === tabs[1].name && <ShareList url={`/share/list/other?other_user_id=${params.id}`}></ShareList>
            }
          </>
        )}
      </main>
    </>
  )
}

export default Main
