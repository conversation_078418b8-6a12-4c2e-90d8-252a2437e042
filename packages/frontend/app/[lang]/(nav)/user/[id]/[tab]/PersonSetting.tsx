'use client'
import cn from 'classnames'
import React from 'react'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import Toast from '@little-tavern/shared/src/ui/toast'
import Image from 'next/image'
import { PhotoIcon, UserCircleIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import useSWRImmutable from 'swr';

const PersonSetting = ({userId}: any) => {
  const { t } = useTranslation()
  const request = useRequest()
  
  const { data: userInfo } = useSWRImmutable(
    `/user/other?other_user_id=${userId}`,
    async (url) => {
      const res = await request(url)
      if (res?.error_code === 0) {
        return res.data
      }
      Toast.notify({ type: 'error', message: res.message })
      throw new Error(res.message)
    },
    {
      fallbackData: {},
      onError: () => {
        Toast.notify({
          type: 'error',
          message: t('app.common.load_err')
        })
      }
    }
  )
  // console.log('userInfo', userInfo);
  return <>
    <div className='flex justify-between items-center mx-2 px-3 py-3 bg-white dark:bg-gray-900 rounded'>
      <div className='flex items-center'>
        {userInfo?.avatar ? <Image className='rounded-full h-12 w-12 object-cover' src={userInfo?.avatar} width={48} height={48} alt={'avatar'} /> : <UserCircleIcon className="h-12 w-12 text-gray-500" aria-hidden="true" />}
        <div className='sm:w-full sm:max-w-sm dark:text-white'>
          <div className="ml-3 w-20">
            {userInfo?.nickname}
          </div>
        </div>
      </div>
    </div>
  </>
}


export default PersonSetting