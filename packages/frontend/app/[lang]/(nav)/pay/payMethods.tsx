"use client";
import Image from 'next/image'
import React, { useState } from 'react';
import s from '@/app/[lang]/globals.module.css'
import cn from 'classnames';
import useRequest from '@little-tavern/shared/src/hook/useRequest';
import useDialogCharge from './useDialogCharge';
import useDialogAlert from '@little-tavern/shared/src/hook/useDialogAlert'
import { CheckIcon } from '@heroicons/react/20/solid';
import Toast from '@little-tavern/shared/src/ui/toast';
import useComfirm from '@little-tavern/shared/src/hook/useComfirm';
import useIosPayDialog from './useIosPayDialog'
import Link from 'next/link';
import { useTranslation } from 'react-i18next'
import { isIOS, isTG } from '@share/src/module/global';
import usePayChannel from './usePayChannel';

type PayMethodsProps = {
    chargeSuccess: Function,
    USDTBalance: number
    supported_pay_types: any[]
    recharge_id: string
};

const payBtn = cn(s.primBtn, 'text-sm !px-1 !py-1.5 w-full !box-border !py-2 whitespace-pre-wrap')
const PayMethods = ({chargeSuccess, supported_pay_types, recharge_id}: PayMethodsProps) => {
    const request = useRequest();
    const dialogAlert = useDialogAlert();
    const dialogCharge = useDialogCharge();
    const comfirm = useComfirm();
    const iosPayDialog = useIosPayDialog();
    const { t } = useTranslation()
    const payChannel = usePayChannel();
    
    // 微信支付宝提供两个支付渠道，目前暂时不用
    const aliWechatPay = async ({typeId, bk_btn, bk_btn_subtitle, channel, payName}: any) => {
        // if(typeId === 'wxpay' || typeId === 'alipay') {
        //     const res = await payChannel.show({ title, desc, btn1, btn2, subTitle, btn1_subtitle, btn2_subtitle })
        //     if(res.event === 'channel1') {
        //         channel = 'channel1'
        //     } else if(res.event === 'channel2') {
        //         channel = 'channel2'
        //     } else {
        //         return
        //     }
        //     recharge2(typeId, channel)
        // }
        recharge2(typeId, bk_btn, bk_btn_subtitle, channel? channel : 'channel1', payName)
    }
    // 支付渠道2
    const recharge2 = async (typeId: string, bk_btn?: string, bk_btn_subtitle?: string, channel?: string, payName?: string) => {
        Toast.showLoading(t('app.common.loading'));
        try {
            const res = await request(`/andada_recharge/recharge`,{
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({recharge_id: recharge_id, type: typeId, isIOS: isIOS, channel: channel})
            });
            Toast.hideLoading();
            if(res.message == 'success') {
                if(isIOS && res.tgbotUrl) {
                    Toast.guide({
                        message: t('app.pay.guideClose')
                    })
                    // if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
                    await iosPayDialog.show({title: t('app.pay.order_created'), desc: t('app.pay.order_created_desc'), url: res.tgbotUrl, comfirmBtn: t('app.pay.pay'), type: 1})
                    // }
                } else {
                    const newWindow = window.open(res.pay_url);
                    if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
                        await iosPayDialog.show({title: t('app.pay.order_created'), desc: t('app.pay.order_created_desc1'), comfirmBtn: t('app.pay.pay1'), url: res.pay_url, type: 1})
                    }
                }
                const comfirmRes = await comfirm.show({
                    title: t('app.pay.charging'),
                    desc: bk_btn? t('app.pay.charging_desc1', {payChannel: payName}) : t('app.pay.charging_desc'),
                    comfirmBtn: t('app.pay.finish_charge'),
                    showCancelBtn: true,
                    showIcon: false,
                    cancelBtn: bk_btn || t('app.pay.btn1'),
                    cancelBtnStyle: '!bg-green-500 !text-white'
                  })
                if(comfirmRes?.confirm) {
                    chargeSuccess();
                } else if(comfirmRes?.cancel) {
                    // 如果有备用通道
                    if(bk_btn) {
                        aliWechatPay({
                            typeId: typeId,
                            bk_btn: bk_btn,
                            bk_btn_subtitle: bk_btn_subtitle,
                            channel: 'channel2'
                        })
                    } else {
                        await iosPayDialog.show({title: t('app.pay.method'), desc: t('app.pay.method_desc'), url: res.pay_url, comfirmBtn: t('app.pay.copy_addr'), type: 2})
                    }
                }
            } else {
                if(res.error_code === 429) {
                    dialogAlert.show({
                        title: t('app.pay.tips'),
                        desc: res.message,
                        alertStatus: 2
                    })
                } else {
                    Toast.notify({
                        type: 'error',
                        message: t('app.pay.charge_err1')
                    })
                }
            }
        } catch (error) {
            Toast.hideLoading();
            Toast.notify({
                type: 'error',
                message: t('app.pay.charge_err1')
            })
            console.error('Failed to recharge tabs:', error);
        }
    }
    // USDT充值
    const rechargeUSDT = async () => {
        const hideLoading = Toast.showLoading("Loading");
        try {
            const res = await request(`/recharge/usdt`,{
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({recharge_id: recharge_id, chain: "EVM"})
            });
            console.log('res', res) 
            hideLoading();
            const payRes = await dialogCharge.show({...res});
            if(payRes) {
                dialogAlert.show({
                    title: t('app.pay.charge_success'),
                    desc: t('app.pay.pay_success') + `${res.display_fee},` + t('app.pay.charge_success_desc1')
                });
            }
            chargeSuccess();
        } catch (error) {
            console.error('Failed to recharge tabs:', error);
        }
    }
    return (
        <div className='fixed bottom-[var(--bottom-margin)] left-0 py-2 right-0 z-10 px-2 bg-white dark:bg-[var(--background)] border-t border-gray-200 dark:border-[#2c2e2d] md:static md:border-none '>
            <h3 className='text-sm font-bold mb-1.5'>{t('app.pay.pay_methods')}</h3>
            <div className="text-center mb-1 grid grid-cols-3 gap-2">
                {supported_pay_types?.map((item: string, index: number) => {
                    if(item === 'wechat') {
                        return <button type='button' key={index} onClick={() => aliWechatPay({
                            typeId: 'wxpay',
                            bk_btn: t('app.pay.wechat_btn'),
                            bk_btn_subtitle: t('app.pay.channel_bk'),
                            payName: t('app.pay.wechat')
                        })} className={payBtn}>
                        <span className='text-base'>{t('app.pay.wechat')}</span>
                        <span className='text-xs block'>{t('app.pay.wechat_subtitle')}</span></button>
                    }
                    if (item === 'alipay') {
                        return <button type='button' key={index} onClick={() => aliWechatPay({
                            typeId: 'alipay',
                            bk_btn: t('app.pay.alipay_btn'),
                            bk_btn_subtitle: t('app.pay.channel_bk'),
                            payName: t('app.pay.alipay')
                        })} className={payBtn}>
                            <span className='text-base'>{t('app.pay.alipay')}</span>
                            <span className='text-xs block'>{t('app.pay.alipay_subtitle')}</span>
                        </button>
                    }
                    if (item === 'voucher') {
                        return <Link key={index} href='https://www.sdfkw.xyz/links/1275C4C5' target="_blank" className={payBtn}><span className='text-base'>{t('app.pay.wechat_alipay')}</span>
                            <span className='text-xs block'>{t('app.pay.wechat_alipay_subtitle')}</span>
                            </Link>
                    }
                    if (item === 'star' && isTG) {
                        return <button key={index} onClick={() => recharge2('star')} className={payBtn}>{t('app.pay.start_pay')}</button>
                    }
                    if (item === 'stripe') {
                        return <button key={index} onClick={() => recharge2('stripe')} className={payBtn}>{t('app.pay.credit_card')}</button>
                    }
                    if (item === 'usdt') {
                        return <button key={index} onClick={() => rechargeUSDT()} className={payBtn}>{t('app.pay.usdt_charge')}</button>
                    }
                })}
            </div>
        </div>
    )
}

export default PayMethods