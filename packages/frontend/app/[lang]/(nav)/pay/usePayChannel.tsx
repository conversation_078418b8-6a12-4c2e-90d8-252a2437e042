'use client'
import classNames from 'classnames'
import type { ReactEventHandler, ReactNode } from 'react'
import React, { useContext, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import PayChannel from './PayChannel'

export type result = {
  event: string
}
const usePayChannel = () => {
  return {
    // 1 成功提示 非1 警告提示
    show: ({ title, desc, btn1, btn2, subTitle, btn1_subtitle, btn2_subtitle }: any) => {
      let holder = document.createElement('div')
      holder.className = 'absolute z-50 w-full h-full left-0 top-0'
      document.body.appendChild(holder)
      const root = createRoot(holder)
      return new Promise((resolve: (value: result) => void, reject) => {
        const onChannel1 = () => {
          resolve({event: 'channel1'})
          root.unmount();
          document.body.removeChild(holder);
        }
        const onChannel2 = () => {
          resolve({event: 'channel2'})
          root.unmount();
          document.body.removeChild(holder);
        }
        const onCancel = () => {
          root.unmount();
          document.body.removeChild(holder);
          resolve({event: 'cancel'});
        }
        root.render(<PayChannel title={title} desc={desc} btn1={btn1} btn2={btn2} subTitle={subTitle} btn1_subtitle={btn1_subtitle} btn2_subtitle={btn2_subtitle} isOpen={true} onChannel1={onChannel1} onChannel2={onChannel2} onClose={onCancel} />);
      })
    },
    hide: () => {
      // if (holder) root.unmount();
    }
  }
}

export default usePayChannel
