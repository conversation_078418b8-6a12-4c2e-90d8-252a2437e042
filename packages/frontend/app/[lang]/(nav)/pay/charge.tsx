"use client";

import React, { useEffect, useState } from 'react';
import type { FC } from 'react'
import Modal from '@little-tavern/shared/src/ui/dialog/Modal';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { Square2StackIcon } from '@heroicons/react/24/outline'
import cn from 'classnames';
import Image from 'next/image'
import format from '@/app/module/formate-date';
import useRequest from '@little-tavern/shared/src/hook/useRequest';
import Toast from '@little-tavern/shared/src/ui/toast';
import { QRCodeCanvas } from 'qrcode.react';
import { useTranslation } from 'react-i18next'
import { Trans } from 'react-i18next/TransWithoutContext'

type IProps = {
  onClose: Function,
  isOpen: boolean,
  onConfirm: Function,
  // 1 成功提示 非1 警告提示
  status?: string,
  payInfo: any
}
const Alert: FC<IProps> = ({ onClose, isOpen, onConfirm, status = '1', payInfo}) => {
  const exp = format(payInfo.expired_at * 1000, 'YYYY-MM-DD HH:mm:ss')
  const time = Math.floor((payInfo.expired_at * 1000 - Date.now()) / 1000)
  const [remainMinute, setRemainMinute] = useState(Math.floor(time / 60))
  const [remainSec, setRemainSec] = useState(time - remainMinute * 60)
  const request = useRequest();
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const { t } = useTranslation()
  
  useEffect(() => {
    const itv = setInterval(async () => {
      const time = Math.floor((payInfo.expired_at * 1000 - Date.now()) / 1000)
      const remainMinute = Math.floor(time / 60)
      const remainSec = time - remainMinute * 60
      setRemainMinute(remainMinute);
      setRemainSec(remainSec);
      if(remainMinute <=0 && remainSec <= 0) {
        Toast.notify({
          type: 'error',
          message: t('app.pay.overtime')
        })
        onClose();
      }
      console.log('setInterval');
      const res = await request('/recharge/usdt_order_status?usdt_order_id=' + payInfo.order_id)
      if(res.status == 'SUCCEED') {
        onConfirm();
      } else if (res.status == 'FAILED') {
        Toast.notify({
          type: 'error',
          message: t('app.pay.pay_failed')
        })
      }
    }, 1000)
    return () => {
      clearInterval(itv)
    }
  }, [])
  const copyAddress = () => {
    navigator.clipboard.writeText(payInfo.recipient).then(() => {
      Toast.notify({type: 'success', message: t('app.pay.copyed_success')})
    }).catch(err => {
        console.error(t('app.pay.copy_fail'), err);
    });
  }
  const copyMoney = () => {
    navigator.clipboard.writeText(payInfo.display_fee).then(() => {
      Toast.notify({type: 'success', message: t('app.pay.copyed_success')})
    }).catch(err => {
        console.error(t('app.pay.copy_fail'), err);
    });
  }
  return (
    <Modal onClose={() => {onClose()}} isOpen={isOpen}>
      <div className='min-h-20'>
        <div className='py-1 sm:px-10'>
          <div className='mt-3'>
            <h1 className='text-base font-semibold leading-6'>{t('app.pay.paying')}</h1>
            <div className='flex items-center mt-3 justify-center'>
              <span className='text-2xl px-1'>{payInfo.display_fee}</span>
              <span className='text-sm mt-2'>USDT</span>
                <button onClick={copyMoney} className='p-0.5 bg-purple-500 text-white rounded text-xs ml-1'><Square2StackIcon className='w-4 h-4'/></button>
            </div>
            <p className='text-xs mt-2 text-center mt-2 dark:text-gray-100 text-gray-800'>
            <Trans i18nKey="app.pay.usdt_warn" t={t}>
            我们到账金额必须 <b className='text-red-500 text-lg'>与上述金额一致</b> 否则无法及时到账<br/>
            交易所钱包请注意 <b className='text-red-500'>扣除手续费后的金额</b> 与上述一致
            </Trans>
            </p>
            
            <div className='text-sm mt-2 text-white'>
            <div className='text-center mb-3'>
              <span className='inline-block p-1 px-2 bg-blue-500 rounded-full'>{payInfo.chain}</span>
            </div>

              <QRCodeCanvas
                id="qrCodeCanvas"
                value={payInfo.recipient}
                size={120}
                className='mx-auto'
              />

              <div className='mt-3'>
                <div className="rounded-full dark:bg-gray-800 bg-gray-200 dark:text-gray-100 text-gray-700 flex justify-between px-2 py-1 mx-auto items-center w-min text-xs">
                  <span>{payInfo.recipient}</span>
                  <button onClick={copyAddress} className='p-0.5 bg-purple-500 text-white rounded ml-1'><Square2StackIcon className='w-4 h-4'/></button>
                </div>
              </div>
              
              <p className='text-xs mt-2 text-center dark:text-gray-300 text-gray-500'>{t('app.pay.usdt_pay_desc')}</p>
              <div>
                <ul className='mt-3 rounded p-3 dark:bg-gray-800 bg-gray-200 dark:text-gray-100 text-gray-700 flex flex-col gap-3'>
                  <li className='flex justify-between'>
                      <span>{t('app.pay.time_remain')}</span>
                      <span>{remainMinute} {t('app.pay.min')} {remainSec} {t('app.pay.sec')}</span>
                  </li>
                  <li className='flex justify-between'>
                      <span>{t('app.pay.order_id')}</span>
                      <span className='text-xs'>{payInfo.order_id}</span>
                  </li>
                  <li className='flex justify-between'>
                      <span>{t('app.pay.network')}</span>
                      <span className='text-xs'>{payInfo.chain}</span>
                  </li>
                  <li className='flex justify-between'>
                      <span>{t('app.pay.charge_amount')}</span>
                      <span className='text-xs'>{payInfo.total_amount}</span>
                  </li>
                </ul>
                <p className='text-xs dark:text-white text-gray-800 py-2'>
                  {t('app.pay.tips')} <br />
                  <Trans i18nKey="app.pay.tips1" t={t}>
                  <b className='text-red-500'>请勿</b>向上述地址充值USDT-ERC20和TRC20资产，否则资产<b className='text-red-500'>将不可找回</b>
                  </Trans>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default Alert;
