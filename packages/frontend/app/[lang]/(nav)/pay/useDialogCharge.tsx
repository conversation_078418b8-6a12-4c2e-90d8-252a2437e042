'use client'
import classNames from 'classnames'
import type { ReactEventHandler, ReactNode } from 'react'
import React, { useContext, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import Charge from './charge';

const useDialogCharge = () => {
  return {
    // 1 成功提示 非1 警告提示
    show: (payInfo: any) => {
      let holder = document.createElement('div')
      holder.className = 'absolute z-50 w-full h-full left-0 top-0'
      document.body.appendChild(holder)
      const root = createRoot(holder)
      return new Promise((resolve, reject) => {
        const onComfirm = () => {
          resolve(true)
          root.unmount();
          document.body.removeChild(holder);
        }
        const onCancel = () => {
          root.unmount();
          document.body.removeChild(holder);
          resolve(false);
        }
        root.render(<Charge isOpen={true} payInfo={payInfo} onConfirm={onComfirm} onClose={onCancel} />);
      })
    },
    hide: () => {
      // if (holder) root.unmount();
    }
  }
}

export default useDialogCharge
