"use client";

import React from 'react';
import type { FC } from 'react'
import Modal from '@little-tavern/shared/src/ui/dialog/Modal';
import { CheckIcon } from '@heroicons/react/24/outline'
import cn from 'classnames';
import Link from 'next/link';
import Toast from '@little-tavern/shared/src/ui/toast';
import { useTranslation } from 'react-i18next'

type IProps = {
  onClose?: React.MouseEventHandler,
  isOpen: boolean,
  onConfirm?: React.MouseEventHandler,
  comfirmBtn?: string,
  title: string,
  desc: string,
  isShowCancelBtn?: boolean,
  url?: string
  type?: number
}

const Alert: FC<IProps> = ({ onClose, isOpen, onConfirm, comfirmBtn, title, desc, isShowCancelBtn = true, url = '', type}) => {
  const { t } = useTranslation()
  const copyAddress = () => {
    navigator.clipboard.writeText(url).then(() => {
      Toast.notify({type: 'success', message: t('app.pay.copy_success')})
    }).catch(err => {
        console.error('复制失败！', err);
    });
  }
  return (
    <Modal onClose={onClose} isOpen={isOpen}>
      <div className='min-h-20 mb-4'>
        <div className='flex p-1'>
          <div className={cn("flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full  m-2 my-3 ", 'bg-green-500')}>
          <CheckIcon className="h-6 w-6 text-white" aria-hidden="true" />
          </div>
          <div className='ml-3 mt-3'>
            <h1 className='text-base font-semibold leading-6'>{title}</h1>
            <div className='text-sm mt-2'>
              {desc}
              {type == 2 && <div className="mt-2 rounded-full dark:bg-gray-800 bg-gray-200 dark:text-gray-400 text-gray-500 px-2 py-1 text-xs">
                  <p className='text-wrap break-all'>{url}</p>
                  {/* <button onClick={copyAddress} className='px-2 py-1 bg-purple-500 text-white rounded ml-1'>复制</button> */}
                </div>}
            </div>
          </div>
        </div>
      </div>
      <div className='px-2 py-2 flex flex-row-reverse'>
        {
          type == 1? <Link className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3' onClick={onConfirm} href={url || ''} target='_blank'>{comfirmBtn || t('app.common.comfirm')} </Link> : 
          <button className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3' onClick={copyAddress}>{comfirmBtn || t('app.common.comfirm')} </button>
        }
        {isShowCancelBtn && <button className='p-2 px-5 bg-gray-800 rounded text-sm ' onClick={onClose}>{t('app.common.cancel')}</button>}
      </div>
    </Modal>
  );
};

export default Alert;
