'use client'
import React, { useContext, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import IosPayDialog from './IosPayDialog'

const useIosPayDialog = () => {
  return {
    show: ({title, desc, isShowCancelBtn = false, url, comfirmBtn, type}: any) => {
      let holder = document.createElement('div')
      holder.className = 'absolute z-50 w-full h-full left-0 top-0'
      document.body.appendChild(holder)
      const root = createRoot(holder)
      return new Promise((resolve, reject) => {
        const onComfirm = () => {
          resolve(true)
          root.unmount();
          document.body.removeChild(holder);
        }
        const onCancel = () => {
          root.unmount();
          document.body.removeChild(holder);
          resolve(false);
        }
        root.render(<IosPayDialog isOpen={true} onConfirm={onComfirm} onClose={onCancel} title={title} desc={desc} isShowCancelBtn={isShowCancelBtn} comfirmBtn={comfirmBtn} url={url} type={type}/>);
      })
    },
    hide: () => {
      // if (holder) root.unmount();
    }
  }
}

export default useIosPayDialog
