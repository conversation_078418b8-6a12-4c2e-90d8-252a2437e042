"use client";

import React, { useContext, useState } from 'react';
import type { FC, FormEvent } from 'react'
import Modal from '@little-tavern/shared/src/ui/dialog/Modal';
import Image from 'next/image';
import Toast from '@little-tavern/shared/src/ui/toast';
import Link from 'next/link';
import useRequest from '@little-tavern/shared/src/hook/useRequest';
import { Trans, useTranslation } from 'react-i18next'
import { web, tgWeb } from '@/app/module/global'
import useDialogAlert from '@little-tavern/shared/src/hook/useDialogAlert'

type IProps = {
  onClose: Function,
  isOpen: boolean,
}
const { notify } = Toast
const DialogUSDT: FC<IProps> = ({ onClose, isOpen }) => {
  const { t } = useTranslation()
  const [errMsg, setErrMsg] = useState('');
  const dialogAlert = useDialogAlert();
  const [formData, setFormData] = useState({
    password: '',
  });
  const request = useRequest();
  const handleChange = (e: any) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    setErrMsg('')
  };
  const onExchange = async (e: FormEvent) => {
    e.preventDefault();
    try{
      const hideLoading = Toast.showLoading(t('app.pay.exchanging'));
      const res = await request('/package_voucher/redeem', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({voucher_code: formData.password})
      });
      hideLoading();
      setFormData({
        password: '',
      })
      onClose(res)
    } catch(e) {
      notify({ type: 'error', message: t('app.pay.sorry_login_desc') })
      setErrMsg(String(e))
      console.error('Error logging in:', e);
    }
  }
  const exchangeGuide = async () => {
      const res = await dialogAlert.show({
        title: t('app.pay.exchange_guide_title'),
        desc: t('app.pay.exchange_guide_desc'),
        isShowCancelBtn: true,
        comfirmBtn: t('app.pay.view_btn'),
        alertStatus: 2
      })
      if(res) {
        window.open('/exchage_guide.png')
      }
    }
  return (
    <Modal onClose={() => {onClose()}} isOpen={isOpen}>
      <div className='min-h-20 mb-2'>
        <div className="flex min-h-full flex-1 flex-col justify-center px-6 pt-5 pb-5">
        <div className="sm:mx-auto sm:w-full sm:max-w-sm">
          <h2 className="text-center text-xl font-bold leading-6 tracking-tight">
            {t('app.pay.card_active')}
          </h2>
        </div>

        <div className="mt-3 sm:mx-auto sm:w-full sm:max-w-sm">
          <form className="space-y-6" action="#" method="POST" onSubmit={onExchange}>
            <div>
              <div className="text-sm">
                <label htmlFor="password" className="block font-medium leading-6 ">
                  {t('app.pay.input_code')}
                </label>
                <Link className='text-blue-500' target='_blank' href='https://www.sdfkw.xyz/links/1275C4C5'>
                <Trans i18nKey="app.pay.purchase_tips" t={t}>
                  （没有卡密？点击<span className='underline'>这里</span>购买）
                </Trans>
                </Link>
                {!web && <button onClick={exchangeGuide} className='underline text-blue-500'>{t('app.pay.exchange_btn')}</button>}
                {/* <div className="text-sm">
                  <a href="#" className="font-semibold text-indigo-400 hover:text-indigo-500">
                    Forgot password?
                  </a>
                </div> */}
              </div>
              <div className="mt-2">
                <textarea
                  id="password"
                  name="password"
                  autoComplete="password"
                  value={formData.password}
                  onChange={handleChange}
                  placeholder={t('app.pay.input_code_placeholder')}
                  required
                  className="py-2.5 ipt placeholder:text-sm"
                />
              </div>
              {errMsg && <p className='text-xs mt-1 text-red-500'>{errMsg}</p>}
            </div>

            <div className='pt-3'>
              <button
                type="submit"
                className="flex w-full justify-center rounded-md px-3 py-2.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-purple-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 bg-purple-600 text-white"
              >{t('app.pay.active')}</button>
            </div>
          </form>
          {/* <p className="mt-2 text-center text-sm text-gray-500">
            没有卡密？
            <Link href="#" className="font-semibold underline leading-6 text-indigo-400 hover:text-indigo-500">
              点击购买
            </Link>
          </p> */}
        </div>
      </div>
      </div>
    </Modal>
  );
};

export default DialogUSDT;
