import React from 'react'
import serverFetch from '@share/src/module/serverFetch'
import Main from './index'
import { switchLanguages } from '@little-tavern/shared/src/i18n/settings'

export function generateStaticParams() {
  return switchLanguages.map((lang) => ({
    lang,
  }))
}

const App = async ({ params }: { params: { lang: string } }) => {
  // const res = await serverFetch({
  //   url: '/recharge/list'
  // })

  return (
    // <Main products={res?.products} />
    <Main products={[]} />
  )
}

export default React.memo(App)
