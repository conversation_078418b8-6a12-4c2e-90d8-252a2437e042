"use client";

import React, { useContext, useState } from 'react';
import type { FC, FormEvent } from 'react'
import Modal from '@little-tavern/shared/src/ui/dialog/Modal';
import { useTranslation } from 'react-i18next'
import ReactMarkdown from 'react-markdown';

type IProps = {
  onClose: Function,
  isOpen: boolean,
  onChannel1: Function,
  onChannel2: Function,
  title: string,
  desc: string,
  btn1: string,
  btn2: string,
  subTitle?: string,
  isMd?: boolean,
  btn1_subtitle?: string,
  btn2_subtitle?: string
}
const PayChannel: FC<IProps> = ({ onClose, isOpen, onChannel1, onChannel2, title, desc, btn1, btn2, subTitle, isMd = false, btn1_subtitle, btn2_subtitle }) => {
  const { t } = useTranslation()
  
  return (
    <Modal onClose={() => {onClose()}} isOpen={isOpen}>
      <div className='min-h-20 mb-2'>
        <div className="flex min-h-full flex-1 flex-col justify-center px-6 pt-3 pb-1">
        <div className="sm:mx-auto sm:w-full sm:max-w-sm">
          <h2 className="text-center text-xl font-bold leading-6 tracking-tight">
            {title}
            {subTitle && <p className="text-center text-sm font-normal">
              {subTitle}
            </p>}
          </h2>
          <div className='text-sm mt-2'>{isMd ? <ReactMarkdown components={{ a: ({node, ...props}) => <a target="_blank" className='text-blue-500 underline' rel="" {...props} /> }}>{desc}</ReactMarkdown> : desc}</div>
        </div>

        <div className="mt-5 flex justify-center space-x-2 ">
            <button
              type="submit"
              className="px-5 w-full justify-center rounded-md px-3 py-2.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-purple-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 bg-purple-600 text-white"
              onClick={() => {onChannel1()}}
            >
              <span className='text-base'>{btn1}</span>
              <span className='text-xs block'>{btn1_subtitle}</span>
            </button>
            <button
              type="submit"
              className="px-5 w-full justify-center rounded-md px-3 py-2.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-purple-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 bg-purple-600 text-white"
              onClick={() => {onChannel2()}}
            >
              <span className='text-base'>{btn2}</span>
              <span className='text-xs block'>{btn2_subtitle}</span>
            </button>
        </div>
      </div>
      </div>
    </Modal>
  );
};

export default PayChannel;
