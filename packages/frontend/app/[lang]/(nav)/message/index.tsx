'use client'
import React, { useCallback, useContext, useEffect, useState, ReactNode } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useParams } from 'next/navigation'
import styles from './style.module.css'
import Loader from '@little-tavern/shared/src/ui/Loading'
import Footer from "@/app/[lang]/components/footer";
import { AuthContext } from '@little-tavern/shared/src/authContext'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import Link from 'next/link'
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import { Markdown } from '../../components/markdown';
import './style.scss'
import Back from '@little-tavern/shared/src/ui/Back'

interface FAQItem {
  id: string
  question: string
  answer: any
}

const Pay = () => {
  const { t } = useTranslation()
  const params = useParams()
  const lang = params.lang as string
  const auth = useContext(AuthContext);
  const request = useRequest();
  const [activeQuestion, setActiveQuestion] = useState('');
  
  useEffect(() => {
    const hash = window.location.hash;
    if (hash) {
      setActiveQuestion(hash.slice(1));
      const element = document.querySelector(hash);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }, []);
  useEffect(() => {
    if(activeQuestion) {
      const element = document.querySelector(`#${activeQuestion}`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }, [activeQuestion]);


  // const faqData: FAQItem[] = [
  //   {
  //     id: 'q1',
  //     question: '1. 状态栏如何填写',
  //     answer: <StatusComponent />,
  //   },
  //   {
  //     id: 'q2',
  //     question: '2. 我们使用什么模型',
  //     answer: '我们使用什么模型',
  //   }
  // ];
  const faqData: FAQItem[] = [
    {
      id: 'q1',
      question: t('app.faq.q1_title'),
      answer: t('app.faq.q1_desc'),
    },
    {
      id: 'q2',
      question: t('app.faq.q2_title'),
      answer: t('app.faq.q2_desc'),
    },
    {
      id: 'q3',
      question: t('app.faq.q3_title'),
      answer: t('app.faq.q3_desc'),
    },
    {
      id: 'q4',
      question: t('app.faq.q4_title'),
      answer: t('app.faq.q4_desc'),
    },
    {
      id: 'q5',
      question: t('app.faq.q5_title'),
      answer: t('app.faq.q5_desc'),
    },
    {
      id: 'q6',
      question: t('app.faq.q6_title'),
      answer: t('app.faq.q6_desc'),
    },
  ];

  const toggleAnswer = (id: string) => {
    setActiveQuestion(activeQuestion === id ? '' : id);
  };
  return (
    <>
      <main className="main-height w-full con-width">
        <Back hideTxt={true} />
        <div className='px-3'>
          <h1 className='text-base py-1'>{t('app.common.message')}</h1>
          {faqData.map((item, index) => (
            <div key={item.id} id={item.id} className=''>
              <h2
                className='flex items-center my-3 cursor-pointer'
                onClick={() => toggleAnswer(item.id)}
              >
                <ChevronDownIcon
                    className={cn("mr-1 h-5 w-5 transition -rotate-90", activeQuestion === item.id && "rotate-0")}
                    aria-hidden="true"
                  />
                {++index}. {item.question}
                
              </h2>
              {activeQuestion === item.id && (
                <div className='mt-2 rounded-md p-3 bg-white dark:bg-gray-900 dark:text-gray-100 text-sm'>
                  {item.answer}
                </div>
              )}
            </div>
          ))}
        </div>
      </main>
      <Footer cl='justify-center ml-0 !m-auto left-0 right-0 w-full'/>
    </>
  )
}

export default React.memo(Pay)

const StatusComponent = () => {
  return <div>xxx</div>
}