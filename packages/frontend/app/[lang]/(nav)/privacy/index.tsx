'use client'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useParams, useRouter } from 'next/navigation'
import Footer from "@/app/[lang]/components/footer";
import { Markdown } from '../../components/markdown';
import Back from '@little-tavern/shared/src/ui/Back'

const Pay = () => {
  const { t } = useTranslation()
  const [content, setContent] = useState('');
  useEffect(() => {
    setContent(t('app.privacy.con'));
  })

  return (
    <>
      <main className="main-height w-full con-width px-3 text-base">
        <Back />
        <div className={cn('prose dark:prose-invert w-full sm:w-[768px] mx-auto pt-1 text-base')}>{content && <Markdown content={content} isPage={true} />}</div>
        {content && <Footer cl='justify-center ml-0 !m-auto left-0 right-0 w-full'/>}
      </main>
    </>
  )
}

export default React.memo(Pay)

