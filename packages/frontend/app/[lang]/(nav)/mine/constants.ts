'use client'

import { TFunction } from 'i18next'

export interface TabItem {
  key: string
  title: string
  link: string
}

export const getMineTabs = (t: TFunction, lang: string): TabItem[] => [
  {
    key: 'gift',
    title: t('app.mine.benefits'),
    link: `/${lang}/mine/gift/free`
  },
  {
    key: 'share',
    title: t('app.mine.my_share'),
    link: `/${lang}/mine/share`
  },
  {
    key: 'fav',
    title: t('app.mine.fav'),
    link: `/${lang}/mine/fav`
  },
  {
    key: 'history',
    title: t('app.mine.diamond_record'),
    link: `/${lang}/mine/history`
  },
  {
    key: 'setting',
    title: t('app.mine.setting'),
    link: `/${lang}/mine/setting`
  }
]
