'use client'
import React, { useCallback, useContext, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useParams, useRouter } from 'next/navigation'
import { AuthContext } from '@little-tavern/shared/src/authContext'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import useSWRImmutable, { mutate } from 'swr';
import Card from '@little-tavern/shared/src/ui/card'
import debounce from 'lodash.debounce';
import { LoadingToast, LoadingToastFailed } from '@little-tavern/shared/src/ui/Loading'

const limit = 200;
const url = `/user/favorite/roles`;
const Fav = () => {
    const { t } = useTranslation()
    const params = useParams()
    const auth = useContext(AuthContext);
    const userInfo = auth?.user;
    const request = useRequest();
    const [offset, setOffset] = useState(0);
    const { data, isLoading, error, mutate } = useSWRImmutable(userInfo?.id ? `${url}?offset=${offset}&limit=${limit}` : null, request, {
        revalidateOnFocus: false
    });
    const [list, setList] = useState<any>(data?.data?.list || [])
    const fetchFavlist = useCallback(async () => {
        setOffset(0)
        mutate(url);
    }, [])

    useEffect(() => {
        if (data?.data?.list?.length >= 0) {
            setList([...(offset === 0 ? [] : list), ...data.data.list])
        }
    }, [data]);

    let hasMore = true;
    if (data?.total) {
        hasMore = offset < data.total - limit;
    }
    const loadMore = () => {
        if (!hasMore || isLoading) return;
        !error && setOffset((offset + limit))
    };
    const handleScroll = debounce(() => {
        if (
            window.innerHeight + document.documentElement.scrollTop + 800 >
            document.documentElement.offsetHeight
        ) {
            loadMore();
        }
    }, 50); // 50ms 的防抖延迟
    // 初始化时监听滚动事件
    // useEffect(() => {
    //   window.addEventListener('scroll', handleScroll);
    //   return () => { window.removeEventListener('scroll', handleScroll); }
    // }, [handleScroll]);
    const retry = () => {
        mutate()
    }

    return (
        <div className='px-2 overflow-y-auto'>
            <div className="px-2 pt-2 pb-2 grid grid-cols-1 sm:grid-cols-2 gap-2 lg:gap-3 lg:grid-cols-4 xl:grid-cols-5">
                {
                    list.length > 0 ?
                        list.map((role: any) => {
                            return <Card key={role.mode_target_id} modeType={role.mode_type} reflesh={fetchFavlist} role={{ ...(role.mode_type === 'single' ? role.role : role.group) }}></Card>
                        }) :
                        <>{userInfo?.id !== undefined && data?.data?.list?.length == 0 && !isLoading && <p className="mt-5 text-xs leading-6 text-gray-400">{t('app.mine.fav_role_tip')}</p>}</>
                }
            </div>
            {
                <div className={cn((isLoading || !hasMore) && offset != 0 ? 'opacity-100' : 'opacity-0',
                    'h-6'
                )}>
                    {isLoading && <p className={'text-center text-xs mb-1'}>{t('app.index.loading')}...</p>}
                    {!hasMore && !isLoading && <p className='text-center text-xs mb-1'>{t('app.index.no_card_load')}</p>}
                </div>
            }
            {userInfo?.id === undefined || isLoading && offset == 0 && <LoadingToast msg={t('app.common.loading')} />}
            {error && !isLoading && <LoadingToastFailed retry={retry} error={error} />}
        </div>
    )
}

export default Fav