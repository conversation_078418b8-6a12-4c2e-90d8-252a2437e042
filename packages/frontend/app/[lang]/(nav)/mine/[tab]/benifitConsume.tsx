'use client'
import Toast from "@share/src/ui/toast"
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams, useRouter } from 'next/navigation'
import { AuthContext } from '@little-tavern/shared/src/authContext'
import cn from 'classnames'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import useSWRImmutable, { mutate } from 'swr';
import debounce from 'lodash.debounce';
import { LoadingToast, LoadingToastFailed } from '@little-tavern/shared/src/ui/Loading'
import format from '@/app/module/formate-date'

const limit = 30;
const url = `/user/benefit_consumption_history`;
const BenifitConsume = ({containerRef}: {containerRef: React.RefObject<HTMLDivElement>}) => {
    const { t } = useTranslation()
    const params = useParams()
    const auth = useContext(AuthContext);
    const isLogin = auth?.isLogin;
    const userInfo = auth?.user;
    const request = useRequest();
    const [offset, setOffset] = useState(0);
    const { data, isLoading, error, mutate } = useSWRImmutable(userInfo?.id ? `${url}?offset=${offset}&limit=${limit}` : null, request, {
        revalidateOnFocus: false
    });
    const [list, setList] = useState<any>(data?.data?.list || [])
    const [newList, setNewList] = useState<{ day: string; list: any[] }[]>([])
    const fetchFavlist = useCallback(async () => {
        setOffset(0)
        mutate(url);
    }, [])

    useEffect(() => {
        if (data?.data?.list?.length >= 0) {
            const updatedList = [...(offset === 0 ? [] : list), ...data.data.list];
            setList(updatedList);
            
            // Group by date
            const groupedData = updatedList.reduce((acc: { [key: string]: any[] }, item: any) => {
                const date = new Date(item.created_at * 1000).getTime();
                const day = format(date, t('app.mine.date_format'));
                if (!acc[day]) {
                    acc[day] = [];
                }
                acc[day].push(item);
                return acc;
            }, {});

            // Transform to array format and sort by date
            const formattedList = Object.entries(groupedData)
                .map(([day, items]) => ({
                    day,
                    list: items
                }))
                .sort((a, b) => new Date(b.day).getTime() - new Date(a.day).getTime());

            setNewList(formattedList);
        }
    }, [data]);
    let hasMore = true;
    if (data?.data?.total) {
        hasMore = offset < data.data.total - limit;
    }
    const loadMore = () => {
        if (!hasMore || isLoading) return;
        !error && setOffset((offset + limit))
    };
    const handleScroll = debounce(() => {
        if (
            containerRef?.current && containerRef.current?.clientHeight + containerRef.current?.scrollTop + 800 >
            containerRef.current?.scrollHeight
        ) {
            loadMore();
        }
    }, 50); // 50ms 的防抖延迟
    // 初始化时监听滚动事件
    useEffect(() => {
      const currentContainer = containerRef?.current;
        
        if (currentContainer) {
            currentContainer.addEventListener('scroll', handleScroll);
        }
        
        return () => {
            currentContainer?.removeEventListener('scroll', handleScroll);
        }
    }, [handleScroll]);
    const retry = () => {
        mutate()
    }
    return (
        <>
        <div className="px-2 sm:justify-normal w-fit">
            {
                <table className='dark:bg-gray-900 bg-white text-gray-500 text-sm border-collapse w-full  sm:w-[720px]'>
                <thead className='dark:bg-slate-700 bg-slate-300 dark:text-slate-300'>
                    <tr className='text-center text-xs sm:text-sm'>
                        <th className='border py-2 px-1 border-slate-600'>{t('app.mine.type')}</th>
                        <th className='border py-2 px-4 border-slate-600'>{t('app.mine.card_name')}</th>
                        <th className='border py-2 px-4 border-slate-600'>{t('app.mine.card_id')}</th>
                        <th className='border py-2 px-4 border-slate-600'>{t('app.mine.chat_mode')}</th>
                        <th className='border py-2 px-3 border-slate-600'>{t('app.mine.consume_diamond')}</th>
                        <th className='border py-2 px-3 border-slate-600'>{t('app.mine.diamond_consume_time')}</th>
                    </tr>
                </thead>
                <tbody>
                    {newList.length > 0 ?
                    newList?.map((group, index) => (
                        <React.Fragment key={index}>
                            <tr className='text-center text-xs sm:text-base bg-slate-200 dark:bg-slate-800'>
                                <td className='border border-slate-700 py-2 px-1 dark:text-white' colSpan={6}>{group.day}</td>
                            </tr>
                            {group.list?.map((item: any, index: number) => (
                                <tr key={index} className='text-center text-xs sm:text-base'>
                                    <td className='border border-slate-700 py-1 px-1'>{item.type}</td>
                                    <td className='border border-slate-700 py-1 px-1'>{item.role_name || '/'}</td>
                                    <td className='border border-slate-700 py-1 px-1'>{item.role_id}</td>
                                    <td className='border border-slate-700 py-1 px-1'>{item.chat_desc || '/'}</td>
                                    <td className='border border-slate-700 py-1 px-1'>{item.amount}</td>
                                    <td className='border border-slate-700 py-1 px-1'>{format(item.created_at * 1000, 'YYYY/MM/DD  HH:mm:ss')}</td>
                                </tr>
                            ))}
                        </React.Fragment>
                    )) :
                    <>{userInfo?.id !== undefined && data?.data?.list?.length == 0 && !isLoading && <tr className='text-center text-xs sm:text-base bg-slate-200 dark:bg-slate-800'>
                        <td className='border border-slate-700 py-2 px-1 dark:text-white' colSpan={6}>{t('app.mine.no_record')}</td>
                    </tr>}</>}
                </tbody>
            </table>
            }
            {
            <div className={cn((isLoading || !hasMore) && offset != 0 ? 'opacity-100' : 'opacity-0',
                'h-6 mt-2 text-gray-600 dark:text-white'
            )}>
                {isLoading && <p className={'text-center text-xs mb-1'}>{t('app.index.loading')}...</p>}
                {!hasMore && !isLoading && <p className='text-center text-xs mb-1'>{t('app.mine.no_more_list')}</p>}
            </div>
            }
            {userInfo?.id === undefined || isLoading && offset == 0 && <LoadingToast msg={t('app.common.loading')} />}
            {error && !isLoading && <LoadingToastFailed retry={retry} error={error} />}

            <div className="text-xs text-gray-400">{t('app.mine.history_tips')}</div>
        </div>
        </>
    )
}

export default BenifitConsume