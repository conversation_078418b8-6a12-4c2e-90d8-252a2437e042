'use client'
import React, { useCallback, useContext, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useParams, useRouter } from 'next/navigation'
import { AuthContext } from '@little-tavern/shared/src/authContext'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import { Switch } from '@headlessui/react'
import Toast from '@share/src/ui/toast'
import updateSetting from '@share/src/module/updateSetting'
import { LightBulbIcon } from '@heroicons/react/24/solid'

const Setting = () => {
    const { t } = useTranslation()
    const params = useParams()
    const auth = useContext(AuthContext);
    const user = auth?.user;
    const request = useRequest();

    useEffect(() => {

    }, []);
    const toggleChatTips = () => {
        updateSetting({
            data: {
                show_chat_tips: !user?.show_chat_tips
            },
            request,
            auth,
            t
        })
    }
    const toggleUseCommonBg = () => {
        updateSetting({
            data: {
                use_personal_bg: !user?.use_personal_bg
            },
            request,
            auth,
            t
        })
    }
    const toggleStatusBar = () => {
        updateSetting({
            data: {
                status_block_switch: !user?.status_block_switch
            },
            request,
            auth,
            t
        })
    }
    return (
        <div className='mx-2 px-3 mb-2 py-3 bg-white dark:bg-gray-900 rounded'>
            <div className="px-2 py-1 sm:w-[500px] flex flex-wrap gap-3 justify-center sm:justify-normal">
                <div className="rounded-md w-full p-3 border dark:border-gray-500 dark:bg-gray-900 bg-white">
                    <div className='flex items-center justify-between'>
                        <div className='flex items-center'>
                            <span className='text-sm'>{t('app.mine.show_chat_tips')}</span>
                        </div>
                        <Switch
                            id='nsfw'
                            checked={user?.show_chat_tips}
                            onClick={toggleChatTips}
                            className="group inline-flex h-5 w-10 items-center rounded-full bg-gray-300 dark:bg-gray-500 transition data-[checked]:bg-purple-500 dark:data-[checked]:bg-purple-500 mr-1"
                        >
                            <span className="size-4 translate-x-0.5 rounded-full bg-white transition group-data-[checked]:translate-x-5" />
                        </Switch>
                    </div>
                </div>
                <div className="rounded-md w-full p-3 border dark:border-gray-500 dark:bg-gray-900 bg-white">
                    <div className='flex items-center justify-between'>
                        <div className='flex items-center'>
                            <span className='text-sm'>{t('app.mine.status_bar')}</span>
                        </div>
                        <Switch
                            id='statusBar'
                            checked={user?.status_block_switch}
                            onClick={toggleStatusBar}
                            className="group inline-flex h-5 w-10 items-center rounded-full bg-gray-300 dark:bg-gray-500 transition data-[checked]:bg-purple-500 dark:data-[checked]:bg-purple-500 mr-1"
                        >
                            <span className="size-4 translate-x-0.5 rounded-full bg-white transition group-data-[checked]:translate-x-5" />
                        </Switch>
                    </div>
                </div>
                {/* <div className="rounded-md w-full p-3 border dark:border-gray-500 dark:bg-gray-900 bg-white">
                    <div className='flex items-center justify-between'>
                        <div className='flex items-center'>
                            <span className='text-sm'>{t('app.mine.use_common_bg')}</span>
                        </div>
                        <Switch
                            id='chat_bg'
                            checked={user?.use_personal_bg}
                            onClick={toggleUseCommonBg}
                            className="group inline-flex h-5 w-10 items-center rounded-full bg-gray-300 dark:bg-gray-500 transition data-[checked]:bg-purple-500 dark:data-[checked]:bg-purple-500 mr-1"
                        >
                            <span className="size-4 translate-x-0.5 rounded-full bg-white transition group-data-[checked]:translate-x-5" />
                        </Switch>
                    </div>
                </div> */}
            </div>
            
        </div>
    )
}

export default Setting