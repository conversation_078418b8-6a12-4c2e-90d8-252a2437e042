import React from 'react'
import Main from './index'

const App = ({params}: any) => {
  return (
    <Main params={params} />
  )
}
export async function generateStaticParams() {
  const tabs = [
    {
      tab: 'share'
    },
    {
      tab: 'fav'
    },
    {
      tab: 'history'
    },
    {
      tab: 'gift',
      subtab: 'recharge'
    },
    {
      tab: 'gift',
      subtab: 'give'
    },
    {
      tab: 'gift',
      subtab: 'free'
    },
    {
      tab: 'setting'
    }
  ]
  
  return tabs
}
export default React.memo(App)
