'use client'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams, useRouter } from 'next/navigation'
import Footer from "@/app/[lang]/components/footer";
import { AuthContext } from '@little-tavern/shared/src/authContext'
import PersonSetting from '@/app/[lang]/components/PersonSetting';
import Loader, { LoadingToast, LoadingToastFailed } from '@little-tavern/shared/src/ui/Loading'
import ShareList from '@/app/[lang]/components/share-list/shareList';
import Tabs from '@/app/[lang]/components/tabs';
import cn from 'classnames'
import Give from './give';
import Free from './free';
import Recharge from './recharge';
import { getMineTabs } from '../../constants'

const Main = ({params: {lang, tab, subtab}}: any) => {
  const { t } = useTranslation()
  const auth = useContext(AuthContext);
  const userInfo = auth?.user;
  const tabs = getMineTabs(t, lang)
  
  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);
  // console.log('userInfo?.id', userInfo?.id, mounted, tab, subtab);
  return (
    <>
        <Tabs tabs={tabs}></Tabs>
        {!mounted ? <Loader msg={t('app.common.loading')} /> : (
          userInfo?.id == undefined ? <Loader msg={t('app.common.loading')} /> : <>
            {
              tab === tabs[0].key && subtab === 'recharge' && <><Recharge></Recharge></>
            }
            {
              tab === tabs[0].key && subtab === 'give' && <><Give></Give></>
            }
            {
              tab === tabs[0].key && subtab === 'free' && <><Free></Free></>
            }
          </>
        )}
    </>
  )
}

export default Main
