'use client'
import Toast from "@share/src/ui/toast"
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams, useRouter } from 'next/navigation'
import { AuthContext } from '@little-tavern/shared/src/authContext'
import cn from 'classnames'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import useSWRImmutable, { mutate } from 'swr';
import Card from '@little-tavern/shared/src/ui/card'
import debounce from 'lodash.debounce';
import { LoadingToast, LoadingToastFailed } from '@little-tavern/shared/src/ui/Loading'
import format from '@/app/module/formate-date'

const HistoryRecharge = () => {
    const { t } = useTranslation()
    const params = useParams()
    const auth = useContext(AuthContext);
    const isLogin = auth?.isLogin;
    const userInfo = auth?.user;
    const request = useRequest();
    const [chargeHistory, setChargeHistory] = useState<any>([])
    const chargeHistoryRef = useRef<any>(null)
    useEffect(() => {
        const searchParam = new URLSearchParams(location.search)
        // s=h，滚动到充值历史
        if (chargeHistoryRef.current && searchParam.get('s') == 'h') {
            console.log('chargeHistoryRef.current', chargeHistoryRef.current);
            setTimeout(() => {
                chargeHistoryRef.current.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 100)
        }
    }, [chargeHistory])
    const fetchBanlance = async () => {
        const hideLoading = Toast.showLoading(t('app.common.loading'));
        const res = await request('/recharge/balance');
        hideLoading()
        setChargeHistory(res.recharge_orders);
    }
    useEffect(() => {
        isLogin && fetchBanlance();
    }, [isLogin])
    return (
        <div className="sm:w-[720px] mx-2" ref={chargeHistoryRef}>
                    <table className='dark:bg-gray-900 bg-white text-gray-500 text-sm border-collapse w-full  sm:w-[720px]'>
                        <thead className='dark:bg-slate-700 bg-slate-300 dark:text-slate-300'>
                            <tr className='text-center text-xs sm:text-sm'>
                                <th className='border py-2 px-1 border-slate-600'>{t('app.pay.amount')}</th>
                                <th className='border py-2 px-4 border-slate-600'>{t('app.pay.diamond')}</th>
                                <th className='border py-2 px-4 border-slate-600'>{t('app.pay.balance')}</th>
                                <th className='border py-2 px-4 border-slate-600'>{t('app.pay.explain')}</th>
                                <th className='border py-2 px-3 border-slate-600'>{t('app.pay.create_time')}</th>
                                <th className='border py-2 px-3 border-slate-600'>{t('app.pay.expire_time')}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {chargeHistory?.map((item: any) => (
                                <tr key={item.order_id} className='text-center text-xs sm:text-base'>
                                    <td className='border border-slate-700 py-1 px-1'>{(item.pay_fee / 1000 / 100).toFixed(4)}</td>
                                    <td className='border border-slate-700 py-1 px-1'>{item.amount}</td>
                                    <td className='border border-slate-700 py-1 px-1'>{item.balance}</td>
                                    <td className='border border-slate-700 py-1 px-1'>{item.desc}</td>
                                    <td className='border border-slate-700 py-1 px-1'>{format(item.created_at * 1000, 'YYYY/MM/DD HH:mm:ss')}</td>
                                    <td className='border border-slate-700 py-1 px-1'>{item.expire_at ? format(item.expire_at * 1000, 'YYYY/MM/DD HH:mm:ss') : t('app.pay.permanent_validity')}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
    )
}

export default HistoryRecharge