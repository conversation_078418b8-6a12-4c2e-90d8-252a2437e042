'use client'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useParams, useRouter } from 'next/navigation'
import { AuthContext } from '@little-tavern/shared/src/authContext'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import useSWRImmutable, { mutate } from 'swr';
import Card from '@little-tavern/shared/src/ui/card'
import debounce from 'lodash.debounce';
import { LoadingToast, LoadingToastFailed } from '@little-tavern/shared/src/ui/Loading'
import Toast from '@share/src/ui/toast'
import format from '@/app/module/formate-date'
import HistoryRecharge from './historyRecharge'
import HistoryConsume from './historyConsume'
import HistoryExpire from './historyExpire'
import BenifitConsume from './benifitConsume'
import SubTabs from './subTabs'

const History = () => {
  const { t } = useTranslation()
  const params = useParams()
  const auth = useContext(AuthContext);
  const isLogin = auth?.isLogin;
  const userInfo = auth?.user;
  const request = useRequest();
  const [selectedIndex, setSelectedIndex] = useState(0)

  const tabs = [
    {
      title: t('app.pay.recharge_history'),
    },
    {
      title: t('app.mine.consume_record'),
    },
    {
      title: t('app.mine.diamond_expire'),
    },
    {
      title: t('app.mine.benifit_consume'),
    }
  ]
  const containerRef = useRef<HTMLDivElement>(null);
  const clickTabHandler = (index: number) => {
    setSelectedIndex(index)
  }
  return (
    <div className='overflow-y-auto' ref={containerRef}>
      <SubTabs tabs={tabs} clickTabHandler={clickTabHandler} selectedIndex={selectedIndex} />
      {selectedIndex == 0 && <HistoryRecharge />}
      {selectedIndex == 1 && <HistoryConsume containerRef={containerRef} />}
      {selectedIndex == 2 && <HistoryExpire containerRef={containerRef} />}
      {selectedIndex == 3 && <BenifitConsume containerRef={containerRef} />}
    </div>
  )
}

export default History