import cn from 'classnames'

const SubTabs = ({tabs, clickTabHandler, selectedIndex}: any) => {
  return (
    <>
        <div className='mx-2'>
                <div className='pb-1'>
                    {tabs.map((item: any, index: number) => {
                        return <button key={index} type='button' onClick={() => { clickTabHandler(index) }} className={cn('mr-4 text-sm mb-1', selectedIndex == index && 'border-b-2 border-purple-500')}>{item.title}</button>
                    })}
                </div>
            </div>
    </>
  )
}

export default SubTabs