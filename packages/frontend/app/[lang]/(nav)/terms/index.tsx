'use client'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useParams, useRouter } from 'next/navigation'
import Footer from "@/app/[lang]/components/footer";
import { Markdown } from '../../components/markdown';
import {ArrowUturnLeftIcon, CheckCircleIcon} from '@heroicons/react/24/solid'
import Back from '@little-tavern/shared/src/ui/Back'

const Pay = () => {
  const { t } = useTranslation()
  const [content, setContent] = useState('');
  const params = useParams()
  const lang = params.lang as string
  useEffect(() => {
    setContent(t('app.terms.con', {mail: process.env.NEXT_PUBLIC_SUPPORT_MAIL}));
  })

  return (
    <>
      <main className="main-height w-full con-width px-3 text-base">
        <Back />
        <div className={cn('prose dark:prose-invert w-full sm:w-[768px] mx-auto pt-1 text-base')}>{content && <Markdown content={content} isPage={true} />}</div>
        {content && <Footer cl='justify-center ml-0 !m-auto left-0 right-0 w-full'/>}
      </main>
    </>
  )
}

export default React.memo(Pay)

