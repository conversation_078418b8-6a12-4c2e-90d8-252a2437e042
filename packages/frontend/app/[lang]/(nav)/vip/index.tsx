'use client'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useParams, useRouter } from 'next/navigation'
import Footer from "@/app/[lang]/components/footer";
import { Markdown } from '../../components/markdown';
import { CheckIcon } from '@heroicons/react/20/solid';
import { Radio, RadioGroup } from '@headlessui/react'
import { CheckCircleIcon } from '@heroicons/react/24/solid'
import s from '@/app/[lang]/globals.module.css'

const plans = [
  { name: 'Startup', ram: '12GB', cpus: '6 CPUs', disk: '256GB SSD disk' },
  { name: 'Business', ram: '16GB', cpus: '8 CPUs', disk: '512GB SSD disk' },
  { name: 'Enterprise', ram: '32GB', cpus: '12 CPUs', disk: '1TB SSD disk' },
]
const feature = [
  'Unlimited messages with all characters and bots',
  'Create characters using advanced models like GPT-4o',
  'Unlimited access to audio messages, and audio generation toggle of italicized text',
  'Free to choose character models during chat',
  'Other advanced creator tools, including GPT-4 Turbo, DALL·E 3, Midjourney, etc.',
  'Creator Certification Mark and priority access to new features',
  'Memory length of 16K'
];
const btn = cn(s.primBtn, 'text-sm')
const Pay = () => {
  const { t } = useTranslation()
  const [selected, setSelected] = useState(plans[0])
  useEffect(() => {
    
  })

  return (
    <>
      <main className="main-height con-width">
        <div className='mx-3 my-5 border rounded-lg p-3 border-gray-500 bg-gray-950'>
        <RadioGroup value={selected} onChange={setSelected} aria-label="Server size" className="space-y-2">
          {plans.map((plan) => (
            <Radio
              key={plan.name}
              value={plan}
              className="group relative flex cursor-pointer rounded-lg bg-white/5 py-4 px-5 text-white shadow-md transition focus:outline-none data-[focus]:outline-1 data-[focus]:outline-white data-[checked]:bg-white/10"
            >
              <div className="flex w-full items-center justify-between">
                <div className="text-sm/6">
                  <p className="font-semibold text-white">{plan.name}</p>
                  <div className="flex gap-2 text-white/50">
                    <div>{plan.ram}</div>
                    <div aria-hidden="true">&middot;</div>
                    <div>{plan.cpus}</div>
                    <div aria-hidden="true">&middot;</div>
                    <div>{plan.disk}</div>
                  </div>
                </div>
                <CheckCircleIcon className="size-6 fill-white opacity-0 transition group-data-[checked]:opacity-100" />
              </div>
            </Radio>
          ))}
        </RadioGroup>
        <ul className='my-5 px-1 text-sm space-y-2 dark:text-gray-300'>
            {
              feature.map(feature => {
                    return <li key={feature} className='flex'>
                      <CheckIcon className='w-5 h-5 inline-block mx-1 text-purple-500' />
                      <p className='flex-1'>{feature}</p></li>
                })
            }
        </ul>
        <button onClick={() => {}} className={cn(btn, '!py-1.5 w-full !box-border !py-2')}>订阅</button>
        </div>
        <div className='mx-3 my-5 border rounded-lg p-3 border-gray-500 bg-gray-950'>
        <h3>Free Plan for All Users</h3>
        <ul className='my-5 px-1 text-sm space-y-2 dark:text-gray-300'>
            {
              feature.map(feature => {
                    return <li key={feature} className='flex'>
                      <CheckIcon className='w-5 h-5 inline-block mx-1 text-purple-500' />
                      <p className='flex-1'>{feature}</p></li>
                })
            }
        </ul>
        <button disabled onClick={() => {}} className={cn('!py-1.5 w-full !box-border !py-2 bg-gray-800 rounded-lg text-sm text-gray-400')}>已订阅</button>
        </div>
      </main>
    </>
  )
}

export default React.memo(Pay)

