'use client'
import React, { use, useCallback, useContext, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useParams, useRouter } from 'next/navigation'
import Card from '@little-tavern/shared/src/ui/card'
import Footer from '../../../components/footer'
import { AuthContext } from '@little-tavern/shared/src/authContext'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import useSWRImmutable, { mutate } from 'swr';
import useDialogAlert from '@little-tavern/shared/src/hook/useDialogAlert'
import { LoginStatus } from '@little-tavern/shared/src/authContext';
import useDialogLogin from '@little-tavern/shared/src/hook/useDialogLogin'
import usePagesState from '@little-tavern/shared/src/hook/usePageState'
import debounce from 'lodash.debounce';
import Loader, {LoadingToast, LoadingToastFailed} from '@little-tavern/shared/src/ui/Loading'
import Share from '@/app/[lang]/components/share'

const limit = 16;
const Main = ({params: {lang, tab}}: any) => {
  const { t } = useTranslation()
  const params = useParams()
  const auth = useContext(AuthContext);
  const request = useRequest();
  const userInfo = auth?.user;
  const [offset, setOffset] = useState(0);
  const [firstLoading, setFirstLoading] = useState(true);
  
  const { data, isLoading, error, mutate } = useSWRImmutable(userInfo?.id? `/user/chat/recent?offset=${offset}&limit=${limit}` : null, request, {
      revalidateOnFocus: false
  });
  // console.log('data?.list', data?.list)
  const [list, setList] = useState<any>(data?.list || [])
  const loginStatus = auth?.loginStatus;
  const dialogLogin = useDialogLogin();
  const reouter = useRouter();

  const tabs = [
    {
      id: 0,
      name: 'recent',
      title: t('app.mine.recent_chat'),
    }
  ]

  useEffect(() => {
    if (!isLoading && (data !== undefined || error)) {
      setFirstLoading(false);
    }
  }, [isLoading, data, error]);

  
  useEffect(() => {
    // console.log('list', list)
    if(data?.list.length >= 0) {
      setList([...(offset === 0? [] : list), ...data.list])
    }
  }, [data]);

  // 删除卡片后，从第一页开始重新刷新
  const updateList = (id: number) => {
    // console.log('updateList', id);
    setList((list: any) => {
      return list.filter((role: any) => role.mode_target_id !== id)
    })  
  }
  useEffect(() => {
    if (loginStatus === LoginStatus.failed) {
      dialogLogin.show();
      return
    }
  }, [loginStatus])

  let hasMore = true;
  if(data?.total) {
    hasMore = offset < data.total - limit;
  }
  // console.log('offset, ', offset, userInfo?.id, isLoading);
  const loadMore = () => {
    // console.log('loadMore', loadMore);
    if (!hasMore || isLoading) return;
      !error && setOffset((offset + limit))
  };
  const containerRef = useRef<HTMLDivElement>(null);
  const handleScroll = debounce(() => {
    if (containerRef.current && 
        containerRef.current.clientHeight + containerRef.current.scrollTop + 800 >
        containerRef.current.scrollHeight
    ) {
        loadMore();
    }
  }, 50); // 50ms 的防抖延迟
  // 初始化时监听滚动事件
  useEffect(() => {
      const currentContainer = containerRef.current;
      
      if (currentContainer) {
          currentContainer.addEventListener('scroll', handleScroll);
      }
      
      return () => { 
          currentContainer?.removeEventListener('scroll', handleScroll); 
      }
  }, [handleScroll]);
  const retry = () => {
    mutate()
  }
  // console.log('hasmore', list?.length, userInfo?.id, isLoading);
  return (
    <>
      <main className="main-height" ref={containerRef}>
        {/* <Share className='' /> */}
        <div className="con-width">
        <div className='py-1 flex'>
          {tabs.map((elem, index) => {
            return <div key={elem.id}>
            <button onClick={() => {
              reouter.push(`/${lang}/history/${elem.name}`);
            }} className={cn('px-2 py-1.5 text-gray-500 text-sm', tab === elem.name? 'dark:text-white text-purple-500 font-medium' : '')}>{elem.title}</button>
          </div>
          })}
        </div>
        {
          tab === tabs[0].name && <div className="px-2 pb-2 grid grid-cols-1 sm:grid-cols-2 gap-2 lg:gap-3 lg:grid-cols-4 xl:grid-cols-5">
          {
            list?.length > 0?
            list.map((role: any) => {
              if(role.mode_type === 'single') {
                return <Card key={'single' + role.mode_target_id} reflesh={updateList} role={{isShowExport: true, ...role.role, isEdit: true, isShowDelChatHistory: true, isShowArchive: true}}></Card>
              } else {
                return <Card key={'group' + role.mode_target_id} reflesh={updateList} modeType={role.mode_type} role={{isShowExport: true, ...role.group, isEdit: true, isShowDelChatHistory: true, isShowArchive: true}}></Card>
              }
            }) :
            <>{!firstLoading && list.length === 0 && <p className="mt-1 text-sm leading-6 text-gray-400 text-center">{t('app.mine.recent_chat_tip')}</p>}</>
          }
          </div>
        }
        </div>
        {
            offset != 0 && <div className={cn((isLoading || !hasMore)? 'opacity-100' : 'opacity-0', 
                'h-6'
            )}>
                {isLoading && <p className={'text-center text-xs mb-1'}>{t('app.index.loading')}...</p>}
                {!hasMore && !isLoading && <p className='text-center text-xs mb-1'>{t('app.index.no_card_load')}</p>}
            </div>
        }
        {firstLoading && <LoadingToast msg={t('app.common.loading')}/>}
        {error && !isLoading && <LoadingToastFailed retry={retry} error={error} />}
      </main>
    </>
  )
}

export default Main
