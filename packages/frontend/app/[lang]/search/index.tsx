'use client'

import { MagnifyingGlassIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useEffect, useRef, useState } from 'react'
import Card from '@little-tavern/shared/src/ui/card/index';
import Toast from '@little-tavern/shared/src/ui/toast'
import { isMobileDevice } from '@/app/module/global'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import { useRouter, useParams } from 'next/navigation'
import s from '@/app/[lang]/globals.module.css'
import useSWRImmutable from 'swr';
import Back from '@share/src/ui/Back'
import Scroller from '../components/Scroller'

const limit = 15;
const Search = () => {
    const { t } = useTranslation()
    const router = useRouter();
    const [keyword, setKeyword] = useState('')
    const [nsfw, setNsfw] = useState(false)
    const seachIptRef = useRef<any>(null)
    const [roles, setRoles] = useState<any>([])
    const request = useRequest();
    const [isSearchEmpty, setIsSearchEmpty] = useState(false)
    const params = useParams()
    const lang = params.lang as string
    const [offset, setOffest] = useState(0)
    const [count, setCount] = useState(0)
    const { data, isLoading } = useSWRImmutable(keyword ? `/roles/search?nsfw=${nsfw}&limit=${limit}&keyword=${keyword}&offset=${offset}` : null, request, {
        revalidateOnFocus: false
    });
    const searhHandle = async (_nsfw?: boolean) => {
        let searchWord = seachIptRef.current.value
        if (!valid(searchWord)) return
        setKeyword(searchWord)
        setOffest(0)
        router.replace(`/${lang}/search?keyword=${searchWord}&nsfw=${_nsfw || nsfw}`)
    }
    const back = () => {
        // 硬编码到首页，避免聊天页面切换语言后，用back的方式，语言不正确
        router.replace(`/${lang}`)
    }
    const valid = (keyword: string) => {
        if ((!keyword || keyword.trim() === '')) {
            Toast.notify({
                type: 'warning',
                message: t('app.search.noMsg')
            })
            return false
        }
        return true
    }
    const handleKeyUp = (e: any) => {
        if (e.code === 'Enter' || e.key === 'Enter') {
            e.preventDefault()
            searhHandle()
        }
    }
    useEffect(() => {
        const searchParams = new URLSearchParams(location.search);
        const _nsfw = searchParams.get('nsfw') === 'true' ? true : false;
        setNsfw(_nsfw)
        const _keyword = searchParams.get('keyword') || ''
        if (_keyword) {
            seachIptRef.current.value = _keyword;
        }
        searhHandle(_nsfw);
    }, [])
    const changeKeyword = (e: any) => {
        setKeyword(e.target.value)
    }
    const handleCreateRole = () => {
        router.replace(`/${lang}/create/myroles`)
    }

    useEffect(() => {
        if (data?.roles.length > 0) {
            setRoles([...(offset === 0 ? [] : roles), ...data.roles])
            setCount(data.count)
        } else if (data?.roles.length === 0) {
            setRoles([])
            setIsSearchEmpty(true)
        }
        // 移动端收起键盘，体验不好可以移除
        isMobileDevice && seachIptRef?.current?.blur();
    }, [data])
    let hasMore = offset < count - limit;;
    const loadMore = () => {
        if (!hasMore || isLoading) return;
        setOffest(offset + limit);
    };
    const containerRef = useRef<HTMLDivElement>(null);
    return <>
        <div className='fixed z-50 left-0 top-0 py-3 box-border w-full px-2 flex items-center bg-white dark:bg-[#070f20] border-b dark:border-gray-700/70'>
            <Back hideTxt={true} />
            <div className='box-border relative flex-1 pr-2'>
                <input
                    ref={seachIptRef}
                    id="searchIpt"
                    type="text"
                    // onChange={changeKeyword}
                    autoComplete="off"
                    enterKeyHint={'search'}
                    onKeyUp={handleKeyUp}
                    autoFocus
                    // value={keyword}
                    placeholder={t('app.index.search_place_holder')}
                    className='pl-3.5 py-2 sm:py-2.5 rounded-full border-0 shadow-sm ring-1 ring-inset dark:ring-gray-500 ring-purple-500 focus:ring-inset focus:outline-0  text-sm sm:leading-6 dark:bg-gray-800 px-3 w-full outline-none' />
                <button onClick={() => { searhHandle }} className='absolute rounded-full dark:bg-gray-800 bg-white text-gray-500 right-3 top-[2px] p-1.5 sm:p-2.5' type='button'>
                    <MagnifyingGlassIcon className='w-5 h-5' />
                </button>
            </div>
        </div>
        <div className={cn('mt-16 px-2 flex-1 sm:flex-none h-[calc(100vh_-_69px)] overflow-auto outline-0')} ref={containerRef}>
            <div className='con-width'>

                {roles.length > 0 ? <div className='sm:pt-2'>
                    <h2 className='text-xs my-1.5'>{t('app.search.search_res')}</h2>
                    <Scroller containerRef={containerRef} loadMore={loadMore} hasMore={hasMore} isLoading={isLoading} offset={offset}>
                        <div className='pb-2 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 lg:gap-3 lg:grid-cols-4 xl:grid-cols-5'>
                            {roles?.map((role: any) => (
                                <Card key={role.id} role={{ ...role, isShowTags: true }}></Card>
                            ))}
                        </div>
                    </Scroller>
                </div> : isSearchEmpty && <div className='text-center mt-12'>
                    <p>{t('app.search.no_res')}</p>
                    <button onClick={handleCreateRole} className={cn(s.primBtn, 'mt-2')} type='button'>{t('app.search.create_role')}</button>
                </div>}
            </div>
        </div>
    </>
}

export default Search