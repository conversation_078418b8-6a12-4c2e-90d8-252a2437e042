@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 255, 255, 255;
  --background: #070f20;
  --background-end-rgb: 0, 0, 0;
  --bottom-margin: 63px;
  --bottom-margin: calc(63px + env(safe-area-inset-bottom));
}

.light {
  --foreground-rgb: 10 37 64;
  --sec-rgb: 66 84 102;
  --background: #eef2ff;
}

body {
  color: rgb(var(--foreground-rgb));
  background: var(--background)
}
html, body{
  height: 100%;
  overflow: hidden;
}

.main-height {
  margin-top: 44px;
  margin-bottom: var(--bottom-margin);
  position: relative;
  z-index: 1;
  overflow-y: auto;
  box-sizing: border-box;
  height: calc(100% - 44px - var(--bottom-margin));
  @apply transform-gpu
}

@media (min-width: 768px) {
  .main-height {
    height: calc(100% - 44px - env(safe-area-inset-bottom));
    margin-bottom: 0;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

button {
  background-color: transparent;
}

.con-width {
  @apply xl:w-[1280px] mx-auto w-full
}

img{ -webkit-user-drag: none; }

.ipt{
  @apply block w-full rounded-md border-0 shadow-sm ring-1 ring-inset dark:ring-gray-500 focus:ring-inset sm:text-sm sm:leading-6 dark:bg-gray-800 px-3 ring-gray-300 focus:ring-purple-500 dark:focus:ring-purple-500 focus:outline-0 outline-none
}
button{
  @apply focus:outline-none 
}

/* 主按钮点击效果 */
.priBtn {
  @apply box-border rounded-lg bg-purple-500 text-white hover:bg-purple-600 inline-block
}
.priBtn_disabled {
  @apply box-border rounded-lg dark:bg-gray-500 bg-gray-300 text-white inline-block
}

/* 如果webview版本过低
** md的.prose :where(q):not(:where([class~="not-prose"],[class~="not-prose"] *)) 会有些兼容问题
** 所以这里单独覆盖做兼容
*/
.light .prose q {
  color: rgba(225, 138, 36, 1);
}
.light .prose q::before {
  content: '';
}
.light .prose q::after {
  content: '';
}
input::placeholder, textarea::placeholder {
  font-size: 14px;
}
/* 处理markdown中的pre span因为字符过长导致滚动条的问题 */
.prose pre code span{
  word-wrap: break-word;
  word-break: break-all;
}