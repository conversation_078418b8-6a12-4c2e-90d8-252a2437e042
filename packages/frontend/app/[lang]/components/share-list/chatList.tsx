'use client'
import React, { useCallback, useContext, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useParams, useRouter } from 'next/navigation'
import { AuthContext } from '@little-tavern/shared/src/authContext'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import useSWRImmutable, { mutate } from 'swr';
import debounce from 'lodash.debounce';
import { LoadingToast, LoadingToastFailed } from '@little-tavern/shared/src/ui/Loading'
import {query} from '@little-tavern/shared/src/module/urlTool'
import ChatItem from './chatItem'
import NoContent from '@little-tavern/shared/src/ui/noContent'

const limit = 12;
const ChatList = ({url}: {url: string}) => {
    const { t } = useTranslation()
    const params = useParams()
    const auth = useContext(AuthContext);
    const userInfo = auth?.user;
    const request = useRequest();
    const [offset, setOffset] = useState(0);
    
    const { data, isLoading, error, mutate } = useSWRImmutable(userInfo?.id ? query.append(url, `offset=${offset}&limit=${limit}`) : null, request, {
        revalidateOnFocus: false
    });
    const [list, setList] = useState<any>(data?.data?.list || [])
    useEffect(() => {
        if (data?.data?.list?.length >= 0) {
            setList([...(offset === 0 ? [] : list), ...data.data.list])
        }
    }, [data]);

    let hasMore = false;
    if (data?.data?.total) {
        hasMore = offset < data.data.total - limit;
    }
    const loadMore = () => {
        if (!hasMore || isLoading) return;
        !error && setOffset((offset + limit))
    };
    const handleScroll = debounce(() => {
        if (
            window.innerHeight + document.documentElement.scrollTop + 800 >
            document.documentElement.offsetHeight
        ) {
            loadMore();
        }
    }, 50);
    useEffect(() => {
        window.addEventListener('scroll', handleScroll);
        return () => {
            window.removeEventListener('scroll', handleScroll);
        }
    }, [handleScroll]);
    const retry = () => {
        mutate()
    }
    
    return (
        <div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 mb-2">
                {
                    list.length > 0 ?
                        list.map((share: any) => {
                            return <ChatItem key={share.share_id} share={share} />
                        }) :
                        <>{userInfo?.id !== undefined && data?.data?.list?.length == 0 && !isLoading && <NoContent />}</>
                }
            </div>
            {
                <div className={cn((isLoading || !hasMore) && offset != 0 ? 'opacity-100' : 'opacity-0',
                    'h-6'
                )}>
                    {isLoading && <p className={'text-center text-xs mb-1'}>{t('app.index.loading')}...</p>}
                    {!hasMore && !isLoading && <p className='text-center text-xs mb-1'>{t('app.index.no_card_load')}</p>}
                </div>
            }
            {userInfo?.id === undefined || isLoading && offset == 0 && <LoadingToast msg={t('app.common.loading')} />}
            {error && !isLoading && <LoadingToastFailed retry={retry} error={error} />}
        </div>
    )
}

export default ChatList