import Image from 'next/image'
import s from '@/app/[lang]/globals.module.css'
import format from '@/app/module/formate-date'
import cn from 'classnames'
import { useParams, useRouter } from 'next/navigation'
import useLightBox from '@share/src/hook/useLightBox'
import { useTranslation } from 'react-i18next'
import UserChat from '../../chat/userChat'
import AIChat from '../../chat/AIChat'
import { Clock, MessageCircleMore, UserRound } from 'lucide-react';
import Link from 'next/link'

const ShareItem = ({ share }: { share: any }) => {
    const router = useRouter();
    const { t } = useTranslation()
    const lightBox = useLightBox();
    const params = useParams()
    const onSlectShare = (id: number) => {
        router.push(`/${params.lang}/chat/share/${id}`)
    }
    const chatList = share?.chat_list?.slice(0, 2);

    // Format the created_at time to show relative time
    const formatRelativeTime = (dateString: number) => {
        if (!dateString) return '';

        const date = new Date(dateString);
        const now = new Date();

        // Calculate the difference in milliseconds
        const diffMs = now.getTime() - date.getTime();
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        const diffMinutes = Math.floor(diffMs / (1000 * 60));

        if (diffMinutes < 60) {
            return `${diffMinutes}${t('app.common.minutes_ago')}`;
        } else if (diffHours < 24) {
            // Less than a day, show hours
            return `${diffHours}${t('app.common.hours_ago')}`;
        } else if (diffDays < 5) {
            // Less than 5 days, show days
            return `${diffDays}${t('app.common.days_ago')}`;
        } else {
            // 5 days or more, show '5天前'
            return `5${t('app.common.days_ago')}`;
        }
    };

    const colors = [
        'bg-gradient-to-br from-purple-100/50 to-pink-100/50 dark:from-purple-950/20 dark:to-pink-950/20',
        'bg-gradient-to-br from-lime-100/50 to-indigo-100/50 dark:from-lime-950/20 dark:to-indigo-950/20',
        'bg-gradient-to-br from-rose-100/50 to-pink-100/50 dark:from-rose-950/20 dark:to-pink-950/20',
        'bg-gradient-to-br from-emerald-100/50 to-teal-100/50 dark:from-emerald-950/20 dark:to-teal-950/20',
        'bg-gradient-to-br from-purple-100/50 to-pink-100/50 dark:from-purple-950/20 dark:to-pink-950/20',
        'bg-gradient-to-br from-amber-100/50 to-orange-100/50 dark:from-amber-950/20 dark:to-orange-950/20'
    ]
   
    return <div key={share.share_id} className={cn('relative overflow-hidden cursor-pointer w-full max-w-full rounded-md px-0 bg-white dark:bg-gray-900 rounded border border-gray-200/40 dark:border-gray-800/40', colors[Math.floor(Math.random() * colors.length)])} onClick={() => onSlectShare(share.share_id)}>
        <div className="relative overflow-hidden text-sm">
            <div className='flex items-center justify-between px-2 border-b border-gray-200/40 dark:border-gray-800/40 bg-white/40 dark:bg-black/10 py-1'>
                <div className='flex-1 py-1 text-base font-semibold mr-6 text-gray-800 dark:text-gray-100 inline-flex items-center truncate'>
                    <span className='truncate'>{share?.share_title}</span>
                </div>
                <div className='flex items-center text-xs text-gray-600 dark:text-gray-100'>
                    <Link className='text-xs flex items-center px-3' onClick={(e: React.MouseEvent) => {e.stopPropagation()}} href={`/user/${share?.sharer_uid}/share`}>
                        <div>{share?.sharer_avator ? <Image className={cn('mr-0.5 rounded-full h-5 w-5 object-cover')} src={share?.sharer_avator} width={24} height={24} alt={share?.sharer_name} unoptimized={true} /> : <UserRound className='mr-0.5 w-3.5 h-3.5' />}</div>
                        <span className='max-w-20 flex-1 truncate break-words hover:underline'>{share?.sharer_name}</span>
                    </Link>
                    <div className='flex items-center text-xs mr-3'><MessageCircleMore className='w-3.5 h-3.5 mr-0.5' />{share?.message_count}{t('app.common.count_msg')}</div>
                    <div className='flex items-center mr-1'><Clock className='w-3.5 h-3.5 mr-0.5' />{formatRelativeTime(share?.created_at * 1000)}</div>
                </div>
            </div>
            {chatList.map((chat: any, index: number) => {
                if (chat.type == 'human') {
                    return <UserChat key={index} user={{
                        nickname: '',
                        avatar: share?.sharer_avator
                    }} chat={chat} isChatSampleStyle={true} />
                } else {
                    return <AIChat key={index} chat={chat} chatInfo={{
                        role_avatar: share?.role_avatar,
                        // role_name: share.role_name,
                    }} chatList={[]} index={index} isChatSampleStyle={true} />
                }
            })}
        </div>
    </div>
}

export default ShareItem