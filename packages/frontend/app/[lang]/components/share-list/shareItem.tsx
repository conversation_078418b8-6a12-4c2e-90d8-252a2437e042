import Image from 'next/image'
import s from '@/app/[lang]/globals.module.css'
import format from '@/app/module/formate-date'
import cn from 'classnames'
import { useParams, useRouter } from 'next/navigation'
import useLightBox from '@share/src/hook/useLightBox'
import { useTranslation } from 'react-i18next'

const ShareItem = ({ share }: { share: any }) => {
    const router = useRouter();
    const { t } = useTranslation()
    const lightBox = useLightBox();
    const params = useParams()
    const onSlectShare = (id: number) => {
        router.push(`/${params.lang}/chat/share/${id}`)
    }

    return <div key={share.share_id} className='relative overflow-hidden cursor-pointer w-full w-full rounded-md py-2 dark:border dark:border-gray-500 dark:bg-gray-900 bg-white' onClick={() => onSlectShare(share.share_id)}>
        <div className="w-[100px] h-[150px] overflow-hidden rounded dark:bg-gray-800 bg-gray-300 ml-2 mr-3 float-left relative">
            <Image key={share.share_id} className='w-[100px] h-[150px] rounded max-w-none my-0' style={{ objectFit: "cover" }} src={share.role_avatar || '/dot.png'} width={100} height={150} quality={90} alt={share.role_name} onClick={(e) => {
                e.stopPropagation()
                lightBox.show({ src: share.role_avatar || '/dot.png' });
            }} />
        </div>
        <div className="relative mr-2 overflow-hidden h-[150px]">
            <div className="name text-lg dark:text-gray-100 truncate w-[185px] min-[350px]:w-[225px] flex items-center">
                <span className='hover:underline inline-block flex-1 text-ellipsis overflow-hidden'>{share.share_title}</span>
            </div>
            <div className='text-xs text-gray-400 mb-1'>{share.role_name}</div>
            <div className={cn(s.lineClamp3, "hover:underline desc text-sm dark:text-gray-100 text-gray-600 h-22")}>{share.share_description}</div>
            <div onClick={(e) => { e.stopPropagation() }} className='absolute py-1 rounded text-xs bottom-0 dark:text-gray-300 text-gray-500 '>
                {share.sharer_name && <div>{t('app.share.sharer')}: {share.sharer_name}</div>}
                <div>{t('app.share.share_time')}: {format(share.created_at * 1000, 'YYYY-MM-DD HH:mm')}</div>
            </div>
        </div>
    </div>
}

export default ShareItem