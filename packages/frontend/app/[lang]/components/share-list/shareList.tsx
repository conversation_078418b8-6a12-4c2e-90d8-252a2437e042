'use client'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { useParams, useRouter } from 'next/navigation'
import { AuthContext } from '@little-tavern/shared/src/authContext'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import useSWRImmutable, { mutate } from 'swr';
import debounce from 'lodash.debounce';
import { LoadingToast, LoadingToastFailed } from '@little-tavern/shared/src/ui/Loading'
import {query} from '@little-tavern/shared/src/module/urlTool'
import ShareItem from './shareItem'
import NoContent from '@little-tavern/shared/src/ui/noContent'

const limit = 12;
const ShareList = ({url}: {url: string}) => {
    const { t } = useTranslation()
    const params = useParams()
    const auth = useContext(AuthContext);
    const userInfo = auth?.user;
    const request = useRequest();
    const [offset, setOffset] = useState(0);
    const containerRef = useRef<HTMLDivElement>(null);
    
    const { data, isLoading, error, mutate } = useSWRImmutable(userInfo?.id ? query.append(url, `offset=${offset}&limit=${limit}`) : null, request, {
        revalidateOnFocus: false
    });
    const [list, setList] = useState<any>(data?.data?.list || [])
    useEffect(() => {
        if (data?.data?.list?.length >= 0) {
            setList([...(offset === 0 ? [] : list), ...data.data.list])
        }
    }, [data]);

    let hasMore = false;
    if (data?.data?.total) {
        hasMore = offset < data.data.total - limit;
    }
    const loadMore = () => {
        if (!hasMore || isLoading) return;
        !error && setOffset((offset + limit))
    };
    const handleScroll = debounce(() => {
        if (containerRef?.current && 
            containerRef.current.clientHeight + containerRef.current.scrollTop + 800 >
            containerRef.current.scrollHeight
        ) {
            loadMore();
        }
    }, 50);
    useEffect(() => {
        const currentContainer = containerRef?.current;
        
        if (currentContainer) {
            currentContainer.addEventListener('scroll', handleScroll);
        }
        
        return () => {
            currentContainer?.removeEventListener('scroll', handleScroll);
        }
    }, [handleScroll]);
    const retry = () => {
        mutate()
    }
    
    return (
        <div ref={containerRef} className='px-2 overflow-y-auto'>
            <div className="pt-2 pb-2 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                {
                    list.length > 0 ?
                        list.map((share: any) => {
                            return <ShareItem key={share.share_id} share={share} />
                        }) :
                        <>{userInfo?.id !== undefined && data?.data?.list?.length == 0 && !isLoading && <NoContent />}</>
                }
            </div>
            {
                <div className={cn((isLoading || !hasMore) && offset != 0 ? 'opacity-100' : 'opacity-0',
                    'h-6'
                )}>
                    {isLoading && <p className={'text-center text-xs mb-1'}>{t('app.index.loading')}...</p>}
                    {!hasMore && !isLoading && <p className='text-center text-xs mb-1'>{t('app.index.no_card_load')}</p>}
                </div>
            }
            {userInfo?.id === undefined || isLoading && offset == 0 && <LoadingToast msg={t('app.common.loading')} />}
            {error && !isLoading && <LoadingToastFailed retry={retry} error={error} />}
        </div>
    )
}

export default ShareList