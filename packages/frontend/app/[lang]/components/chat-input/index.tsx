'use client'
import type { <PERSON>, ReactEventHandler } from 'react'
import React, { useEffect, useRef, useContext, useState } from 'react'
import cn from 'classnames'
import { useTranslation } from 'react-i18next'
import s from './style.module.css'
import Tooltip from '@little-tavern/shared/src/ui/tooltip'
import Toast from '@little-tavern/shared/src/ui/toast'
import AutoHeightTextarea from '@/app/[lang]/components/auto-height-textarea'
import { isMobileDevice } from '@/app/module/global'
import { AuthContext } from '@little-tavern/shared/src/authContext';
import { LightBulbIcon, PlusIcon, MinusIcon, SquaresPlusIcon } from '@heroicons/react/24/solid'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import { commonHeader } from '@little-tavern/shared/src/module/commonHeader';
import useComfirm from '@little-tavern/shared/src/hook/useComfirm'
import DOMPurify from 'dompurify';
import { useTheme } from 'next-themes'
import IconDice from './icons/dice'
import Image from 'next/image'
import useCreateGroup from '@share/src/hook/useCreateGroup'
import useDialogAlert from '@share/src/hook/useDialogAlert'
import ChatMenu from './ChatMenu'
import updateSetting from '@share/src/module/updateSetting'
import { getLen } from './stringTool'
import useLocalStorage from '@share/src/hook/useLocalStorage'
import SwitchModel from './switchModel'

// 如果快捷输入点击，不触发blur事件
let isConvenientBtnTouch = false
let controller: any;
const quickBtnCls = 'rounded-full dark:bg-gray-800 bg-white drop-shadow py-0.5 px-2.5 mr-3 mb-1';
const ChatInput: FC<any> = ({
  controlClearQuery,
  onSend,
  isResponsing,
  cancelRespone,
  onBlur,
  conversationId,
  chatInfo,
  modeType,
  groupId,
  onfocus,
  chatList, supportMids, setModelEventMap, roleId,
  chatTimes,
  setChatTimes
}) => {
  const { t } = useTranslation()
  const { notify } = Toast
  const { resolvedTheme } = useTheme()
  const isUseInputMethod = useRef<any>(false)
  const [query, setQuery] = React.useState('')
  const inputRef = useRef<any>(null);
  const auth = useContext(AuthContext);
  const user = auth?.user
  const request = useRequest();
  const [isRecResing, setIsRecResing] = useState(false)
  const [isWaitResing, setIsWaitResing] = useState(false)
  const comfirm = useComfirm();
  const createGroup = useCreateGroup();
  const [showGroupGuide, setShowGroupGuide] = useLocalStorage('show_group_guide', true)

  const handleContentChange = (e: any) => {
    const value = e.target.value
    setQuery(value)
    setInputAnswer('')
  }

  const valid = () => {
    if ((!query || query.trim() === '') && (!inputAnswer || inputAnswer.trim() === '')) {
      notify({ type: 'error', message: t('app.chat.msg_not_empty'), duration: 3000 })
      return false
    }
    const limit = 1000;
    if (getLen(query) > limit) {
      notify({ type: 'error', message: t('app.chat.msg_too_long', { limit }), duration: 3000 })
      return false
    }
    return true
  }

  useEffect(() => {
    if (controlClearQuery)
      setQuery('')
  }, [controlClearQuery])
  const handleSend = () => {
    // isMobileDevice && inputRef?.current?.focus();
    if (!valid())
      return
    onSend({ message: query || inputAnswer })
    // 移动端收起键盘，体验不好可以移除
    isMobileDevice && inputRef?.current?.blur();
    if (!isResponsing)
      setQuery('')
    setInputAnswer('')
    setIsOnfocus(false);
  }
  const handleKeyUp = (e: any) => {
    if (e.code === 'Enter') {
      e.preventDefault()
      // prevent send message when using input method enter
      if (!e.shiftKey && !isUseInputMethod.current)
        handleSend()
    }
  }

  const haneleKeyDown = (e: any) => {
    isUseInputMethod.current = e.nativeEvent.isComposing
    if (e.code === 'Enter' && !e.shiftKey) {
      setQuery(query.replace(/\n$/, ''))
      e.preventDefault()
    }
  }

  const [inputAnswer, setInputAnswer] = useState('');
  const getRecAnswer = async () => {
    // 第一次提示用户
    const isShowRecAnswerTips = user?.show_chat_tips;
    if (isShowRecAnswerTips) {
      const res = await comfirm.show({
        title: t('app.chat.tips'),
        desc: t('app.chat.tips_desc'),
        comfirmBtn: t('app.chat.comfirm'),
        showCancelBtn: true,
        icon: <LightBulbIcon className='w-6 h-6 text-yellow-600' />,
        isShowIgnore: true
      })
      if (res?.ignore) {
        updateSetting({
          data: {
            show_chat_tips: false
          },
          request,
          auth,
          t
        })
      }
      if (!res?.confirm) {
        return;
      }
    }
    if (isRecResing) {
      notify({ type: 'info', message: t('app.chat.wating_desc') })
      return
    }
    setQuery('')
    setInputAnswer('')
    setIsRecResing(true);
    setIsWaitResing(true)
    try {
      controller = new AbortController();
      let response: any = await fetch(`${process.env.NEXT_PUBLIC_API_HOST}/impersonate`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          ...commonHeader
        },
        signal: controller.signal,
        body: JSON.stringify({
          conversation_id: conversationId,
          mode_type: modeType,
          group_id: groupId,
          role_id: chatInfo.role_id,
          api_version: 'v2'
        })
      });
      const reader = response.body.getReader();
      let result = '';
      setIsWaitResing(false)
      if (response.status == 402) {
        auth?.showBalanceConfirm();
        return;
      }
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          break;
        }

        let buffer = new TextDecoder("utf-8").decode(value);
        if (buffer.includes('\r\n\r\n')) {
          const lines = buffer.split('\r\n\r\n')
          try {
            lines.forEach((message) => {
              if (!message || !message.startsWith('event: data\r\ndata: '))
                return
              let s = message.substring(13);
              s = s.split('data: ').join('')
              result += s
              process.env.NEXT_PUBLIC_RM_CONSOLE != "true" && console.log('res', result);
            })
          }
          catch (e) {
            console.error('handleStream error', e)
          }

          setInputAnswer(formate(result))
        }
      }
      // process.env.NEXT_PUBLIC_RM_CONSOLE != "true" && console.log('res', result);
      setIsRecResing(false);
      setInputAnswer(formate(result))
      setChatTimes(chatTimes + 1)
    } catch (e) {
      setIsWaitResing(false)
      console.log("fetch 主动取消");
    }
  }
  const formate = (content: string) => {
    let _content = DOMPurify.sanitize(content, { KEEP_CONTENT: true, ALLOWED_TAGS: [] });
    process.env.NEXT_PUBLIC_RM_CONSOLE != "true" && console.log('sanitize', _content.trim());
    _content = _content.replace(/^[^:：]*[:：]/, "");
    return _content.trim();
  }
  const cancelRecRespone = (e: any) => {
    console.log('cancelRespone');
    isRecResing && controller && controller.abort('user cancel');
    isRecResing && setIsRecResing(false)
    isResponsing && cancelRespone && cancelRespone(e);
  }

  const getAiReply = (aiRoleId?: number) => {
    const id = aiRoleId ? aiRoleId : chatInfo.roles[Math.floor(chatInfo.roles.length * Math.random())].id
    onSend({ message: '', aiRoleId: id })
    setChatTimes(chatTimes + 1)
  }
  const dialogAlert = useDialogAlert();
  // todo: 跟卡片页面checkIsAudit复用
  const checkIsAudit = () => {
    if (chatInfo.audit_status == 'auditing') {
      dialogAlert.show({
        alertStatus: 0,
        title: t('app.cardEdit.auditng'),
        desc: t('app.cardEdit.audit_desc')
      })
      return true
    }
    return false
  }

  const [isOnfocus, setIsOnfocus] = useState(false);
  const handleFocus = (e: any) => {
    setIsOnfocus(true);
    onfocus && onfocus(e)
  }
  const handleInsert = (text: string, isCenter?: boolean) => {
    const input = inputRef.current;
    const start = input.selectionStart;
    const end = input.selectionEnd;
    const newQuery = query.slice(0, start) + text + query.slice(end);
    setQuery(newQuery);
    // Set cursor position after the inserted text
    input.focus();
    setTimeout(() => {
      input.selectionStart = input.selectionEnd = start + (isCenter ? text.length / 2 : text.length);
      isConvenientBtnTouch = false
    }, 0);
  }
  const handBlur = (e: any) => {
    !isConvenientBtnTouch && onBlur && onBlur(e);
  }

  useEffect(() => {
    const closeGroupGuide = () => {
      setShowGroupGuide(false)
    }
    if (showGroupGuide === true) {
      document.addEventListener('click', closeGroupGuide)
    }
    return () => {
      document.removeEventListener('click', closeGroupGuide)
    }
  }, [showGroupGuide, setShowGroupGuide])

  return (
    <div className='dark:bg-slate-950 bg-white drop-shadow-[0_-4px_4px_rgba(0,0,0,0.1)] dark:border-t dark:border-[#2c2e2d] pb-[env(safe-area-inset-bottom)]'>
      <div className={cn('relative md:w-[768px]', `w-full flex py-3 rounded-lg w-auto mx-auto `)}>
        <SwitchModel supportMids={supportMids} setModelEventMap={setModelEventMap} chatList={chatList} modeType={modeType} conversationId={conversationId} roleId={roleId} groupId={groupId} isResponsing={isResponsing} chatTimes={chatTimes} setChatTimes={setChatTimes} />
        {(!isResponsing) && <div className={cn(isRecResing && 'animate-pulse', "flex z-10 absolute left-16")}>
          <Tooltip
            selector='try-tip'
            htmlContent={
              <div>
                <div>{t('app.chat.try_ai_answer')}</div>
              </div>
            }
          >
            <button
              onClick={() => { getRecAnswer() }}
              className={`${s.btn} ml-2 mr-3`}>
              <LightBulbIcon className='w-6 h-6 text-yellow-600' />
            </button>
          </Tooltip>
        </div>}
        {isWaitResing && <div className={cn(s.loader, resolvedTheme === 'dark' && s.loaderDark, 'absolute z-10 w-8 h-8 left-28 top-6')}></div>}
        <div className={cn('flex-1 mr-4 relative')}>
          {
            (modeType === 'group' || isOnfocus) && <div className='absolute -top-0 w-full'><div className='absolute bottom-0 left-4'>
              {modeType === 'group' && <div className='inline-flex items-center relative'>
                {/* 群聊引导 */}
                {showGroupGuide && <div className='absolute bottom-12 w-28 -left-12 bg-white rounded-full p-3 px-5 text-black text-xs after:content-[""] after:absolute after:w-3 after:h-3 after:bg-white after:rotate-45 after:-bottom-1 after:left-[58%] after:-translate-x-1/2'>
                  {t('app.chat.dice_tooltip')}
                </div>}
                {showGroupGuide && <div className='absolute bottom-12 w-28 -right-12 bg-white rounded-full p-3 px-5 text-black text-xs after:content-[""] after:absolute after:w-3 after:h-3 after:bg-white after:rotate-45 after:-bottom-1 after:left-[39%] after:-translate-x-1/2'>
                  {t('app.chat.avatar_tooltip')}
                </div>}
                <button className='p-1 w-8  h-8  border border-purple-300 dark:border-gray-300 rounded-full flex items-center justify-center bg-white dark:bg-black' onClick={() => { getAiReply() }}><IconDice /></button>
                {
                  chatInfo.roles.map((role: any) => {
                    return <button className='p-1' key={role.id} onClick={() => { getAiReply(role.id) }}>
                      <Image className='object-cover w-8 h-8 border border-purple-300 dark:border-gray-300 rounded-full' src={role.role_avatar || '/dot.png'} alt={role.card_name} width={32} height={32} />
                    </button>
                  })
                }

                {/* {
              chatInfo?.private_card && <>
                <button className='p-1 mr-2 ml-1 w-8  h-8  border border-purple-300 dark:border-gray-300 rounded-full flex items-center justify-center bg-white dark:bg-black' onClick={async () => {
                  if(checkIsAudit()) return
                  const isSuccess = await createGroup.show({ msg: { create_roles: [], id: groupId, isEdit: true } })
                  isSuccess && reflesh && reflesh();
                }}>
                  <SquaresPlusIcon className='w-4 h-4 text-gray-500/60 dark:text-gray-200' />
                  </button>
              </>
            } */}
              </div>}
              {isOnfocus && <div className='text-xs flex-wrap pt-1' onTouchStartCapture={() => {
                isConvenientBtnTouch = true
              }}>
                {chatInfo?.role_name && <button onClick={() => {
                  handleInsert(`${chatInfo.role_name}`)
                }} className={cn(quickBtnCls)} type='button'>@{chatInfo.role_name}</button>}
                {
                  chatInfo?.roles?.map((role: any) => {
                    if (!role.role_name) return null;
                    return <button key={role.id} onClick={() => {
                      handleInsert(`${role.role_name}`)
                    }} className={cn(quickBtnCls)} type='button'>@{role.role_name}</button>
                  })
                }
                {user?.nickname && <button onClick={() => {
                  handleInsert(`${user?.nickname}`)
                }} className={cn(quickBtnCls)} type='button'>@{user?.nickname}</button>}
                <button onClick={() => {
                  handleInsert(`""`, true)
                }} className={cn(quickBtnCls)} type='button'>&ldquo;&rdquo;</button>
                <button onClick={() => {
                  handleInsert(`()`, true)
                }} className={cn(quickBtnCls)} type='button'>()</button>
              </div>}
            </div></div>
          }

          <div className="relative">
            <AutoHeightTextarea
              ref={inputRef}
              value={query || inputAnswer}
              onChange={handleContentChange}
              onKeyUp={handleKeyUp}
              onKeyDown={haneleKeyDown}
              minHeight={56}
              maxHeight={220}
              onBlur={handBlur}
              onfocus={handleFocus}
              className={`${cn(s.ipt)} block dark:bg-[#212121]`}
            />
            <div className="absolute top-1 right-3 flex items-center h-[48px]">
              {/* <div className={`${s.count} mr-2 h-5 leading-5 text-sm bg-gray-50 text-gray-500`}>{query.trim().length}</div> */}
              <Tooltip
                selector='send-tip'
                htmlContent={isResponsing ?
                  <div>{t('app.chat.cancel_reply')}</div> :
                  <div>
                    <div>{t('app.operation.send')} Enter</div>
                    <div>{t('app.operation.lineBreak')} Shift Enter</div>
                  </div>
                }
              >
                {isResponsing || isRecResing ? <button
                  onClick={cancelRecRespone}
                  className={`${s.btn} ml-2 mr-0`}>
                  <div className='w-4 h-4 dark:bg-gray-50 bg-gray-400'></div>
                </button> : <button type='button' className={`${s.sendBtn} w-12 h-12 rounded-md block`} onClick={handleSend}></button>}
              </Tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default React.memo(ChatInput)
