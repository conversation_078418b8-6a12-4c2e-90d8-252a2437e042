// 获取字符串长度，中文直接采用length，英文采用2.5*length
const getLen = (str: string) => {
    let totalLength = 0;
    for (let char of str) {
        if (/[\u4e00-\u9fa5]|\d/.test(char)) {
            // Chinese character or number
            totalLength += 1;
        } else {
            // Non-Chinese character and non-number
            totalLength += 2.5;
        }
    }
    console.log('totalLength', totalLength);
    return totalLength;
}
export {
    getLen
}