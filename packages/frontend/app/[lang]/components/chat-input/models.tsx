'use client'
import React, { Fragment, useContext, memo, useEffect, useState, useCallback } from 'react'
import { AuthContext } from '@little-tavern/shared/src/authContext';
import { CheckCircleIcon } from '@heroicons/react/24/solid'
import { useParams, useRouter } from 'next/navigation';
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import Toast from '@little-tavern/shared/src/ui/toast';
import { Menu, Transition } from '@headlessui/react'
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import cn from 'classnames'
import { useTranslation } from 'react-i18next'
import useDialogAlert from '@little-tavern/shared/src/hook/useDialogAlert';
import { LockOpenIcon } from '@heroicons/react/24/solid'
import useComfirm from '@share/src/hook/useComfirm';
import { runsOnServerSide } from '@little-tavern/shared/src/module/global'
import { EventType } from './switchModel';

const Models = ({ supportMids, setModelEventMap, chatList, modeType, conversationId, roleId, groupId, isResponsing }: any) => {
    const { t } = useTranslation()
    const params = useParams()
    const lang = params.lang as string
    const auth = useContext(AuthContext);
    const user = auth?.user;
    const payedUser = user?.payed_user;
    const mid = user?.user_model
    const allModels = user?.chat_products;
    const curModel: any = allModels?.find((item: any) => {
        return item.mid == mid
    });
    const router = useRouter();
    const request = useRequest();
    const dialogAlert = useDialogAlert();
    const confirm = useComfirm();
    const [isShowGuideSwitchModel, setIsShowGuideSwitchModel] = useState(!runsOnServerSide && localStorage.getItem('showGuideSwitchModel') === null)
    // 支持的模型
    const models = allModels?.filter((item: any) => supportMids ? supportMids?.includes(item.mid) : true)
    // 用户的模型是否在支持的模型中
    const isCurModelInSupport = models?.some((item: any) => item.mid == mid)
    // 不支持的模型
    const notPassModels = allModels?.filter((item: any) => supportMids?.indexOf(item.mid) === -1)
    const notPassModelNames = notPassModels?.map((item: any) => item.model_name)
    // 默认推荐模型
    let suggestModel = null;
    let exitFreeRecModel = false;
    if (payedUser) {
        // 获取支持的models里面price最小的模型
        suggestModel = models?.sort((a: any, b: any) => a.price - b.price)[0]
    } else {
        // 获取支持的models里面permission==="ALL_USER"模型列表
        const allUserModels = models?.filter((item: any) => item.permission === 'ALL_USER')
        // 如果存在免费模型列表，获取price最小的模型
        if (allUserModels && allUserModels?.length > 0) {
            suggestModel = allUserModels?.sort((a: any, b: any) => a.price - b.price)[0]
            exitFreeRecModel = true
        } else {
            // 获取所有models里面permission==="ALL_USER"模型列表
            const allModelsAllUserModels = allModels?.filter((item: any) => item.permission === 'ALL_USER')
            // 如果不存在支持的免费模型，获取免费价格最高的免费模型
            suggestModel = allModelsAllUserModels?.sort((a: any, b: any) => b.price - a.price)[0]
        }
    }

    //  为null时表示用户当前选的模型是角色卡支持的，不用提示用户切换模型
    // "switch_model": {
    //   target_model: "m17",  # 需要帮用户切换到的目标模型
    //   need_recharge_first: false     # false 表示不需要提示用户需要先充值。  true表示需要提示用户先进行充值，才能切换 （说明  target_model是个付费模型并且当前用户还不是充值用户）
    //  }  

    // 定义changeModel函数并用useCallback包裹以避免不必要的重新渲染
    const changeModel = React.useCallback(async (mid: string, event_type: string) => {
        if (mid === user?.user_model) return
        console.log('isResponsing', isResponsing)
        if (isResponsing) {
            Toast.notify({
                type: 'info',
                message: t('app.chat.switch_failed1'),
                duration: 3000
            })
            return
        }
        Toast.showLoading('');
        try {
            const res = await request(`/chat/update_model`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    mode_type: modeType,
                    mode_target_id: modeType === 'single' ? roleId : groupId,
                    conversation_id: conversationId,
                    model: mid,
                    event_type: event_type
                })
            });
            Toast.hideLoading();
            if (res.error_code === 1003) {
                dialogAlert.show({
                    title: t('app.chat.switch_failed'),
                    desc: res.message,
                    alertStatus: 0
                })
            } else if(res.error_code === 0) {
                setModelEventMap((prev: any) => {
                    if (chatList.length === 0) return
                    const lastMsgId = chatList[chatList.length - 1].message_id;
                    const last = prev[lastMsgId];
                    const modeEvent = {
                        event_type: event_type,
                        from_model: user?.user_model,
                        from_model_name: allModels?.find((item: any) => item.mid == user?.user_model)?.model_name,
                        to_model: mid,
                        to_model_name: allModels?.find((item: any) => item.mid == mid)?.model_name
                    };
                    if (last && last.length > 0) {
                        last.push(modeEvent);
                    } else {
                        prev[lastMsgId] = [modeEvent]
                    }
                    return prev
                })
                auth?.updateUserInfo({
                    user_model: mid
                });
                Toast.notify({ type: 'success', message: t('app.chat.switch_success') })
            }
        } catch (e: any) {
            console.error('changeModel error:', e);
            Toast.hideLoading();
            Toast.notify({ type: 'error', message: t('app.chat.switch_failed') })
        }
    }, [request, dialogAlert, t, auth]);

    const recModel = useCallback(() => {
        // 如果用户当前的模型不在支持的模型中，需要推荐的模型
        if (!isCurModelInSupport && suggestModel) {
            if (payedUser) {
                // confirm.show({
                //     title: t('app.common.tips'),
                //     desc: t('app.chat.switch_desc', { not_pass_model: notPassModelNames?.join('、'), supported_model: models?.map((item: any) => item.model_name).join('、') }),
                //     comfirmBtn: t('app.chat.switch_model', { model: suggestModel?.model_name }),
                //     cancelBtn: t('app.chat.continue_model', { model: curModel?.model_name }),
                //     showIcon: false
                // }).then((res) => {
                //     if (res?.confirm) {
                //         changeModel(suggestModel?.mid, event_type.chat_man)
                //     }
                // })
                changeModel(suggestModel?.mid, EventType.chat_auto)
                return;
            } else {
                // 如果存在免费的推荐模型，自动切换
                if (exitFreeRecModel) {
                    // dialogAlert.show({
                    //     title: t('app.common.tips'),
                    //     desc: t('app.chat.switch_desc1', { model_name: curModel?.model_name, target_model_name: suggestModel?.model_name }),
                    // })
                    changeModel(suggestModel?.mid, EventType.chat_auto)
                    return;
                }
                // 如果不存在免费的推荐模型，提示用户充值
                confirm.show({
                    title: t('app.common.tips'),
                    desc: t('app.chat.switch_desc2', { not_pass_model: notPassModelNames?.join('、'), supported_model: models?.map((item: any) => item.model_name).join('、') }),
                    comfirmBtn: t('app.chat.btn_pay'),
                    cancelBtn: t('app.chat.switch_top_free_model', { model_name: suggestModel?.model_name }),
                    showIcon: false,
                    isCloseIconShow: false
                }).then((res) => {
                    if (res?.confirm) {
                        router.push(`/${lang}/pay`)
                    } else {
                        changeModel(suggestModel?.mid, EventType.chat_man)
                    }
                })
            }
        }
    }, []);
    // 处理模型切换的逻辑
    // 检查并处理模型切换
    useEffect(() => {
        recModel();
    }, []);
    // 检查引导用户切换模型弹窗 - 使用useCallback来避免重复创建
    

    // 组件加载时检查是否显示切换模型弹窗
    useEffect(() => {
        const showSwitchModel = async () => {
            if (!isShowGuideSwitchModel) return
            const res = await request('/operation/popup?popup_type=pay_switch_model');
            const popup = res?.popup;
            if (popup?.type === 'pay_switch_model') {
                const userRes = await confirm.show({
                    title: popup.title,
                    icon: <LockOpenIcon className="h-6 w-6 text-purple-500" />,
                    desc: popup.content,
                    comfirmBtn: t('app.chat.switch'),
                    cancelBtn: t('app.chat.keep')
                })
                if (userRes.confirm) {
                    changeModel(popup.extra, EventType.chat_man)
                }
                setIsShowGuideSwitchModel(false)
                localStorage.setItem('showGuideSwitchModel', '1')
            }
        };
        showSwitchModel();
    }, [])

    const changeNotPassModel = (mid: string) => {
        if (mid === user?.user_model) return
        const targetModel = allModels?.find((item: any) => item.mid == mid)
        const recModel = isCurModelInSupport ? curModel : suggestModel;
        // 如果当前模型和目标模型相同，直接返回
        if (recModel?.mid === targetModel?.mid) {
            changeModel(recModel?.mid, EventType.chat_man)
            return;
        }
        // console.log('switchModel', allModels, targetModel)
        confirm.show({
            title: t('app.common.tips'),
            desc: t('app.chat.switch_desc', { not_pass_model: notPassModelNames?.join('、'), supported_model: models?.map((item: any) => item.model_name).join('、') }),
            comfirmBtn: t('app.chat.switch_model', { model: recModel?.model_name }),
            cancelBtn: t('app.chat.continue_model', { model: targetModel?.model_name }),
            showIcon: false,
            isCloseIconShow: false
        }).then((res) => {
            if (res?.confirm) {
                changeModel(recModel?.mid, EventType.chat_man)
            } else {
                changeModel(mid, EventType.chat_man)
            }
        })
    }
    return <div className='w-fit mx-auto text-sm h-7 flex items-center justify-center'>
        {models && models?.length > 0 && <><div className='flex items-center flex-shrink-0 text-gray-500'>
            <div className="flex items-center text-sm">
                <Menu as="div" className="inline-block text-left">
                    {({ open }) => (
                        <>
                            <div>
                                <Menu.Button className={cn("inline-flex w-full justify-center px-4 py-2 text-sm font-medium text-zinc-500 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 group", open && "text-black dark:text-white")}>
                                    <div className={cn('w-fit text-right')}>
                                        <div className='max-w-[calc(100vw_-_220px)] truncate'>{t('app.common.model')}：{curModel?.model_name}</div>
                                    </div>
                                    <ChevronDownIcon
                                        className={cn("-mr-1 h-5 w-5 transition", open && "rotate-180 text-black dark:text-white")}
                                        aria-hidden="true"
                                    />
                                </Menu.Button>
                            </div>
                            <Transition
                                as={Fragment}
                                enter="transition ease-out duration-100"
                                enterFrom="transform opacity-0 scale-95"
                                enterTo="transform opacity-100 scale-100"
                                leave="transition ease-in duration-75"
                                leaveFrom="transform opacity-100 scale-100"
                                leaveTo="transform opacity-0 scale-95"
                            >
                                <Menu.Items className="dark:border border-gray-700 absolute left-0 right-0 mt-0.5 mx-auto w-fit origin-top-right divide-y divide-gray-100 rounded-md dark:bg-gray-900 bg-white shadow-lg ring-1 ring-black/5 focus:outline-none">
                                    <div className="px-1 py-1 overflow-y-auto max-h-[calc(100vh_-_130px)]">
                                        {
                                            notPassModels?.map((model: any) => {
                                                return <MenuItem key={model.mid} model={model} mid={mid} changeModel={changeNotPassModel} cl='opacity-50' />
                                            })
                                        }
                                        {
                                            models.map((model: any) => {
                                                return <MenuItem key={model.mid} model={model} mid={mid} changeModel={changeModel} />
                                            })
                                        }
                                    </div>
                                </Menu.Items>
                            </Transition>
                        </>
                    )}
                </Menu>
            </div>
        </div>
        </>}
    </div>
}

export default memo(Models)


export const MenuItem = ({ model, mid, changeModel, cl }: { model: any, mid: string | undefined, changeModel: (mid: string, event_type: string) => void, cl?: string }) => {
    const { t } = useTranslation()
    return <Menu.Item key={model.mid}>
        {({ active }) => (
            <div
                onClick={() => { changeModel(model.mid, EventType.chat_man) }}
                className={`${active ? 'bg-violet-500 text-white' : ''
                    } group w-full items-center rounded-md px-2 py-2 text-sm dark:text-zinc-100 whitespace-nowrap flex gap-2 ${cl}`}
            >
                <div>
                    <p>{model.model_name}<span className='ml-2 text-xs text-yellow-600'>{model.price} {t('app.chat.price')}</span></p>
                    <p className={`w-[80vw] max-w-96 whitespace-break-spaces text-xs text-gray-500 ${active ? 'bg-violet-500 text-white' : ''
                        }`}>{model.desc}</p>
                </div>
                {model.mid == mid && <CheckCircleIcon className='w-4 h-4' />}
            </div>
        )}
    </Menu.Item>
}