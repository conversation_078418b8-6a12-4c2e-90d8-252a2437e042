import { Bars3Icon } from '@heroicons/react/24/solid'
import useComfirm from '@share/src/hook/useComfirm';
import { useTranslation } from 'react-i18next';
import IconClean from './icons/clean'
import s from './style.module.css'
import cn from 'classnames';
import useDialogAlert from '@share/src/hook/useDialogAlert';
import useLoadArchive from '@share/src/hook/useLoadArchive';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState, useCallback } from 'react';

const btnClass = 'group flex w-full items-center gap-2 rounded-lg py-1.5 px-3 dark:data-[focus]:bg-white/10 dark:hover:bg-white/10 hover:bg-violet-500 hover:text-white'
const iconClass = 'size-4 text-purple-500 dark:text-gray-300 group-hover:text-white'
const ChatMenu = ({ startNewChat, modeType, chatInfo, groupId }: any) => {
  const { t } = useTranslation()
  const comfirm = useComfirm();
  const dialogAlert = useDialogAlert();
  
  const params = useParams()
  const lang = params.lang as string
  const [isOpen, setIsOpen] = useState(false)
  

  const hideMenu = useCallback((e: MouseEvent) => {
    // 检查点击是否发生在菜单外部
    if (isOpen) {
      setIsOpen(false)
    }
  }, [isOpen])

  useEffect(() => {
    // 只在 isOpen 为 true 时添加事件监听
    if (isOpen) {
      document.addEventListener('click', toggleMenu)
      return () => {
        document.removeEventListener('click', toggleMenu)
      }
    }
  }, [isOpen, hideMenu])
  useEffect(() => {
    if(localStorage.getItem('hasShowChatMenu') !== '1' && modeType !== 'group') {
      setIsOpen(true)
    }
  }, [])

  const toggleMenu = (e: any) => {
    e.stopPropagation()
    setIsOpen(prev => !prev)
    localStorage.setItem('hasShowChatMenu', '1')
  }

  return <div className={`flex z-20 relative ${s.btn} ml-2 mr-3`}>
    <div className="w-52 text-right relative z-10" onClick={toggleMenu}>
        <button 
          {...(isOpen ? { 'data-open': '' } : {})}
          className="inline-flex items-center gap-2 rounded-md text-purple-500 border border-purple-500 dark:border-gray-300 bg-white dark:bg-gray-900 py-1 px-2 text-sm/6 font-semibold dark:text-gray-300 shadow-white/10 focus:outline-none data-[hover]:bg-gray-700 data-[open]:bg-gray-100 dark:data-[open]:bg-gray-800 data-[focus]:outline-1 data-[focus]:outline-white ${s.btn} ml-2 mr-2">
          <Bars3Icon className='w-5 h-5' />
        </button>
        <div
          className={`absolute drop-shadow z-50 w-52 origin-bottom-left bottom-10 left-2 rounded-xl border border-white/5 dark:bg-gray-950/95 bg-white p-1 text-sm/6 text-gray-500 dark:text-white transition-opacity transition-transform duration-200 ease-out [--anchor-gap:var(--spacing-1)] focus:outline-none ${
            isOpen ? 'opacity-100 scale-100' : 'opacity-0 scale-95 pointer-events-none left-[-9999px]'
          }`} >
          <div>
            <button className={btnClass} onClick={async () => {
            const res = await comfirm.show({
                title: t('app.chat.warn'),
                desc: t('app.chat.warn_desc')
            })
            if (res?.confirm) {
                startNewChat()
            }
            }}>
              <div className={iconClass}><IconClean /></div>
              {t('app.chat.start_new')}
            </button>
          </div>
          <div className="my-1 h-px bg-gray-200 dark:bg-white/5" />
          <div>
            
          </div>
          {/* <div className="my-1 h-px bg-white/5" /> */}
        </div>
    </div>
  </div>
}

export default ChatMenu;