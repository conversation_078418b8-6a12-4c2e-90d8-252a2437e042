
.count {
  /* display: none; */
  padding: 0 2px;
}

.sendBtn {
  background: url(./icons/send-active.svg) center center no-repeat;
  background-size: 48px auto;
}

.sendBtn:hover {
  background-image: url(./icons/send-active.svg);
  /* background-color: #EBF5FF; */
}

.textArea:focus+div .count {
  display: block;
}

.textArea:focus+div .sendBtn {
  background-image: url(./icons/send-active.svg);
}

.shadow {
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}

.btn{
  padding: 0;
  box-sizing: border-box;
  height: 56px;
  width: 48px;
  flex-shrink: 0;
  border-radius: 25px;
  color: #fff;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ipt {
  width: 100%;
  flex-shrink: 0;
  border-radius: 26px;
  background: rgb(255, 255, 255);
  font-size: 14px;
  /* padding: 18px 52px 18px 45px; */
  box-shadow: 0px 0px 0px 1px rgba(0, 0, 0, 0.05), 0px 2px 4px 0px rgba(0, 0, 0, 0.09);
  overflow: auto;
}

.loader {
  
}
.loader:after {
  content:"";
  display: inline-block;
  width: 20px;
  margin-top: 9px;
  aspect-ratio: 4;
  --_g: no-repeat radial-gradient(circle closest-side,#6f6f6f 90%,#fff);
  background: 
    var(--_g) 0%   50%,
    var(--_g) 50%  50%,
    var(--_g) 100% 50%;
  background-size: calc(100%/3) 100%;
  animation: b2 1s infinite linear;
}
.loaderDark:after {
  --_g: no-repeat radial-gradient(circle closest-side,#fff 90%,#0000);
}
@keyframes b2 {
  33%{background-size:calc(100%/3) 0%  ,calc(100%/3) 100%,calc(100%/3) 100%}
  50%{background-size:calc(100%/3) 100%,calc(100%/3) 0%  ,calc(100%/3) 100%}
  66%{background-size:calc(100%/3) 100%,calc(100%/3) 100%,calc(100%/3) 0%  }
}