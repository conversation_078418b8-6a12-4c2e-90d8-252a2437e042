'use client'
import { setLocaleOnClient } from '@little-tavern/shared/src/i18n/client'
import { switchLanguages } from '@little-tavern/shared/src/i18n/settings'
import IconLang from '@little-tavern/shared/src/ui/icon/lang'
import { usePathname } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import React from 'react'

const LangSwitch = ({className = ''}: {className?: string}) => {
    const { t, i18n } = useTranslation()
    const lang = i18n.language
    const pathname = usePathname()
    const change = (l: string) => {
        const searchStr = window.location.search;
        setLocaleOnClient(l)
        // 切换语言后，重新刷新页面，重置状态
        location.replace(pathname.replace(lang, l) + searchStr + '#reload=1')
    }

    const setLang = (e: any) => {
        change(e.target.value)
    }

    return <>
    {switchLanguages.length > 1 && <div className={`${className} flex text-sm items-center py-4 px-1.5 relative text-zinc-500`}>
        <IconLang className='w-3.5 h-3.5 mr-0.5' />{t(`app.lan_short.${lang}`)}
        <select className='text-center bg-white dark:bg-black dark:text-white w-full h-full absolute opacity-0 left-0 top-0 cursor-pointer' value={lang} onChange={setLang}>
            {switchLanguages.map((l, index) => {
                return (
                    <option key={l} value={l}>{t(`app.lang.${l}`)}</option>
                )
            })}
        </select>
    </div>}
    </>
}

export default LangSwitch