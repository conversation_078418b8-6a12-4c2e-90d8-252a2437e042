'use client'
// 通用导航栏
import React, { Fragment, useContext, memo, useEffect, useState } from 'react'
import { AuthContext } from '@little-tavern/shared/src/authContext';
import {ArrowUturnLeftIcon} from '@heroicons/react/24/solid'
import { usePara<PERSON>, useRouter } from 'next/navigation';
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import { useTranslation } from 'react-i18next'
import LangSwitch from './langSwitch';
import { useBackButton } from '@share/src/hooks/useBackButton'

const MyNav = ({children, showSwitchLang = true}: any) => {
    const { t } = useTranslation()
    // Use the custom hook with the back function
    const { backHandler } = useBackButton({ useBackFn: showSwitchLang })
    
    return <nav className='fixed dark:bg-[var(--background)] bg-white drop-shadow-u dark:drop-shadow-none z-10 w-full left-0 top-0 py-2 dark:border-b dark:border-[#2c2e2d]'>
        <button className='absolute p-2 left-1 top-0.5 flex text-base items-center hover:bg-gray-200 dark:hover:bg-gray-800 rounded' onClick={backHandler}>
            <ArrowUturnLeftIcon className='w-5 h-5 mr-1 text-purple-500' />
            {t('app.common.back')}
        </button>
        <div className='w-fit mx-auto text-sm h-7 flex items-center justify-center'>
            <div className='flex items-center flex-shrink-0 text-gray-600 dark:text-gray-200'>
                <div className="text-center text-sm w-[60vw] text-ellipsis">
                {children}
                </div>
            </div>
        </div>
        {showSwitchLang && <LangSwitch className='!absolute !py-3 right-0 top-0' />}
    </nav>
}


export default memo(MyNav)