import Link from "next/link"
import { useEffect, useState } from "react"
import useRequest from "@share/src/hook/useRequest"
import { useTranslation } from "react-i18next"
import {web} from '@/app/module/global'

const Share = ({ className }: any) => {
    const { t } = useTranslation()
    const request = useRequest()
    const [shareLink, setShareLink] = useState(null)
    const getShareLink = async () => {
        const res = await request('/tasks/my_invite_link')
        if (res.error_code === 0) {
            setShareLink(res.data?.link)
        }
    }
    useEffect(() => {
        getShareLink()
    }, [])
    return (
        <>
            {shareLink && !web && <div className={`${className} absolute right-2 top-1.5 priBtn px-3 py-1 !rounded-md text-sm`}>
                <Link target="_blank" href={shareLink}>{t('app.share.share_btn')}</Link>
            </div>}
        </>
    )
}

export default Share