import cn from 'classnames'
import type { FC } from 'react'
import { forwardRef } from 'react';

type IProps = {
  placeholder?: string
  value: string
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  className?: string
  minHeight?: number
  maxHeight?: number
  onKeyDown?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void
  onKeyUp?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void
  onBlur?: (e: React.FocusEvent<HTMLTextAreaElement>) => void
  onfocus?: (e: React.FocusEvent<HTMLTextAreaElement>) => void
}

const AutoHeightTextarea = forwardRef((
  { value, onChange, placeholder, className, minHeight = 56, maxHeight = 96, onKeyDown, onKeyUp, onBlur, onfocus }: IProps, ref: any
) => {
  return (
    <div className={cn(className, 'relative')}>
      <div className={cn('invisible whitespace-pre-wrap break-all overflow-y-auto pl-12 pt-4 pr-14 pb-1.5')} style={{ minHeight, maxHeight }}>
        {!value ? placeholder : value.replace(/\n$/, '\n ')}
      </div>
      <textarea
        ref={ref}
        className={cn('absolute left-12 top-4 right-14 bottom-1.5 resize-none overflow-auto block dark:bg-[#212121] outline-0 outline-none')}
        placeholder={placeholder}
        onChange={onChange}
        onKeyDown={onKeyDown}
        onKeyUp={onKeyUp}
        value={value}
        // @ts-ignore
        enterKeyHint={'send'}
        onBlur={onBlur}
        onFocus={onfocus}
        // autoFocus
      />

    </div>
  )
})
AutoHeightTextarea.displayName = 'AutoHeightTextarea';

export default AutoHeightTextarea
