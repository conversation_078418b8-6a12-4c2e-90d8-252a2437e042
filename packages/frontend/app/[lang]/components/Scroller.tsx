import { useEffect } from "react";
import debounce from 'lodash.debounce';
import cn from 'classnames'
import { useTranslation } from 'react-i18next'
import { LoadingToast } from '@little-tavern/shared/src/ui/Loading'

type IScroller = {
    children: React.ReactNode,
    containerRef: React.RefObject<HTMLDivElement>,
    loadMore: () => void,
    isLoading: boolean,
    hasMore: boolean,
    offset: number
}
const Scroller: React.FC<IScroller> = ({ children, containerRef, loadMore, isLoading, hasMore, offset }) => {
    const { t } = useTranslation()
    const handleScroll = debounce(() => {
        if (containerRef.current &&
            containerRef.current.clientHeight + containerRef.current.scrollTop + 800 >
            containerRef.current.scrollHeight
        ) {
            loadMore();
        }
    }, 50); // 50ms 的防抖延迟
    // 初始化时监听滚动事件
    useEffect(() => {
        const currentContainer = containerRef.current;

        if (currentContainer) {
            currentContainer.addEventListener('scroll', handleScroll);
        }

        return () => {
            currentContainer?.removeEventListener('scroll', handleScroll);
        }
    }, [handleScroll]);
    const checkForScrollbar = () => {
        const currentContainer = containerRef.current;
        // Only check when we're at the first page (offset === 0)
        if (offset === 0 && currentContainer && hasMore && !isLoading) {
            // If there's no scrollbar (content height <= container height) and we have more content to load
            if (currentContainer.scrollHeight <= currentContainer.clientHeight) {
                loadMore();
            }
        }
    };
    // 自动补充加载，解决内容不足一屏时无法滚动的问题
    useEffect(() => {
        checkForScrollbar()
    }, [children]);
    return <>
        {children}
        {
            <div className={cn((isLoading || !hasMore) && offset != 0 ? 'opacity-100' : 'opacity-0',
                'h-6'
            )}>
                {isLoading && <p className={'text-center text-xs mb-1'}>{t('app.index.loading')}...</p>}
                {!hasMore && !isLoading && <p className='text-center text-xs mb-1'>{t('app.index.no_card_load')}</p>}
            </div>
        }
        {isLoading && offset == 0 && <LoadingToast msg={t('app.common.loading')} />}
    </>
}

export default Scroller