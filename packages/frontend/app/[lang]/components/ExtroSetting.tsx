'use client'
import cn from 'classnames'
import type { ReactEventHandler, ReactNode } from 'react'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import Toast from '@little-tavern/shared/src/ui/toast'
import { useForm, SubmitHandler, useController, Controller, useFieldArray, useWatch } from "react-hook-form"
import { AuthContext } from '@little-tavern/shared/src/authContext'
import { ChevronDownIcon, PhotoIcon, UserCircleIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import { createPortal } from 'react-dom';
import Modal from '@share/src/ui/dialog/Modal'
import Illustrate from '@share/src/ui/tooltip/illustrate'
import AutoTextareaHeight from '@share/src/ui/dialog/CreateCard/AutoTextareaHeight'
import useComfirm from '@share/src/hook/useComfirm'
import { getByteLength } from '@share/src/module/stringTool';
import _ from 'lodash';

export type ISetting = {
  models: any,
  onSelect: ReactEventHandler
}

type FormData = {
  extroList: Extro[],
}

type Extro = {
  title: string;
  description?: string;
  enable?: boolean;
}
const itemTokensLimit = 80
const ExtroSetting = () => {
  const { t } = useTranslation()
  const [isEdit, setIsEdit] = useState(false)
  const request = useRequest();
  const comfirm = useComfirm();
  // 记录每个item的tokens
  const [contentTokens, setContentTokens] = useState<{ [key: number]: number }>({});
  const {
    register,
    handleSubmit,
    watch,
    getValues,
    control,
    trigger,
    setValue,
    formState: { errors },
  } = useForm<FormData>()
  const updateExtroInfo = async (data: any, isShowToast = true) => {
    Toast.showLoading('');
    const list = data.extroList.map((item: any) => ({
      ...item,
      status: item.enable ? 2 : 1
    }));
    try {
      const res = await request(`/user/alt_persona/update`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(list)
      });
      isShowToast && Toast.notify({ type: 'success', message: t('app.mine.update_success') })
      Toast.hideLoading();
      setIsEdit(false);
    } catch (e) {
      console.error('Upload error:', e);
      isShowToast && Toast.notify({ type: 'success', message: t('app.mine.create_failed') })
      Toast.hideLoading();
    }
  }
  const onSubmitExtroInfo: SubmitHandler<FormData> = async (data) => {
    updateExtroInfo(data)
  }

  // Add form validation before submitting
  const validateAllFields = () => {
    // Validate all textarea fields, even if not visible
    fields.forEach((_, index) => {
      trigger(`extroList.${index}.title`);
      trigger(`extroList.${index}.description`);
    });
    
    // If there are errors, make sure the relevant fields are expanded
    if (errors?.extroList) {
      const errorIndex = Object.keys(errors.extroList).map(Number)[0];
      if (errorIndex !== undefined && errorIndex >= 0) {
        setSelectedIndex(errorIndex);
      }
    }
  };

  let { fields, append, remove } = useFieldArray({
    control,
    name: "extroList",
    rules: {
      validate: {

      }
    }
  });
  const [selectedIndex, setSelectedIndex] = useState(0)
  const addExtraInfo = () => {
    const len = fields.length;
    if (len >= 3) {
      Toast.notify({ type: 'warning', message: t('app.mine.extra_info_limit') })
      return;
    }
    append({ title: "", enable: false })
    setSelectedIndex(len);
  }

  const toggleHandle = (index: number) => {
    if (selectedIndex !== -1 && selectedIndex === index) {
      setSelectedIndex(-1)
    } else {
      setSelectedIndex(index)
    }
  }

  // 记录上一次的extroList，只有更新的内容才计算tokens
  const [prevRoleBook, setPrevRoleBook] = useState<any[]>([]);
  let abortControllers: any = useRef<any>(null);

  const countContentToken = async (content: string, index: number) => {
    // console.log('countContentToken', index);
    if (abortControllers?.current) {
      abortControllers.current.abort('debounce自动取消');
      abortControllers.current = null;
    }
    abortControllers.current = new AbortController();
    try {
      const res = await request('/tokenizers/count', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ content }),
        signal: abortControllers.signal
      });
      setContentTokens(prev => ({
        ...prev,
        [index]: res.token_count
      }));
      setTimeout(() => {
        // 触发校验特定的content字段校验
        trigger(`extroList.${index}.description`);
      }, 10);
      abortControllers.current = null;
      return res.token_count;
    } catch (error) {
      console.error('Error counting tokens:', error);
    }
  };
  const debouncedCountToken = useCallback(
    _.debounce(countContentToken, 500),
    []
  );

  const watchFileds = useWatch({
    name: "extroList",
    control
  });

  useEffect(() => {
    if (watchFileds && prevRoleBook) {
      watchFileds.forEach((item: any, index: number) => {
        const prevItem = prevRoleBook[index];
        // prevItem为空的时候，说明是新增的或者第一次进入，有内容并且内容发生了变化
        if (prevItem && (item.description && item.description !== prevItem?.description)) {
          if (item.description) {
            debouncedCountToken(item.description, index);
          } else {
            setContentTokens(prev => ({
              ...prev,
              [index]: 0
            }));
          }
        }
      });
    }
    setPrevRoleBook(watchFileds);
  }, [watchFileds]);

  const showEdit = async () => {
    Toast.showLoading('');
    try {
      const res = await request(`/user/alt_persona`);
      if (res.error_code === 0) {
        if (res.data.persona_settings?.length > 0) {
          setValue('extroList', res.data.persona_settings.map((item: any) => ({
            ...item,
            enable: item.status === 2
          })));
        } else {
          setValue('extroList', [{ title: "", description: "", enable: false }]);
        }
      }
      setIsEdit(true);
    } catch (e) {
      Toast.notify({ type: 'error', message: t('app.common.load_err') })
      console.error('getExtroInfo error:', e);
    } finally {
      Toast.hideLoading();
    }
  }

  return <>
    <div className='flex justify-between items-center mx-2 bg-white dark:bg-gray-900 rounded'>
      <button type='button' onClick={showEdit} className='priBtn !rounded dark:bg-gray-800 bg-gray-200 dark:text-gray-100 text-gray-500 px-3 py-1.5 text-xs'>{t('app.mine.extra_info')}</button>
    </div>
    {isEdit && createPortal(<Modal isOpen={true} onClose={() => { setIsEdit(false) }}><>
      <div className="pb-2 pt-5 flex flex-wrap gap-6 justify-center sm:justify-normal">
        <div className="rounded-md w-full dark:border-gray-500 dark:bg-gray-900 bg-white max-h-[88vh] overflow-auto">
          <div className='space-y-5 pb-1'>
            <div>
              <h3 className='mb-3'>{t('app.mine.extra_info')}
                <Illustrate className="ml-1" title={t('app.common.tips')} desc={t('app.mine.extra_info_desc')} />
                <span className="ml-2 text-xs bg-red-500 text-white rounded px-1.5 py-0.5">{t('app.mine.vip_limit')}</span>
              </h3>
              <form action="#" method="POST" onSubmit={handleSubmit((data) => {
                validateAllFields();
                if (Object.keys(errors).length === 0) {
                  onSubmitExtroInfo(data);
                }
              })}>
                {fields.map((item: any, index: number) => {
                  return <div key={item.id} className='my-2'>
                    <div className='text-sm font-medium leading-6 flex justify-between bg-gray-200 dark:bg-gray-800 items-center pl-2'>
                      <div className='flex items-center flex-1'>
                        <span className='mr-1'>{index}.</span>
                        <label htmlFor={`extroList.${index}.enable`} className="flex items-center mr-2 w-14 shrink">
                          <input className='ml-1 mr-0.5' type='checkbox' id={`extroList.${index}.enable`} {...register(`extroList.${index}.enable`)} />
                          <span className='block'>{t('app.mine.select')}</span>
                        </label>
                        <Controller
                          name={`extroList.${index}.title`}
                          control={control}
                          rules={{
                            required: t('app.dialog.require'),
                            validate: {
                              len: (value: any) => {
                                const byteLength = getByteLength(value || '');
                                return byteLength <= 30 || t('app.dialog.name_len');
                              }
                            }
                          }}
                          render={({ field }) => {
                            let errMsg: any = '';
                            if (errors?.extroList?.[index]?.title?.message) {
                              errMsg = <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5 mb-1'>{errors?.extroList?.[index]?.title?.message}</span>
                            }
                            return <div className='flex-1'>
                              {errMsg}
                              <div className='flex items-center'>
                                <input
                                  {...field}
                                  type="text"
                                  value={field.value || ''}
                                  placeholder={t('app.mine.extra_info_title_placeholder')}
                                  className={`ipt py-1 text-sm flex-1`}
                                /></div></div>
                          }} />
                      </div>
                      <div className='flex items-center'>
                        <button className='ml-2 p-0.5 text-xs text-blue-500' type="button" onClick={
                          async (e) => {
                            e.stopPropagation();
                            const isDel = await comfirm.show({});
                            console.log('isDel', isDel);
                            if (isDel?.confirm) {
                              remove(index)
                            }
                          }}>{t('app.common.del')}</button>
                        <button type='button' className='flex items-center px-3 py-1' onClick={() => toggleHandle(index)}><ChevronDownIcon className={cn("-mr-1 h-5 w-5 text-violet-500 dark:text-violet-200 dark:hover:text-violet-100 transition", selectedIndex === index && "rotate-180")} /></button>
                      </div>
                    </div>
                    <Controller
                      name={`extroList.${index}.description`}
                      control={control}
                      rules={{
                        required: t('app.dialog.require'),
                        validate: {
                          tokenLimit: async (value) => {
                            // console.log('tokenLimit', value);
                            if (!value) return true;
                            if (contentTokens[index] > itemTokensLimit) {
                              Toast.notify({
                                type: 'warning',
                                message: t('app.cardEdit.token_limit_exceeded', { limit: itemTokensLimit })
                              })
                              return t('app.cardEdit.token_limit_exceeded', { limit: itemTokensLimit })
                            } else {
                              return true;
                            }
                          },
                        }
                      }}
                      render={({ field }) => <div className='mt-2'>
                        {(selectedIndex === index) && <>
                          {contentTokens[index] > 0 && (
                            <span className="text-sm ">
                              ({contentTokens[index]} tokens)
                            </span>
                          )}
                          {errors?.extroList?.[index]?.description && (
                            <span className="ml-1 text-xs text-white bg-red-700 px-1 rounded inline-block py-0.5">{errors?.extroList?.[index]?.description?.message}</span>
                          )}
                          <AutoTextareaHeight
                            {...field}
                            rows={3}
                            className={cn(`flex-1 dark:bg-gray-800 ipt`)} placeholder={t('app.mine.extra_info_placeholder')} /></>}
                      </div>}
                    />
                  </div>
                })}
                <button className='w-full dark:bg-gray-700 bg-gray-300 mt-1 py-1.5 text-sm rounded text-center' type="button" onClick={addExtraInfo}>
                  {t('app.cardEdit.add')}
                </button>
                <button className='mt-2 priBtn w-full p-1.5 px-3 bg-purple-500 text-white rounded text-sm' type="button" onClick={() => {
                  validateAllFields();
                  // After validation, if no errors, submit the form
                  setTimeout(() => {
                    if (Object.keys(errors).length === 0) {
                      handleSubmit(onSubmitExtroInfo)();
                    }
                  }, 100); // Small delay to ensure validation completes
                }}>{t('app.mine.update')}</button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </></Modal>, document.body)}
  </>
}

export default ExtroSetting