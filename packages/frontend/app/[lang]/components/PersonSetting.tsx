'use client'
import cn from 'classnames'
import type { ReactEventHandler, ReactNode } from 'react'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import Toast from '@little-tavern/shared/src/ui/toast'
import { useForm, SubmitHandler, useController, Controller, useFieldArray, useWatch } from "react-hook-form"
import { AuthContext } from '@little-tavern/shared/src/authContext'
import Image from 'next/image'
import { ChevronDownIcon, PhotoIcon, UserCircleIcon } from '@heroicons/react/24/solid'
import useCrop from '@little-tavern/shared/src/hook/useCrop'
import { useTranslation } from 'react-i18next'
import { useConfig } from '@share/src/configContext'
import { createPortal } from 'react-dom';
import Modal from '@share/src/ui/dialog/Modal'
import Illustrate from '@share/src/ui/tooltip/illustrate'
import useComfirm from '@share/src/hook/useComfirm'
import { getByteLength } from '@share/src/module/stringTool';
import _ from 'lodash';
import ExtroSetting from './ExtroSetting'
import EditUser from './editUser'

export type ISetting = {
  models: any,
  onSelect: ReactEventHandler
}

type UserItem = {
  id?: string;
  user_id?: number;
  nickname: string;
  avatar?: string;
  enable?: boolean;
}

const PersonSetting = () => {
  const { t } = useTranslation()
  const request = useRequest();
  const auth = useContext(AuthContext)
  const user: any = auth?.user;
  const [isShowEdit, setIsShowEdit] = useState(false)
  const [isShowUser, setIsShowUser] = useState(false)
  const comfirm = useComfirm();
  const [userList, setUserList] = useState<UserItem[]>([])
  const [editUser, setEditUser] = useState(null)
  // id最小的用户账号，最小的用户账号是默认账号不能删除
  const [minId, setMinId] = useState(0)
  // console.log('persion setting')

  const addUserInfo = () => {
    const len = userList.length;
    if (len >= 3) {
      Toast.notify({ type: 'warning', message: t('app.mine.extra_user_limit') })
      return;
    }
    setIsShowEdit(true)
  }

  const removeUser = async (id: number) => {
    const res = await request(`/user/alt_profile/delete?profile_id=${id}`, {
      method: 'POST'
    })
    if (res.error_code === 0 && res.data.result) {
      Toast.notify({ type: 'success', message: t('app.common.del_seccess') })
    }
    getUserInfo();
  }

  const selectUser = async (userInfo: UserItem) => {
    Toast.showLoading('');
    try {
      const res = await request(`/user/alt_profile/active?profile_id=${userInfo.id}`, {
        method: 'POST'
      })
      if (res.error_code === 0) {
        Toast.notify({ type: 'success', message: t('app.mine.switch_success') })
        auth?.updateUserInfo({
          avatar: userInfo.avatar,
          nickname: userInfo.nickname
        })
        // 更新userlist
        setUserList(userList.map((item: any) => {
          if (item.id === userInfo.id) {
            return {
              ...item,
              enable: true
            }
          } else {
            return {
              ...item,
              enable: false
            }
          }
        }))
      }
    } catch (e) {
      Toast.notify({ type: 'error', message: t('app.common.load_err') })
      console.error('getExtroInfo error:', e);
    } finally {
      Toast.hideLoading();
    }
  }
  const getUserInfo = async () => {
    Toast.showLoading('');
    try {
      const res = await request(`/user/alt_profile`)
      if (res.error_code === 0) {
        if (res.data.alt_profile_list?.length > 0) {
          setUserList(res.data.alt_profile_list.map((item: any) => ({
            ...item,
            enable: item.status === 2
          })))
          const userInfo = res.data.alt_profile_list.find((item: any) => item.status === 2)
          auth?.updateUserInfo({
            avatar: userInfo.avatar,
            nickname: userInfo.nickname
          })
          setMinId(res.data.alt_profile_list.reduce((pre: number, cur: any) => Math.min(pre, cur.id), Number.MAX_SAFE_INTEGER))
        } else {
          setUserList([{ nickname: "", avatar: "", user_id: user?.id, enable: false }]);
        }
      }
      setIsShowUser(true);
    } catch (e) {
      Toast.notify({ type: 'error', message: t('app.common.load_err') })
      console.error('getExtroInfo error:', e);
    } finally {
      Toast.hideLoading();
    }
  }

  const showUser = () => {
    getUserInfo();
  }

  useEffect(() => {
    const query = new URLSearchParams(window.location.search);
    const action = query.get('action');
    if (action === 'edit') {
      showUser()
    }
  }, [])

  return <>
    <div className='flex justify-between items-center mx-2 px-3 mt-2 py-1 bg-white dark:bg-gray-900 rounded'>
      <div className='flex items-center'>
        {(user?.avatar) ? <Image className='rounded-full h-12 w-12 object-cover' src={user?.avatar} width={48} height={48} alt={'avatar'} unoptimized={true} /> : <UserCircleIcon className="h-12 w-12 text-gray-500" aria-hidden="true" />}
        <div className='sm:w-full sm:max-w-sm dark:text-white'>
          <div className="ml-3 w-20">
            {user?.nickname}
          </div>
          <div className="ml-3 w-20 text-sm text-gray-500">
            {t('app.mine.user_id')}: {user?.id}
          </div>
        </div>
      </div>
      <div className='flex items-center'>
        <button type='button' onClick={showUser} className='priBtn !rounded dark:bg-gray-800 bg-gray-200 dark:text-gray-100 text-gray-500 px-3 py-1.5 text-xs'>{t('app.operation.edit')}</button>
        {/* <ExtroSetting /> */}
      </div>
    </div>
    {isShowUser && createPortal(<Modal isOpen={true} onClose={() => { setIsShowUser(false) }}><>
      <div className="pb-2 pt-5 flex flex-wrap gap-6 justify-center sm:justify-normal">
        <div className="rounded-md w-full dark:border-gray-500 dark:bg-gray-900 bg-white max-h-[88vh] overflow-auto">
          <div className='space-y-5 pb-1'>
            <h3 className='mb-3'>
              {t('app.mine.user_role')}
            </h3>
            {
              userList.map((item: any, index: number) => {
                return <div key={item.id} className='mb-2'><div className='text-sm font-medium leading-6 flex justify-between bg-gray-200 dark:bg-gray-800 items-center px-2'>
                  <div className='flex items-center flex-1'>
                    <span className='mr-1'>{index + 1}.</span>
                    <label htmlFor={`user.${index}.enable`} className="flex items-center mr-2 shrink py-1">
                      <input name='user' className='ml-1 mr-1' type='radio' id={`user.${index}.enable`} defaultChecked={item.enable} value={item.enable} onChange={() => selectUser(item)} />
                      <span className='block mr-1'>{item.nickname}</span>
                      {item.avatar ? <Image className='rounded-full h-8 w-8 object-cover' src={item.avatar} width={32} height={32} alt={'avatar'} /> : <UserCircleIcon className="h-8 w-8 text-gray-500 mr-1" aria-hidden="true" />}
                    </label>
                  </div>
                  <div className='flex items-center'>
                    <button className='p-2 text-xs text-blue-500' type="button" onClick={
                      async (e) => {
                        e.stopPropagation();
                        setEditUser(item)
                        setIsShowEdit(true)
                      }}>{t('app.cardEdit.edit')}</button>
                    {userList.length > 1 && item.id !== minId && <button className='p-2 text-xs text-blue-500' type="button" onClick={
                      async (e) => {
                        e.stopPropagation();
                        // 选中状态不能删除
                        if (item.enable) {
                          Toast.notify({ type: 'warning', message: t('app.mine.cannot_delete_enabled') })
                          return;
                        } else {
                          const isDel = await comfirm.show({});
                          console.log('isDel', isDel);
                          if (isDel?.confirm) {
                            removeUser(item.id)
                          }
                        }
                      }}>{t('app.common.del')}</button>}

                    {/* <button type='button' className='flex items-center px-3 py-1' onClick={() => toggleUserHandle(index)}><ChevronDownIcon className={cn("-mr-1 h-5 w-5 text-violet-500 dark:text-violet-200 dark:hover:text-violet-100 transition", selectedUserIndex === index && "rotate-180")} /></button> */}
                  </div>
                </div>
                </div>
              })
            }

            <button className='w-full dark:bg-gray-700 bg-gray-300 mt-1 py-1.5 text-sm rounded text-center' type="button" onClick={addUserInfo}>
              {t('app.cardEdit.add')}
            </button>
          </div>
        </div>
      </div>

    </></Modal>, document.body)}
    {
      isShowEdit && <EditUser onClose={() => { setIsShowEdit(false); setEditUser(null) }} onSuccess={() => getUserInfo()} editUser={editUser} />
    }
  </>
}

export default React.memo(PersonSetting)