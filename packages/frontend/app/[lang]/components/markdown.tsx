import ReactMarkdown from 'react-markdown'
import React from 'react'
import 'katex/dist/katex.min.css'
import RemarkBreaks from 'remark-breaks'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneLight, coldarkDark } from 'react-syntax-highlighter/dist/esm/styles/prism'
import type { FC } from 'react'
import DOMPurify from 'dompurify';
import rehypeRaw from 'rehype-raw'
import { useTheme } from 'next-themes'
import { isArray } from 'lodash';
import remarkGfm from 'remark-gfm';

export type IMarkdownProps = {
  content: string
  regs?: any[]
  formateType?: string
  roleName?: string
  isPage?: boolean
  // highlight: string
}

// 创建自定义样式
const customDarkStyle = {
  ...coldarkDark, // 基于已有的主题
  'code[class*="language-"]': {
    ...coldarkDark['code[class*="language-"]'],
    background: 'rgb(28 33 44)',
    color: 'var(--tw-prose-body)'
  },
  'pre[class*="language-"]': {
    ...coldarkDark['pre[class*="language-"]'],
    background: 'rgb(28 33 44)',
    color: 'var(--tw-prose-body)'
  },
};
const customLightStyle = {
  ...oneLight, // 基于已有的主题
  'code[class*="language-"]': {
    ...oneLight['code[class*="language-"]'],
    background: '#fbfbfd',
  },
  'pre[class*="language-"]': {
    ...oneLight['pre[class*="language-"]'],
    background: '#fbfbfd',
    border: '1px solid #e1e1e5'
  },
};

const Markdown: FC<IMarkdownProps> = ({
  content,
  regs,
  formateType,
  roleName,
  // 是否是文档页面
  isPage = false
}) => {
  // content = `~~哈哈哈哈~`
  // console.log('regs', regs, content);
  // 双引号特殊处理
  // console.log('md');
  const regex = /(<script\b[^>]*>)|(<\/script>)/gi;
  content = content?.replace(regex, '').trim() || '';
  const { resolvedTheme } = useTheme()
  // 如果formateType为Chat，对带括号的进行换行处理
  if(formateType === 'Chat' && isArray(regs)) {
    regs.push({
      "title": "fix light chat (",
      "regex": "/(?<!\\<sp>.*)(?<![\"“][^\\n\\r]*?)(?<![\\n\\r\\>\\^])\\s*?([(（])/gs",
      "replacement": "\n$1",
      "affects": [
        "AI_OUTPUT"
      ],
      "options": [
        "FORMAT_DISPLAY"
      ]
    })
    regs.push({
      "title": "fix light chat )",
      "regex": "/(?<!\\<sp>.*)(?<![\\n\\r\\>\\^])([)）])\\s*?(?![\\r\\n\\<$])/gs",
      "replacement": "$1\n",
      "affects": [
        "AI_OUTPUT"
      ],
      "options": [
        "FORMAT_DISPLAY"
      ]
    })
  }
  // 后台正则匹配
  regs?.forEach((reg: any) => {
    if (reg.affects.includes('AI_OUTPUT') && reg.options.includes('FORMAT_DISPLAY')) {
      
      try {
        const regex = reg.regex.replace('{{char}}', roleName || '')
        // console.log('regex', regex);
        const [, pattern, flags] = regex.match(/^\/(.*?)\/([gimuys]*)$/);
        const regexPattern = new RegExp(pattern, flags);
        content = content.replace(regexPattern, reg.replacement);
        // console.log('regexPattern', regexPattern, content);
      } catch (e) {
        console.log('markdown,reg', e);
      }
    }
  })
  // console.log('后台正则过滤后的文本', content);
  let _content = DOMPurify.sanitize(content, { KEEP_CONTENT: true, ALLOWED_TAGS: ['details', 'summary'] });
  // console.log('DOMPurify清楚标签后的文本', _content);
  // “” ""「 」 『』采用黄色，（）括号用蓝色
  if(!isPage) {
    _content = _content.replace(/```[\s\S]*?```|```[\s\S]*|``[\s\S]*?``|`[\s\S]*?`|(".+?")|(\u201C.+?\u201D)|([\(\（].+?[\)\）])|(\u300C.+?\u300D)|(\u300E.+?\u300F)/gm, function (match, p1, p2, p3, p4, p5) {
      if (p1) {
        return '<q>"' + p1.replace(/"/g, '') + '"</q>';
      } else if (p2) {
        return '<q>"' + p2.replace(/\u201C|\u201D/g, '') + '"</q>';
      } else if (p3) {
        return '<span class="dark:text-sky-300 text-blue-500">' + p3 + '</span>';
      } else if (p4) {
        return '<q>' + p4 + '</q>';
      } else if (p5) {
        return '<q>' + p5 + '</q>';
      } else {
        return match;
      }
    });
  }
  // 特殊换行格式化，状态栏内不处理
  if (formateType === 'Chat') {
    // 替换所有的换行符为特定标记
    let temp = _content.replace(/\n/g, "__NEWLINE__");
    // 恢复```代码块中的换行符
    temp = temp.replace(/```[\s\S]*?```|```[\s\S]*/g, match => match.replace(/__NEWLINE__/g, "\n"));
    // 恢复<details>标签中的换行符
    temp = temp.replace(/<details>[\s\S]*?<\/details>/g, match => match.replace(/__NEWLINE__/g, "\n"));
    // 替换标记为两个换行符
    _content = temp.replace(/__NEWLINE__/g, "\n\n");
  }
  // process.env.NEXT_PUBLIC_RM_CONSOLE != "true" && console.log('所有内容正则处理后的内容：', _content);
  return (
    <div className="">
      <ReactMarkdown
        remarkPlugins={[RemarkBreaks, remarkGfm]}
        rehypePlugins={[
          [rehypeRaw]
        ]}
        unwrapDisallowed={true}
        components={{
          code(props) {
            const { children, className, node, ...rest } = props
            const match = /language-(\w+)/.exec(className || '') || ['', undefined]
            return match ? (
              <SyntaxHighlighter
                PreTag="div"
                language={'javascript'}
                style={resolvedTheme === 'dark' ? customDarkStyle : customLightStyle}
                wrapLongLines={true}
                customStyle={{
                  padding: '12px 10px 12px 10px',
                  borderRadius: '0.5rem',
                  fontSize: '14px'
                }}
              >{String(children).replace(/\n$/, '')}</SyntaxHighlighter>
            ) : (
              <code {...rest} className={className}>
                {children}
              </code>
            )
          },
          // details(props) {
          //   return <details className='whitespace-pre-line'>{props.children}</details>
          // },
          a(props) {
            return (
              <a {...props} className={props.className} onClick={(e) => {
                e.preventDefault()
                console.log('onClick')
              }}>
                [{props.children}]
              </a>
            )
          },
        }}
      >
        {_content}
      </ReactMarkdown>
    </div>
  )
}

export { Markdown }
export default React.memo(Markdown, (prevProps, nextProps) => {
  // 只有当 content 不同时才重新渲染
  return prevProps.content === nextProps.content;
})