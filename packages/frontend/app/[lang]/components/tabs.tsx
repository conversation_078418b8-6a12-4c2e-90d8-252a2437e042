import { useParams } from 'next/navigation'
import cn from 'classnames'
import Link from 'next/link';

const Tabs = ({tabs}: any) => {
    const params = useParams();
    const tab = params.tab;
    return (
        <div className='py-1 flex ml-2 space-x-3.5'>
          {tabs.map((elem: any) => {
            return <div key={elem.key}>
              <Link href={elem.link} className={cn('my-1.5 text-sm py-0.5', tab === elem.key ? 'dark:text-white text-purple-500 border-b-2 border-purple-500' : 'hover:bg-gray-200 dark:hover:bg-gray-800 text-gray-500 dark:text-gray-400')}>{elem.title}</Link>
            </div>
          })}
        </div>
    );
};

export default Tabs;