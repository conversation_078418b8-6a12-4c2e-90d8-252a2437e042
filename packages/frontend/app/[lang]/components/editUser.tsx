'use client'
import cn from 'classnames'
import type { ReactEventHandler, ReactNode } from 'react'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import useRequest from '@share/src/hook/useRequest'
import useTokenCounter from '@share/src/hook/useTokenCounter'
import Toast from '@little-tavern/shared/src/ui/toast'
import { useForm, SubmitHandler, useController, Controller, useFieldArray, useWatch } from "react-hook-form"
import { AuthContext } from '@little-tavern/shared/src/authContext'
import Image from 'next/image'
import { ChevronDownIcon, PhotoIcon, UserCircleIcon } from '@heroicons/react/24/solid'
import useCrop from '@little-tavern/shared/src/hook/useCrop'
import { useTranslation } from 'react-i18next'
import { createPortal } from 'react-dom';
import Modal from '@share/src/ui/dialog/Modal'
import _ from 'lodash';
import AutoTextareaHeight from '@share/src/ui/dialog/CreateCard/AutoTextareaHeight'
import Illustrate from '@share/src/ui/tooltip/illustrate'
import useDialogAlert from '@share/src/hook/useDialogAlert'

type UserFormData = {
  nickname: string;
  persona_setting: string;
}

const itemTokensLimit = 150
const EditUser = ({ onSuccess, onClose, editUser }: any) => {
  const { t } = useTranslation()
  const request = useRequest();
  const [avatar, setAvatar] = useState('')
  const [cropImg, setCropImg] = useState<Blob | string>('')
  const croper = useCrop();
  const auth = useContext(AuthContext)
  const user: any = auth?.user;
  const dialogAlert = useDialogAlert();
  // 记录每个item的tokens
  const [contentTokens, setContentTokens] = useState<number>(0);
  const {
    register,
    handleSubmit,
    formState: { errors },
    trigger,
    watch
  } = useForm<UserFormData>({
    defaultValues: {
      nickname: editUser?.nickname,
      persona_setting: editUser?.persona_setting
    }
  })
  const updateExtroInfo = async (data: any, isShowToast = true) => {
    Toast.showLoading('');
    const formData = new FormData()
    if (editUser !== null) {
      data.profile_id = editUser.id
    }
    formData.append('setting', JSON.stringify(data))
    cropImg && formData.append('avatar_img', cropImg)
    try {
      const res = await request(editUser ? `/user/alt_profile/update` : `/user/alt_profile/create`, {
        method: 'POST',
        body: formData
      });
      isShowToast && Toast.notify({ type: 'success', message: t('app.mine.update_success') })
      Toast.hideLoading();
      onSuccess()
      onClose()
    } catch (e) {
      console.error('Upload error:', e);
      isShowToast && Toast.notify({ type: 'success', message: t('app.mine.create_failed') })
      Toast.hideLoading();
    }
  }
  const onSubmitExtroInfo: SubmitHandler<UserFormData> = async (data) => {
    const nickname = data.nickname;
    let persona_setting = data.persona_setting;
    if (persona_setting.includes(nickname)) {
      data.persona_setting = persona_setting.replace(nickname, '{{user}}')
      await dialogAlert.show({
        title: t('app.common.tips'),
        desc: t('app.mine.extra_info_name_same'),
        alertStatus: '2'
      })
    } 
    updateExtroInfo(data)
  }

  const handleFileChange = async (e: any) => {
    const tmpFile = e.target.files[0];
    e.target.value = ''
    try {
      const file: any = await croper.show(URL.createObjectURL(tmpFile), 1, 0.9, 256);
      setCropImg(file);
      setAvatar(URL.createObjectURL(file));
    } catch (e) {
      console.log('handleFileChange e', e);
    }
  }

  const { debouncedCountTokens } = useTokenCounter();

  const persona_setting = watch('persona_setting');

  useEffect(() => {
    debouncedCountTokens(persona_setting, (tokenCount: number) => {
      setContentTokens(tokenCount);
    });
  }, [persona_setting, debouncedCountTokens]);
  return createPortal(<Modal isOpen={true} onClose={() => { onClose() }}>
    <div className="pb-2 pt-5 flex flex-wrap gap-6 justify-center w-full">
      <form className='w-full' action="#" method="POST" onSubmit={handleSubmit(onSubmitExtroInfo)}>
        <div className='ml-3 mt-3 mb-3'>
          <div className='sm:w-full sm:max-w-sm flex items-center'>
            <h3 className="block leading-6 ">
              {t('app.mine.nick_name')}
            </h3>
            <div className="ml-3 w-48">
              <input
                id="name"
                {...register(`nickname`)}
                type="name"
                autoComplete="name"
                required
                placeholder={t('app.mine.input_your_name')}
                className="ipt py-2 text-sm"
              />
            </div>
          </div>
          <div className="col-span-full flex items-center mt-3">
            <h3 className="block leading-6 ">
              {t('app.mine.avatar')}
            </h3>
            <label htmlFor={`add_avatar_button`} className="ml-3 cursor-pointer inline-flex items-center gap-x-3">
              {(editUser?.avatar || avatar) ? <Image className='rounded-full h-10 w-10 object-cover' src={avatar || editUser?.avatar} width={40} height={40} alt={'avatar'} /> : <UserCircleIcon className="h-12 w-12 text-gray-500" aria-hidden="true" />}
              <div className="rounded-md dark:bg-gray-800 px-2 py-1 text-sm shadow-sm dark:hover:bg-gray-700 border border-gray-300 hover:bg-gray-200"
              >Change</div>
              <input hidden type="file" onChange={handleFileChange} id={`add_avatar_button`} name="avatar" accept="image/*"></input>
            </label>
          </div>
          <div>
            <h3 className='mb-3 mt-3'>{t('app.mine.user_role')}
                <Illustrate className="ml-1" title={t('app.common.tips')} desc={t('app.mine.extra_info_desc')} />
                <span className="ml-2 text-xs bg-red-500 text-white rounded px-1.5 py-0.5">{t('app.mine.vip_limit')}</span>
              </h3>
            {(
              <span className="text-sm ">
                ({contentTokens? contentTokens : 0} tokens)
              </span>
            )}
            {errors?.persona_setting && (
              <span className="ml-1 text-xs text-white bg-red-700 px-1 rounded inline-block py-0.5">{errors?.persona_setting?.message}</span>
            )}
            <AutoTextareaHeight
              {...register('persona_setting', {
                validate: {
                  tokenLimit: async (value) => {
                    console.log('tokenLimit', value);
                    if (!value) return true;
                    if (contentTokens > itemTokensLimit) {
                      Toast.notify({
                        type: 'warning',
                        message: t('app.cardEdit.token_limit_exceeded', { limit: itemTokensLimit })
                      })
                      return t('app.cardEdit.token_limit_exceeded', { limit: itemTokensLimit })
                    } else {
                      return true;
                    }
                  },
                }
              })}
              rows={3}
              className={cn(`flex-1 dark:bg-gray-800 ipt`)} placeholder={t('app.mine.extra_info_placeholder')} />
          </div>
        </div>
        <button className='priBtn w-full p-1.5 px-3 bg-purple-500 text-white rounded text-sm' type="submit">{editUser ? t('app.mine.update') : t('app.common.save')}</button>
      </form>
    </div>
  </Modal>, document.body)
}

export default EditUser