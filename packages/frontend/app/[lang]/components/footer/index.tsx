'use client'

import cn from 'classnames'
import ThemeSwitch from '../header/theme/theme-switch'
import { useParams, usePathname } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import Link from 'next/link'
import {web} from '@/app/module/global'

const Footer = (
  {
    cl,
  }: { cl?: string },
) => {
  
  const params = useParams()
  const lang = params.lang as string
  const { t } = useTranslation()
  return (
    <>{web && <div className={cn('hidden md:flex md:ml-[var(--layout-left)] justify-center md:w-content py-4 bottom-4 space-x-2 text-gray-500 font-normal text-xs items-center justify-center ml-0 !m-auto left-0 right-0 w-full', cl)}>
      <div className="">© {t('app.meta.copyright')} {(new Date()).getFullYear()}</div>
      
      <Link href={`/${lang}/about`} prefetch={false}>{t('app.footer.about')}</Link>
        {/* <Link href="https://÷forms.gle/4AYk1F2FWVKMQrTe6" target='_blank'>联系我们</Link> */}
      <Link href={web? `mailto:${process.env.NEXT_PUBLIC_SUPPORT_MAIL}` : "https://t.me/playai666"} target='_blank' prefetch={false}>{t('app.footer.contact')}</Link>
      <Link href={`/${lang}/terms`} prefetch={false}>{t('app.footer.terms')}</Link>
      <Link href={`/${lang}/privacy`} prefetch={false}>{t('app.footer.privacy')}</Link>
      {/* <Link href={`/${lang}/faq`}>FAQ</Link> */}
      {/* <ThemeSwitch></ThemeSwitch> */}
    </div>}
    </>
  )
}

export default Footer
