'use client'
import { useState, useEffect } from 'react'
import { useTheme } from 'next-themes'
import { useTranslation } from 'react-i18next'
import {chromeVersion} from '@little-tavern/shared/src/module/global'
import { Sun, Moon, Monitor } from 'lucide-react'
import React from 'react'

const ThemeSwitch = ({className = ''}: {className?: string}) => {
  const { theme, setTheme } = useTheme()
  const { t } = useTranslation()
  const [isShow, setIsShow] = useState(false)
  
  useEffect(() => {
    const isLowBrowser = chromeVersion > 0 && chromeVersion < 88;
    setIsShow(isLowBrowser? false : true);
    if(isLowBrowser) {
      setTheme('light')
    }
  }, [setTheme])

  const handleThemeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setTheme(e.target.value)
  }

  // Function to get the current theme icon
  const getCurrentThemeIcon = () => {
    switch(theme) {
      case 'system':
        return <Monitor className='w-4 h-4 mr-0.5' />;
      case 'dark':
        return <Moon className='w-4 h-4 mr-0.5' />;
      case 'light':
        return <Sun className='w-4 h-4 mr-0.5' />;
      default:
        return <Monitor className='w-4 h-4 mr-0.5' />;
    }
  };

  return (
    <>
    {isShow && (
      <div className={`${className} flex text-sm items-center py-4 relative text-zinc-500`}>
        {getCurrentThemeIcon()}{t('app.setting.theme')}
        <select 
          className='text-center bg-white dark:bg-black dark:text-white w-full h-full absolute opacity-0 left-0 top-0 cursor-pointer' 
          value={theme} 
          onChange={handleThemeChange}
        >
          <option value="system">{t('app.setting.system')}</option>
          <option value="dark">{t('app.setting.dark')}</option>
          <option value="light">{t('app.setting.light')}</option>
        </select>
      </div>
    )}
    </>
  )
}

export default React.memo(ThemeSwitch)