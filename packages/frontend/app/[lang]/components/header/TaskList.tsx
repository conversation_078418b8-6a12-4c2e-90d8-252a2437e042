'use client'
import type { FC } from 'react'
import React, { useState, useEffect } from 'react'
import Link from 'next/link'
// import LoginInfo from './loginInfo'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import Toast from '@little-tavern/shared/src/ui/toast'
import useSWR from 'swr'
import Modal from '@little-tavern/shared/src/ui/dialog/Modal'
import { useTranslation } from 'react-i18next'

type IProps = {
    onClose?: any
    lang?: string
  }
  
const TaskList: FC<IProps> = ({ onClose, lang }) => {
    const request = useRequest();
    const { data, error, isValidating, mutate } = useSWR('/tasks', request)
    const { t } = useTranslation()
    
    useEffect(() => {
        if(isValidating) {
            Toast.showLoading("Loading");
        } else {
            Toast.hideLoading();
        }
    }, [isValidating])
    const handleClose = () => {
        Toast.hideLoading();
        onClose && onClose();
    }
    const handleReceive = async (taskId: string) => {
        Toast.showLoading('')
        const res = await request('/tasks/receive', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({task_id: taskId})
        })
        if(res.error_code === 0) {
            Toast.notify({
                type: 'success',
                message: res?.data?.tips
            })
        } else {
            Toast.notify({
                type: 'error',
                message: res?.message
            })
        }
        Toast.hideLoading();
        mutate()
    }
    return (
        <>
        {data && <Modal onClose={handleClose} isOpen={true}>
        <div className='min-h-20 mb-4 p-3'>
            <div className=''>
                <h1 className='text-base font-semibold leading-6'>{t('app.index.task')}</h1>
                <ul className='mt-3 space-y-3'>
                {data?.map((task: any) => {
                    return <li key={task.title} className='flex justify-between items-center'>
                    <div className='w-[72%]'>
                        <h3>{task.title}</h3>
                        {task.subTitle && <p className='text-xs text-white'>{task.subTitle}</p>}
                        <p className='text-sm text-gray-500'>{task.desc}</p>
                    </div>
                    {task.status === "TODO" && <Link href={task.linkUrl} className='bg-purple-500 rounded text-sm py-1.5 px-2.5 text-white '>{task.btn}</Link>}
                    {task.status === "UN_RECEIVE" && task.actionType === 'api' && task.taskType === 'DIRECT_RECEIVE' && <button className='bg-purple-500 rounded text-sm py-1.5 px-2.5 text-white' onClick={() => handleReceive(task.taskId)}>{task.btn}</button>}

                    {task.status === "DONE" && (task.linkUrl? <Link href={task.actionType === 'link'? task.linkUrl : `/${lang}/${task.linkUrl}`} className='bg-green-500 rounded text-sm py-1.5 px-2.5 text-white' target={task.actionType === 'link'? '_blank' : '_self'}>{task.btn}</Link> : <span className='bg-gray-500 rounded py-1.5 px-2.5 text-sm text-gray-300'>{task.btnDoneDesc}</span>)}
                    </li>
                })}
                </ul>
            </div>
        </div>
        </Modal>}
        </>
    );
};
export default TaskList