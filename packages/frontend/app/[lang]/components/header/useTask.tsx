'use client'
import classNames from 'classnames'
import type { ReactEventHandler, ReactNode } from 'react'
import React, { useContext, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import TaskList from './TaskList'
import { useParams } from 'next/navigation'

const useTask = () => {
  const params = useParams()
  const lang = params.lang as string
  return {
    show: () => {
      let holder = document.createElement('div')
      holder.className = 'absolute z-50 w-full h-full left-0 top-0'
      document.body.appendChild(holder)
      const root = createRoot(holder)
      return new Promise((resolve, reject) => {
        const onComfirm = () => {
          resolve(true)
          root.unmount();
          document.body.removeChild(holder);
        }
        const onCancel = () => {
          root.unmount();
          document.body.removeChild(holder);
          resolve(false);
        }
        root.render(<TaskList onClose={onCancel} lang={lang} />);
      })
    },
    hide: () => {
      // if (holder) root.unmount();
    }
  }
}

export default useTask
