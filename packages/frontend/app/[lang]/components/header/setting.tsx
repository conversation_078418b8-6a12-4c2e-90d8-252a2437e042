'use client'
import classNames from 'classnames'
import type { ReactEventHandler, ReactNode } from 'react'
import React, { useContext, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import Toast from '@little-tavern/shared/src/ui/toast'
import Modal from '@little-tavern/shared/src/ui/dialog/Modal'
import { useForm, SubmitHandler, useController } from "react-hook-form"
import { AuthContext } from '@little-tavern/shared/src/authContext'
import { useTranslation } from 'react-i18next'
import { Switch } from '@headlessui/react'

export type ISetting = {
  models: any,
  onSelect: ReactEventHandler
}

type FormData = {
  enable_nsfw: boolean
}

const Setting = ({auth, onClose}: any) => {
  const { t } = useTranslation()
  const request = useRequest();
  const user = auth?.user;
  const enable_nsfw = user.enable_nsfw
  const {
    register,
    handleSubmit,
    watch,
    getValues,
    formState: { errors },
  } = useForm<FormData>()
  const onSubmit: SubmitHandler<FormData> = async (data) => {
    const formData = new FormData()
    console.log('data', data);
    formData.append('setting', JSON.stringify(data));
    try{
      const res = await request(`/setting`, {
        method: 'POST',
        body: formData
      });
      // setModelData(res.model || {})
      auth?.updateUserInfo({
        enable_nsfw: res.enable_nsfw
      });
      // Toast.notify({type: 'success', message: t('app.dialog.operate_success')})
    } catch(e) {
      console.error('Upload error:', e);
      Toast.notify({type: 'success', message: t('app.dialog.create_failed')})
    }
  }

  const toggleReg = async () => {
    const nsfw = !enable_nsfw;
    onSubmit({
      enable_nsfw: nsfw
    });
  }
  
  return <Modal isOpen={true} onClose={onClose} conWidth='sm:w-[700px]'>
    <div className='min-h-20 mb-4'>
      <div className="flex min-h-full flex-1 flex-col justify-center pt-4">
      <div className="">
        <form className="" action="#" method="POST" onSubmit={handleSubmit(onSubmit)}>
          <div className=' overflow-auto space-y-6 mb-4 px-1 pt-4 pb-1'>
            <div className='sm:px-5 flex items-center'>
              <label htmlFor="name" className="block flex-1 text-sm font-medium leading-6 ">
              {t('app.setting.enable_nsfw')}
              <p className='text-xs text-gray-500'>{t('app.setting.enable_nsfw_desc')}</p>
              </label>
              <div className="ml-6 w-12">
              <Switch
                    id='nsfw'
                    checked={enable_nsfw}
                    onChange={() => {}}
                    onClick={() => {toggleReg()}}
                    className="group inline-flex h-5 w-10 items-center rounded-full bg-gray-300 dark:bg-gray-500 transition data-[checked]:bg-purple-600 dark:data-[checked]:bg-blue-600 mr-1"
                >
                    <span className="size-4 translate-x-0.5 rounded-full bg-white transition group-data-[checked]:translate-x-5" />
                </Switch>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
    </div>
  </Modal>
}

export default Setting
