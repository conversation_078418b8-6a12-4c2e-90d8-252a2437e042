'use client'
import type { FC } from 'react'
import React, { useState, useEffect, useContext } from 'react'
import cn from 'classnames'
import Link from 'next/link'
import { useTranslation } from 'react-i18next'
import { useParams, useSelectedLayoutSegment } from 'next/navigation'
import classNames from 'classnames'
import { Home, MessageSquare, CreditCard, PenSquare, UserRound } from 'lucide-react'
import s from './style.module.scss'
import { AuthContext } from '@little-tavern/shared/src/authContext'
const commonNavClass = 'w-full flex flex-col items-center justify-center relative rounded cursor-pointer py-2.5 z-10 px-1.5 md:mr-7 text-sm'
const navIconActive = 'stroke-[url(#gradient)] dark:stroke-[url(#gradient-dark)]'

export type INavBottomProps = {
}
// 展示过任务图标，隐藏后不再展示，下次启动小程序再展示
let showPublicTaskIcon = true
const NavBottom: FC<INavBottomProps> = ({
}) => {
  const { t } = useTranslation()
  const params = useParams()
  const lang = params.lang as string
  const selectedSegment = useSelectedLayoutSegment()
  const auth = useContext(AuthContext);
  const publish_task_switch = auth?.user?.publish_task_switch
  // We no longer need to check for isLowBrowser as the CSS handles compatibility
  const navTxtActive = s.navTextGradient
  // 开发环境测试
  // const getGlobalConfig = async () => {
  //   const res: any = await request('/config')
  //   setShowPublicTask(res.publish_task_switch)
  // }
  // useEffect(() => {
  //   process.env.NEXT_PUBLIC_ENV === 'dev' && getGlobalConfig()
  // }, [request])
  return (
    <>
      <svg width="0" height="0" className="absolute z-0 left-[-999px]">
        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#f472b6" /> {/* from-pink-400 */}
          <stop offset="100%" stopColor="#a855f7" /> {/* to-purple-500 */}
        </linearGradient>
        <linearGradient id="gradient-dark" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#f472b6" /> {/* from-pink-400 */}
          <stop offset="100%" stopColor="#ec4899" /> {/* to-pink-500 */}
        </linearGradient>
      </svg>
      <div className="fixed md:hidden z-10 bottom-0 w-full left-0 bg-white dark:bg-[var(--background)] drop-shadow-u dark:drop-shadow-none dark:border-t dark:border-[#2c2e2d] text-[#181818] dark:text-white text-xs pb-[env(safe-area-inset-bottom)]">
        <div className='flex items-center justify-around'>
          <Link href={`/${lang}`} className={classNames(
            commonNavClass
          )}>
            <Home size={20} className={cn(selectedSegment === null && navIconActive)} />
            <span className={cn(selectedSegment === null && navTxtActive, 'mt-0.5')}> {t('app.nav.index')}</span>
          </Link>
          <Link href={`/${lang}/history/recent`} className={classNames(
            commonNavClass
          )}>
            <MessageSquare size={20} className={cn(selectedSegment === 'history' && navIconActive)} />
            <span className={cn(selectedSegment === 'history' && navTxtActive, 'mt-0.5')}>{t('app.nav.chat')}</span>
          </Link>
          <Link href={`/${lang}/pay`} className={classNames(
            commonNavClass
          )}>
            <CreditCard size={20} className={cn(selectedSegment === 'pay' && navIconActive)} />
            <span className={cn(selectedSegment === 'pay' && navTxtActive, 'mt-0.5')}>{t('app.nav.pay')}</span>
          </Link>
          <Link href={`/${lang}/create/myroles`} className={classNames(
            commonNavClass
          )} onClick={() => {showPublicTaskIcon = false}}>
            {publish_task_switch && showPublicTaskIcon && <div className='absolute top-0.5 mx-auto right-[-40px] left-0 text-xs px-1 bg-red-500 rounded-full text-white w-fit'>{t('app.gift')}</div>}
            <PenSquare size={20} className={cn(selectedSegment === 'create' && navIconActive)} />
            <span className={cn(selectedSegment === 'create' && navTxtActive, 'mt-0.5')}>{t('app.nav.create')}</span>
          </Link>
          <Link href={`/${lang}/mine/gift/free`} className={classNames(
            commonNavClass
          )}>
            <UserRound size={20} className={cn(selectedSegment === 'mine' && navIconActive)} />
            <span className={cn(selectedSegment === 'mine' && navTxtActive, 'mt-0.5')}>{t('app.nav.mine')}</span>
          </Link>
        </div>
      </div>
    </>
  )
}

export default NavBottom
