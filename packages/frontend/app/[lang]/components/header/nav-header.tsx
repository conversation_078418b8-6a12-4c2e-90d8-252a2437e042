import { useParams, useSelectedLayoutSegment } from 'next/navigation'
import React, { useState, useEffect } from 'react'
import classNames from 'classnames'
import s from './style.module.scss'
import AppIcon from './app-icon'
import Link from 'next/link'
import { useTranslation } from 'react-i18next'
import { web } from '@/app/module/global'
import Image from 'next/image'
import u from './app-icon/u.png'
import { useTheme } from 'next-themes'

const commonNavClass = 'flex items-center relative rounded h-8 font-medium text-base cursor-pointer font-medium h-[43px] z-10 px-5'
const NavHeader = ({ lang }: any) => {
  const selectedSegment = useSelectedLayoutSegment()
  const { t } = useTranslation()
  const { resolvedTheme } = useTheme()
  const [mount, setMount] = useState(false)

  useEffect(() => {
    setMount(true)
  }, [])

  return <div className='flex items-center'>
    {
      web ? <Link className='md:ml-4 ml-2 items-center flex' href={`/${lang}`} target='_self' prefetch={false}>
        <div className='flex items-center'>
          {/* <span className={`${s.navTextGradient} font-delius-swash-caps text-2xl mr-[1px]`}>U</span> */}
          <Image src={u} width={18} alt='logo' className='mr-[2px]' />
          <span className='font-crimson-text text-2xl'>honey</span>
        </div>
         </Link> : <Link className='md:ml-4 ml-2 items-center flex' href={web ? `/${lang}` : `https://t.me/playai666`} target={web ? '_self' : '_blank'} prefetch={false}>
        <AppIcon />
        <div className={classNames(
          "ml-0.5 mt-0.5 text-sm md:text-base py-2 md:p-0.5 md:py-1.5 font-bold",
          s.gradientText
        )}>{t('app.common.title')}</div>
      </Link>
    }


    <div className='hidden md:flex items-center ml-1.5 sm:ml-7'>
      <Link href={`/${lang}`} className={classNames(
        commonNavClass,
        selectedSegment == null ? s.navActive : s.navUnActive,
      )}>
        {t('app.nav.index')}
      </Link>
      <Link href={`/${lang}/history/recent`} className={classNames(
        commonNavClass,
        selectedSegment === 'history' ? s.navActive : s.navUnActive,
      )}>
        {t('app.nav.chat')}
      </Link>
      <Link href={`/${lang}/pay`} className={classNames(
        commonNavClass,
        selectedSegment === 'pay' ? s.navActive : s.navUnActive,
      )}>
        {t('app.nav.pay')}
      </Link>
      <Link href={`/${lang}/create/myroles`} className={classNames(
        commonNavClass,
        selectedSegment === 'create' ? s.navActive : s.navUnActive,
      )}>
        {t('app.nav.create')}
      </Link>
      <Link href={`/${lang}/mine/share`} className={classNames(
        commonNavClass,
        selectedSegment === 'mine' ? s.navActive : s.navUnActive,
      )}>
        {t('app.nav.mine')}
      </Link>
      {web && <Link href={`/${lang}/about`} className={classNames(
        commonNavClass,
        selectedSegment === 'about' ? s.navActive : s.navUnActive,
      )}>
        {t('app.footer.about')}
      </Link>}
    </div>
  </div>
}

export default React.memo(NavHeader)