'use client'

import TaskList from './TaskList'
import { useTranslation } from 'react-i18next'
import React, { useContext, Suspense, useState, useEffect } from 'react'
import useTask from './useTask'
import useRequest from '@share/src/hook/useRequest'
import Toast from '@share/src/ui/toast'
import {web, tgWeb} from '@/app/module/global'

const Gift = () => {
    const { t, i18n } = useTranslation()
    const task = useTask();
    const request = useRequest()

    const handleTask = async () => {
        if(web) {
            const res = await request('/tasks/check_in', {
                method: 'POST'
            })
            if(res.error_code === 0) {
                Toast.notify({
                    type: 'success',
                    message: res.message
                })
            } else {
                Toast.notify({
                    type: 'error',
                    message: res.message
                })
            }
        } else {
            task.show()
        }
    }

    return <>
    {!tgWeb && <button className='mr-1.5 items-center flex text-xs rounded-full bg-pink-600 px-2 py-1 sm:p-3 sm:py-1 sm:ml-1.5 sm:mr-1.5 text-white relative z-20' onClick={handleTask}>
    {web? t('app.nav.checkin_gift') : t('app.nav.claim_gift')}
    <span className='animate-wiggle-more animate-thrice ml-0.5'>💎</span>
    </button>}
    </>
};

export default Gift;