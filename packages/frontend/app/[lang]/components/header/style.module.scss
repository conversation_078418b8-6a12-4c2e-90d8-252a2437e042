
.navActive {
    @apply after:content-[""] after:absolute after:block after:bottom-0 after:w-[calc(100%_-_40px)] after:border-b-4 after:border-purple-600 
}
.navUnActive {
    color: rgb(var(--sec-rgb));
    font-weight: normal;
}
.navUnActive:hover {
    @apply after:content-[""] after:absolute after:block after:bottom-0 after:w-[calc(100%_-_40px)] after:border-b-4 after:border-purple-600 after:origin-center after:animate-scaleIn
}

@keyframes scaleIn {
    0% {
        transform: scaleX(0);
    }
    100% {
        transform: scaleX(1);
    }
}

.animate-scaleIn {
    animation: scaleIn 0.3s ease-out forwards;
}

:global(.dark) .navUnActive:hover {
    --hover-bg: rgb(255 255 255 / 19%);
}

/* 渐变文本效果带降级兼容 */
.gradientText, .navTextGradient {
    /* 默认使用 background-clip: text */
    background-image: linear-gradient(to right, #f472b6, #a855f7);
    color: transparent;
    -webkit-background-clip: text;
    background-clip: text;
    
    /* 为不支持 background-clip: text 的浏览器提供降级方案 */
    @supports not (background-clip: text) {
        background-image: none;
        color: #ec4899; /* 粉色文本作为降级方案 */
    }
    
    /* 黑暗模式下的颜色调整 */
    :global(.dark) & {
        background-image: linear-gradient(to right, #f472b6, #ec4899);
        
        @supports not (background-clip: text) {
            background-image: none;
            color: #f472b6;
        }
    }
}

/* 导航项文本样式，增加font-medium */
.navTextGradient {
    font-weight: 500; /* font-medium */
}