import { apiHost } from "../global";
export const dynamic = 'force-dynamic' // defaults to auto
// https://developer.mozilla.org/docs/Web/API/ReadableStream#convert_async_iterator_to_stream
function iteratorToStream(iterator: any) {
  return new ReadableStream({
    async pull(controller) {
      const { value, done } = await iterator.next()
 
      if (done) {
        controller.close()
      } else {
        controller.enqueue(value)
      }
    },
  })
}
 
function sleep(time: number) {
  return new Promise((resolve) => {
    setTimeout(resolve, time)
  })
}
 
const encoder = new TextEncoder()

const data = {content: "你好啊，哈哈哈"}
 
async function* makeIterator() {
  yield encoder.encode(`event: data\r\ndata: 你\r\n\r\n

`)
  await sleep(500)
  yield encoder.encode(`event: data\r\ndata: 好\r\n\r\n

`)
  await sleep(500)
  yield encoder.encode(`event: data\r\ndata: 啊\r\n\r\n

`)
}
 
export async function POST() {
  const iterator = makeIterator()
  const stream = iteratorToStream(iterator)
 
  return new Response(stream)
}