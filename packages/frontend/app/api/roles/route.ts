import { apiHost } from "../global";
export const dynamic = 'force-dynamic' // defaults to auto
export async function GET(request: Request, res: Response) {
  try {
    console.log(`${apiHost}/roles`);
    let tgData = request.headers.get("Tgdata") || 'b0';
    let tgId = request.headers.get("TgId") || 'youzi';
    console.log({
      Tgdata: tgData,
      TgId: tgId,
    });
    const response = await fetch(`${apiHost}/roles`, {
      cache: 'no-store',
      headers: {
        Tgdata: tgData,
        TgId: tgId,
      },
    });
    const roles = await response.json();
    console.log(roles);
    return Response.json({ roles });
  } catch (error) {
    return Response.json({ error: 'Failed to fetch role' });
  }
}
