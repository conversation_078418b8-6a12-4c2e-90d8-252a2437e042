import { resolve } from "path";
import { apiHost } from "../global";
import { NextRequest } from "next/server";
export const dynamic = 'force-dynamic' // defaults to auto
export async function GET(request: NextRequest, res: Response) {
  try {
    const msgId = new URLSearchParams(request.nextUrl.search).get('msgId');
    await sleep(1500)
    console.log(msgId);
    return Response.json({src: 'http9037dd'});
  } catch (error) {
    return Response.json({ error: 'Failed to fetch role' });
  }
}

function sleep(time: number) {
  return new Promise((resolve) => {
    setTimeout(resolve, time)
  })
}