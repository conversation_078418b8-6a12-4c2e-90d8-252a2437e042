import { apiHost } from "../global";
export const dynamic = 'force-dynamic' // defaults to auto
export async function GET(request: Request, res: Response) {
    let tgData = request.headers.get("Tgdata") || ', ';
    let tgId = request.headers.get("TgId") || 'youzi';
    console.log({
      Tgdata: tgData,
      TgId: tgId,
    });
    const role = request.headers.get("role") || "uzi";
    const response = await fetch(`${apiHost}/history?role=${role}`, {
      method: "GET",
      headers: {
        Tgdata: tgData,
        TgId: tgId,
      },
    });
    const history = await response.json();
    console.log(history, `length: ${history.length}`);
    const chatList = history.map((h: any) => ({
      isUser: h.type == "human",
      avatar: h.avatar,
      content: h.content,
      voiceUrl: h.voice_url,
      date: new Date(h.timestamp * 1000).toLocaleString(),
    }));

    return Response.json(chatList);
}