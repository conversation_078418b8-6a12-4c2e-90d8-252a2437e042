import { apiHost } from "../global";
import { type NextRequest, NextResponse } from 'next/server'
const extract = require('png-chunks-extract');
const PNGtext = require('png-chunk-text');
const ExifReader = require('exifreader');
const sharp = require('sharp');
// sharp.cache(false);
const utf8Decode = new TextDecoder('utf-8', { ignoreBOM: true });

export const dynamic = 'force-dynamic' // defaults to auto
export async function POST(request: NextRequest, res: Response) {
  try {
    const formData = await request.formData();
    const file = formData.get('image');
    const format = <string>formData.get('file_type')

    if (!file || !(file instanceof File)) {
      return NextResponse.json({ error: 'No image file provided' }, { status: 400 });
    }
    console.log('file, format', file, format);
    const buffer = Buffer.from(await file.arrayBuffer());
    // 在这里你可以进一步处理图片文件数据
    // 例如保存到文件系统或上传到云存储服务
    var img_data = await charaRead(buffer, format);
    const imgData = JSON.parse(img_data);
    let cardData;
    if(imgData.spec !== undefined) {
      console.log('importing from v2 json');
      cardData = imgData.data
    } else {
      console.log('importing from v1 json');
      cardData = imgData
    }
    let firstMsg = '';
    // 兼容类脑社区卡片
    if(cardData.alternate_greetings && cardData.alternate_greetings[0]) {
      firstMsg = cardData.alternate_greetings[0];
    } else {
      firstMsg = cardData.first_mes
    }
    const outputBuffer = await sharp(buffer).jpeg({ quality: 80 }).toBuffer();

    // 将 Buffer 转换为 Blob
    const blob = new Blob([outputBuffer], { type: 'image/jpeg' });
    console.log(blob);
    const avatar = outputBuffer.toString('base64');
    // console.log(`token=${request.cookies.get('token')?.value}`, avatar);
    return NextResponse.json({
      role_name: cardData.name,
      card_name: cardData.name,
      introduction: '',
      description: cardData.description,
      first_message: firstMsg,
      personality: cardData.personality,
      example_dialog: cardData.mes_example,
      scenario: cardData.scenario,
      img_array_64: avatar
    });
  } catch (error) {
    console.error('Error uploading image:', error);
    return NextResponse.json({ error: 'Error uploading image' }, { status: 500 });
  }
}

async function charaRead(buffer: Buffer, format: string){
  // sharp.cache(false);
  switch(format){
      case 'webp':
      try {
          // sharp.cache(false);
          let char_data;
          const exif_data = await ExifReader.load(buffer);
          if (exif_data['UserComment']['description']) {
              let description = exif_data['UserComment']['description'];
              try {
                  JSON.parse(description);
                  char_data = description;
              } catch {
                  const byteArr = description.split(",").map(Number);
                  const uint8Array = new Uint8Array(byteArr);
                  const char_data_string = utf8Decode.decode(uint8Array);
                  char_data = char_data_string;
              }
          } else {
              console.log('No description found in EXIF data.');
              return false;
          }
          return char_data;
      } catch (err) {
          console.log(err);
          return false;
      }
      case 'image/png':
          const chunks = extract(buffer);
          const textChunks = chunks.filter(function(chunk: any) {
              return chunk.name === 'tEXt';
          }).map(function (chunk: any) {
              return PNGtext.decode(chunk.data);
          });
          var base64DecodedData = Buffer.from(textChunks[0].text, 'base64').toString('utf8');
          return base64DecodedData;
      default:
          break;
  }
}