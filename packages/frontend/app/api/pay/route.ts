import { apiHost } from "../global";
export const dynamic = 'force-dynamic' // defaults to auto
export async function GET(request: Request, res: Response) {
  try {
    console.log(`${apiHost}/roles`);
    let tgData = request.headers.get("Tgdata") || ', 0';
    let tgId = request.headers.get("TgId") || 'uzi';
    console.log({
      Tgdata: tgData,
      TgId: tgId,
    });
    const response = await fetch(`${apiHost}/roles`, {
      cache: 'no-store',
      headers: {
        Tgdata: tgData,
        TgId: tgId,
      },
    });
    const roles = await response.json();
    console.log(roles);
    return Response.json({ 
      balance: 9999,
      payDesc: '钻石可以在所有角色共用，每个轮消耗100钻石',
      inventorys: [
        {
          id: 1,
          title: '6000钻石',
          price: '$3.99'
        },
        {
          id: 2,
          title: '6000钻石',
          price: '$3.99'
        },
        {
          id: 3,
          title: '6000钻石',
          price: '$3.99'
        },
        {
          id: 4,
          title: '6000钻石',
          price: '$3.99'
        },
        {
          id: 5,
          title: '6000钻石',
          price: '$3.99'
        }
      ]
     });
  } catch (error) {
    return Response.json({ error: 'Failed to fetch role' });
  }
}
