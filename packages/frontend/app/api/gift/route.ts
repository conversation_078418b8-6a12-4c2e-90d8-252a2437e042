import { resolve } from "path";
import { apiHost } from "../global";
import { NextRequest } from "next/server";
export const dynamic = 'force-dynamic' // defaults to auto
export async function GET(request: NextRequest, res: Response) {
  try {
    return Response.json({
      isGift: true,
      title: '新用户-限时礼包',
      desc: '内测期间，新用户免费领取3天会员礼包（10w钻石）'
    });
  } catch (error) {
    return Response.json({ error: 'Failed to fetch role' });
  }
}

function sleep(time: number) {
  return new Promise((resolve) => {
    setTimeout(resolve, time)
  })
}