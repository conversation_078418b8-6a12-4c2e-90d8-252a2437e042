import { apiHost } from "../global";
export const dynamic = 'force-dynamic' // defaults to auto
export async function GET(request: Request, res: Response) {
  try {
    console.log(`${apiHost}/roles`);
    let tgData = request.headers.get("Tgdata") || '0';
    let tgId = request.headers.get("TgId") || 'youzi';
    console.log({
      Tgdata: tgData,
      TgId: tgId,
    });
    const response = await fetch(`${apiHost}/roles`, {
      cache: 'no-store',
      headers: {
        Tgdata: tgData,
        TgId: tgId,
      },
    });
    const roles = await response.json();
    console.log(roles);
    return Response.json({ 
      userRoles: [
          {
            id: 6,
          "role": "Alice",
          "name": "Alice",
          "imgSrc": "h16b05fe42e1fa1bf1ab64a4171379d3",
          "desc": "取精液",
          "tags": "热门",
          "link": "ice"
          }
      ]
     });
  } catch (error) {
    return Response.json({ error: 'Failed to fetch role' });
  }
}
