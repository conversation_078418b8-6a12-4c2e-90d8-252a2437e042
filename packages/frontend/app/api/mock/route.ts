import { resolve } from "path";
import { apiHost } from "../global";
import { NextRequest } from "next/server";
export const dynamic = 'force-dynamic' // defaults to auto
export async function GET(request: NextRequest, res: Response) {
  try {
    const msgId = new URLSearchParams(request.nextUrl.search).get('msgId');
    await sleep(3000)
    console.log('msgId:');
    return Response.json({
      "title": "群聊测试2",
      "current_tag": "精选",
      "current_sub_tag": ""
  });
  } catch (error) {
    return Response.json({ error: 'Failed to fetch role' });
  }
}

function sleep(time: number) {
  return new Promise((resolve) => {
    setTimeout(resolve, time)
  })
}