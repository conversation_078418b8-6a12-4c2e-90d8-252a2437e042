import { apiHost } from "../global";
import { NextResponse } from 'next/server';
const extract = require('png-chunks-extract');
const PNGtext = require('png-chunk-text');
const ExifReader = require('exifreader');
// const sharp = require('sharp');
// sharp.cache(false);
const utf8Encode = new TextEncoder();
const utf8Decode = new TextDecoder('utf-8', { ignoreBOM: true });

export const dynamic = 'force-dynamic' // defaults to auto
export async function POST(request: Request, res: Response) {
  try {
    const json = await request.json();
    console.log('json', json);
    return NextResponse.json(json,{headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },});
  } catch (error) {
    console.error('Error uploading image:', error);
    return NextResponse.json({ error: 'Error uploading image' }, { status: 500 });
  }
}

export async function OPTIONS(request: Request) {
  const response = NextResponse.json({}, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
  return response;
}