import { useParams, usePathname, useRouter } from "next/navigation";
// cid:conversation_id
const geenerateUrl = ({isGroup, id, cid, pathname, lang }: any) => {
    const searchStr = window.location.search
    const _cid = cid? cid : '';
    // 由于chat页面有切换语言选项，切换后，需要更新来源url，所以增加from
    const fromUrl = encodeURIComponent(`${pathname}${searchStr}`)
    const url = isGroup? `/${lang}/chat?groupid=${id}&from=${fromUrl}&cid=${_cid}` : `/${lang}/chat?roleid=${id}&from=${fromUrl}&cid=${_cid}`
    return url
}

const useLinkToChat = () => {
    const pathname = usePathname();
    const router = useRouter();
    const params = useParams()
    const lang = params.lang as string
    return {
        push: ({isGroup, id, cid }: any) => {
            router.push(geenerateUrl({isGroup, id, cid, pathname, lang }))
        },
        prefetch: ({isGroup, id, cid }: any) => {
            router.prefetch(geenerateUrl({isGroup, id, cid, pathname, lang }))
        }
    }
}

export default useLinkToChat