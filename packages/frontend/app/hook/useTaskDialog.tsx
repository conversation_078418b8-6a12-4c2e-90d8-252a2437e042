'use client'
import classNames from 'classnames'
import type { ReactEventHandler, ReactNode } from 'react'
import React, { useContext, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import type { FC } from 'react'
import Modal from '@little-tavern/shared/src/ui/dialog/Modal'
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { CheckIcon } from '@heroicons/react/24/outline'
import cn from 'classnames';
import Link from 'next/link'
import s from '../[lang]/globals.module.css'
import { useTranslation } from 'react-i18next'

const useTaskDialog = () => {
  const { t } = useTranslation()
  return {
    show: ({taskList}: any) => {
      let holder = document.createElement('div')
      holder.className = 'absolute z-50 w-full h-full left-0 top-0'
      document.body.appendChild(holder)
      const root = createRoot(holder)
      return new Promise((resolve, reject) => {
        const onComfirm = () => {
          resolve(true)
          root.unmount();
          document.body.removeChild(holder);
        }
        const onCancel = () => {
          root.unmount();
          document.body.removeChild(holder);
          resolve(false);
        }
        root.render(<Tasklist onClose={onComfirm} isOpen={true} taskList={taskList} />);
      })
    },
    hide: () => {
      // if (holder) root.unmount();
    }
  }
}

export default useTaskDialog

type IProps = {
  onClose?: React.MouseEventHandler,
  isOpen: boolean,
  taskList: any[]
}

const Tasklist: FC<IProps> = ({ onClose, isOpen, taskList}) => {
  const { t } = useTranslation()
  return (
    <Modal onClose={onClose} isOpen={isOpen}>
      <div className='min-h-20 mb-4 p-3'>
        <div className=''>
            <h1 className='text-base font-semibold leading-6'>{t('app.index.task')}</h1>
            <ul className='mt-3 space-y-3'>
              {taskList.map((task) => {
                return <li key={task.title} className='flex justify-between items-center'>
                  <div className='w-[72%]'>
                    <h3>{task.title}</h3>
                    <p className='text-sm text-gray-500'>{task.desc}</p>
                  </div>
                  {task.status === "TODO"? <Link href={task.linkUrl} className='bg-purple-500 rounded text-sm py-1.5 px-2.5'>{task.btn}</Link> : 
                  <span className='bg-gray-500 rounded py-1.5 px-2.5 text-sm text-gray-300'>{task.btnDoneDesc}</span>}
                </li>
              })}
            </ul>
        </div>
      </div>
    </Modal>
  );
};