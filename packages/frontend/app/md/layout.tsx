import type { Viewport } from 'next'
import { getTranslation } from "@little-tavern/shared/src/i18n/index";
import "../[lang]/globals.css";
import '../[lang]/markdown.scss'
import { ThemeProvider } from 'next-themes'

export async function generateMetadata({ params: { lang } }: {
  params: {
    lang: string;
  };
}) {
  const { t } = await getTranslation(lang, "app");
  return {
    title: t('meta.title'),
    description: t('meta.desc'),
  }
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
}

export default async function RootLayout({
  children,
  params: {
    lang,
  },
}: Readonly<{
  children: React.ReactNode
  params: any
}>) {
  return (
    <html lang={lang} suppressHydrationWarning>
      <body>
        <ThemeProvider attribute="class" defaultTheme={'dark'}>
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}