'use client'

import { useParams } from 'next/navigation'
import React, { useCallback, useContext, useEffect, useState, Suspense } from 'react'
import { useTranslation } from 'react-i18next'
import { Markdown } from '../[lang]/components/markdown'

const sampleTxt = `这是普通文字(括号内的内容会高亮显示），并且内容中的“双引号也会高亮显示”

# 1号标题
## 2号标题
### 3号标题

_斜体文本_
**粗体文本**
_斜体文本**结合**粗体使用_

分割线，3个星号单独一行为分割线
***

删除线，两边波浪符号包围会出现删除线
~这是删除线示例~

## 状态栏，也叫代码块
注意，状态栏内编辑不需要\`\`\`包裹内容
\`\`\`
小明的状态
心情: 羞耻，害怕，焦虑
好感: 33
地点: 班级教室
\`\`\`

## 引用
这是引用,可以多行引用,可以用来写优美的诗歌

> 轻轻的我走了
> 正如我轻轻的来
> 我轻轻的招手作别西天的云彩

## 这是表格

| 物品名字 | 价格 |  库存 |
| ------- |:------:|:------:|
| 丝袜      | 12     |   12
| 短裤      | 8     |    12
| 短裙      | 15     |   12

## 无序列表

* 第一点
* 第二点
* 第三点

## 有序列表

1. 第一点
2. 第二点
3. 第三点

`
const Main = () => {
  const { t } = useTranslation()
  const params = useParams()
  const [markdownText, setMarkdownText] = useState(sampleTxt)
  
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMarkdownText(e.target.value)
  }

  return (
    <div className='max-w-[1200px] mx-auto'>
      <p className='mt-1 p-2 text-base '>Markdown 语法示例，支持实时调试</p>
      <div className="flex p-1 h-[calc(100vh-4rem)] prose dark:prose-invert">
      <div className="flex-1 !max-w-[100%] !ml-auto !mr-auto">
        <textarea
          className="w-full h-full py-4 px-1 font-mono text-sm  resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
          value={markdownText}
          onChange={handleTextChange}
          placeholder="Enter your Markdown text here..."
        />
      </div>
      <div className="flex-1 overflow-auto  py-4 px-1 dark:bg-gray-900 bg-white !max-w-[100%] !ml-auto !mr-auto">
        <Markdown content={markdownText} />
      </div>
    </div>
    </div>
  )
}


export default Main
