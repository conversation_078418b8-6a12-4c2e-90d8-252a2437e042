"use client";

import * as Sen<PERSON> from "@sentry/nextjs";
import NextError from "next/error";
import { useEffect } from "react";

export default function GlobalError({
  error,
}: {
  error: Error & { digest?: string };
}) {
  useEffect(() => {
    console.log('error', error);
    Sentry.captureException(error);
  }, [error]);

  return (
    <html>
      <body>
        <NextError title={error.message} statusCode={0} />
      </body>
    </html>
  );
}
