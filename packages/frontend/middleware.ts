import { match } from '@formatjs/intl-localematcher'
import Negotiator from 'negotiator'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import type { Locale } from '@little-tavern/shared/src/i18n/settings'
import { LOCALE_COOKIE_NAME, fallbackLng as defaultLocale, switchLanguages as locales } from '@little-tavern/shared/src/i18n/settings'

export const getLocale = (request: NextRequest): Locale => {
  let languages: string[] | undefined
  // get locale from cookie
  const localeCookie = request.cookies.get(LOCALE_COOKIE_NAME)
  languages = localeCookie?.value ? [localeCookie.value] : []

  if (!languages.length) {
    // Negotiator expects plain object so we need to transform headers
    const negotiatorHeaders: Record<string, string> = {}
    request.headers.forEach((value, key) => (negotiatorHeaders[key] = value))
    // Use negotiator and intl-localematcher to get best locale
    languages = new Negotiator({ headers: negotiatorHeaders }).languages()
  }

  // match locale
  let matchedLocale: Locale = defaultLocale
  try {
    // If languages is ['*'], Error would happen in match function.
    matchedLocale = match(languages, locales, defaultLocale) as Locale
  }
  catch (e) {}
  return matchedLocale
}

export const middleware = async (request: NextRequest) => {
  console.log('middleware')
  let pathname = request.nextUrl.pathname
  // 斜杠会导致多一次重定向，删除
  pathname === '/' && (pathname = '')
  if (/\.(css|js(on)?|ico|svg|png)$/.test(pathname))
    return

  // Check if there is any supported locale in the pathname
  const pathnameIsMissingLocale = locales.every(
    locale => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`,
  )

  // Redirect if there is no locale
  if (pathnameIsMissingLocale) {
    const locale = getLocale(request)
    const queryString = request.nextUrl.search
    const newUrl = new URL(`/${locale}${pathname}${queryString}`, request.url)
    // e.g. incoming request is /products
    // The new URL is now /en-US/products
    return NextResponse.redirect(newUrl)
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    `/((?!api|_next/static|_next/image|assets|favicon.ico|sw.js|zh|zh\-TW|en|telegram-web-app.js|dot.png|exchage_guide.png|gift2.png|md).*)`,
  ],
}
