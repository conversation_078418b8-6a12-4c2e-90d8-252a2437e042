import * as Sentry from "@sentry/nextjs";

// if (process.env.NODE_ENV === 'production') {
if (true) {
  Sentry.init({
    dsn: "https://<EMAIL>:8443/5",
    // Replay may only be enabled for the client-side
    integrations: [Sentry.replayIntegration()],
    // Set tracesSampleRate to 1.0 to capture 100%
    // of transactions for tracing.
    // We recommend adjusting this value in production
    tracesSampleRate: 0,
    // Capture Replay for 10% of all sessions,
    // plus for 100% of sessions with an error
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,
    autoSessionTracking: false, // 关闭自动会话跟踪
    ignoreErrors: [
      // 添加要忽略的错误消息
      /debounce自动取消/,
      /no supported source/,
      /Method not found/,
    ],
    beforeSend(event) {
      if (event.exception && event.exception.values) {
        const errorType = event.exception.values[0].type;
        const errorMessage = event.exception.values[0].value;
        // 过滤掉 'NotAllowedError: play() can only be initiated by a user gesture'
        if (errorMessage.includes('play()') || errorMessage.includes('pause') || errorMessage.includes('aborted')) {
          return null;
        }
        // 过滤掉 'NotAllowedError: The request is not allowed by the user agent or the platform in the current context...'
        if (errorMessage.includes('not allowed by the user agent')) {
          return null;
        }
        // 过滤掉 'TypeError: undefined is not an object (evaluating 'window.TelegramGameProxy.receiveEvent')'
        if (errorMessage.includes("window.TelegramGameProxy")) {
          return null;
        }
      }
      return event;  // 正常上报其他错误
    },
  });
}
