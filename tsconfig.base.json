{
  "compilerOptions": {
    "baseUrl": "./",
    "paths": {
      "@backend/*": ["packages/backend/*"], // 配置跨项目路径
      "@/*": ["packages/frontend/*"], // 配置跨项目路径
      "@share/*": ["packages/shared/*"]
    },
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
  },
}
