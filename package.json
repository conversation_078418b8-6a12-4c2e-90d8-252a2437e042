{"name": "little-tavern", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev:frontend": "pnpm --filter @little-tavern/frontend dev", "dev:backend": "pnpm --filter @little-tavern/backend dev", "build:frontend": "pnpm --filter @little-tavern/frontend build", "build:backend": "pnpm --filter @little-tavern/backend build"}, "keywords": [], "author": "", "dependencies": {"@dnd-kit/core": "^6.2.0", "@dnd-kit/sortable": "^9.0.0", "@dnd-kit/utilities": "^3.2.2", "@formatjs/intl-localematcher": "^0.5.4", "@headlessui/react": "^2.1.1", "@heroicons/react": "^2.1.3", "@sentry/nextjs": "^8.12.0", "@types/dompurify": "^3.0.5", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.6", "@types/lodash.debounce": "^4.0.9", "@types/react-syntax-highlighter": "^15.5.13", "accept-language": "^3.0.18", "base64url": "^3.0.1", "classnames": "^2.5.1", "cookies-next": "^4.1.1", "dompurify": "^3.1.1", "exifreader": "^4.9.2", "firebase": "^11.6.0", "i18next": "^23.11.2", "i18next-browser-languagedetector": "^7.2.1", "i18next-resources-to-backend": "^1.2.1", "immer": "9.0.21", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lucide-react": "^0.371.0", "negotiator": "^0.6.3", "next": "14.2.15", "next-themes": "^0.3.0", "png-chunk-text": "^1.0.0", "png-chunks-encode": "^1.0.0", "png-chunks-extract": "^1.0.0", "qrcode.react": "^3.1.0", "react": "^18", "react-dom": "^18", "react-easy-crop": "^5.0.6", "react-hook-form": "^7.51.3", "react-i18next": "^14.1.0", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^9.0.1", "react-smooth-dnd": "^0.11.1", "react-syntax-highlighter": "^15.5.0", "react-tooltip": "^5.26.3", "recyclerlistview": "^4.2.0", "rehype-katex": "^7.0.0", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "sass": "^1.75.0", "sharp": "^0.33.3", "swr": "^2.2.5", "tailwindcss-animated": "^1.1.2", "use-immer": "^0.9.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.13", "@types/negotiator": "^0.6.3", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.1", "postcss": "^8", "tailwindcss": "^3.4.10", "typescript": "^5"}, "license": "ISC"}